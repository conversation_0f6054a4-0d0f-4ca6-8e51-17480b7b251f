#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
from typing import Optional

class DeviceIdUtils:
    """设备ID处理工具类，提供设备ID处理和MAC地址生成功能"""
    
    @staticmethod
    def normalize_device_id(device_id: str) -> str:
        """
        标准化设备ID格式，确保格式一致性
        
        :param device_id: 原始设备ID
        :return: 标准化后的设备ID
        """
        # 移除可能存在的前缀
        if device_id.startswith("ip:"):
            device_id = device_id[3:]
        
        return device_id
    
    @staticmethod
    def generate_mac_for_device(device_id: str, index: int, prefix: Optional[str] = None) -> str:
        """
        为设备生成唯一且固定的MAC地址
        
        :param device_id: 设备ID或标识符
        :param index: 索引值，用于生成多个不同的MAC地址
        :param prefix: MAC地址前缀，默认为None，会使用生成的值
        :return: 格式化的MAC地址字符串
        """
        # 使用设备ID和索引的组合作为种子生成哈希
        seed = f"{device_id}:{index}"
        hash_obj = hashlib.md5(seed.encode())
        hash_hex = hash_obj.hexdigest()
        
        # 如果没有指定前缀，使用哈希的前两个字节作为前缀
        if prefix is None:
            # 使用哈希的前两个字节，但确保是有效的MAC地址
            # 将第一个字节的第二个低位置为0(保证是全局唯一地址)
            byte1 = int(hash_hex[0:2], 16) & 0xfe  # 确保地址是全局的(U/L位为0)
            prefix = f"{byte1:02x}{hash_hex[2:4]}"
        
        # 使用哈希的剩余部分生成MAC地址的其余部分
        mac_parts = [
            prefix[0:2],
            prefix[2:4],
            hash_hex[4:6],
            hash_hex[6:8],
            hash_hex[8:10],
            hash_hex[10:12]
        ]
        
        # 格式化为标准MAC地址格式
        return ":".join(mac_parts).lower()
    
    @staticmethod
    def is_valid_mac(mac: str) -> bool:
        """
        检查MAC地址是否有效
        
        :param mac: MAC地址字符串
        :return: 是否有效
        """
        # 移除可能的分隔符
        mac = mac.replace(":", "").replace("-", "").replace(".", "").lower()
        
        # 检查长度和字符
        if len(mac) != 12:
            return False
        
        for char in mac:
            if char not in "0123456789abcdef":
                return False
        
        return True
    
    @staticmethod
    def format_mac(mac: str, separator: str = ":") -> str:
        """
        格式化MAC地址
        
        :param mac: 原始MAC地址字符串
        :param separator: 分隔符，默认为":"
        :return: 格式化后的MAC地址
        """
        # 移除可能的分隔符
        mac = mac.replace(":", "").replace("-", "").replace(".", "").lower()
        
        # 如果长度不正确，返回原始值
        if len(mac) != 12:
            return mac
        
        # 格式化为标准格式
        return separator.join([mac[i:i+2] for i in range(0, 12, 2)]) 