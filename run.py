#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import create_app, db, socketio, SOCKETIO_AVAILABLE
import os

app = create_app()

# 在应用程序启动时创建数据库表
with app.app_context():
    db.create_all()
    print("\n数据库表创建成功\n")

if __name__ == '__main__':
    # 确保SSL文件夹存在
    if not os.path.exists('ssl'):
        os.makedirs('ssl')

    # 确保frontend文件夹存在
    if not os.path.exists('frontend'):
        os.makedirs('frontend')

    # 运行应用
    if SOCKETIO_AVAILABLE:
        print("启动WebSocket服务器...")
        socketio.run(app, host='0.0.0.0', port=5000, debug=True)
    else:
        print("启动普通Flask服务器（WebSocket功能不可用）...")
        app.run(host='0.0.0.0', port=5000, debug=True)