body {
    font-family: -apple-system, BlinkMacSystemF<PERSON>, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    background-color: #f0f2f5;
    color: #333;
}

.sidebar {
    width: 280px;
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #34495e;
}

.sidebar-header {
    padding: 10px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #34495e;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.4em;
    color: #ecf0f1;
}

.sidebar-new-task-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 1em;
    margin-bottom: 15px;
    cursor: pointer;
    border-radius: 4px;
    width: 100%;
}

.sidebar-new-task-btn:hover {
    background-color: #2980b9;
}

.sidebar-section-title {
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 10px;
    color: #bdc3c7;
    font-size: 0.9em;
    text-transform: uppercase;
}

.task-list {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex-grow: 1;
}

.task-list li {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #34495e;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.15s ease;
    position: relative;
    min-height: 44px; /* 确保最小高度 */
}

.task-list li:hover {
    background-color: #34495e;
}

.task-list li.active {
    background-color: #3498db;
    color: #ffffff;
    border-left: 4px solid #2980b9;
}

.task-list li.active .task-name {
    font-weight: 600;
}

.task-name {
    flex: 1;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0; /* 允许flex项目缩小到内容宽度以下 */
    font-size: 0.9em;
    line-height: 1.4;
}

.task-actions {
    display: flex;
    gap: 5px;
    flex-shrink: 0; /* 防止按钮被压缩 */
}

/* Tooltip样式优化 */
.task-name[title]:hover {
    position: relative;
}

.task-list li .task-actions button {
    background: none;
    border: none;
    color: #95a5a6;
    cursor: pointer;
    padding: 3px;
    font-size: 0.9em;
}

.task-list li .task-actions button:hover {
    color: #ecf0f1;
}

.management-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.management-list li {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #34495e;
    display: flex;
    align-items: center;
    transition: background-color 0.2s ease;
    font-size: 0.95em;
}

.management-list li:hover, .management-list li.active {
    background-color: #34495e;
}

.management-list li .management-item-name {
    display: flex;
    align-items: center;
}

.management-list li i { /* For icons */
    margin-right: 8px;
    width: 16px; /* Ensure icon takes space */
    text-align: center;
}

.main-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    transition: opacity 0.15s ease;
    position: relative;
}

.main-content.switching {
    opacity: 0.5;
}

.main-header {
    padding: 10px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
}

.main-header input[type="text"] {
    flex-grow: 1;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
    margin-right: 10px;
}

.main-header .run-button {
    background-color: #27ae60;
    color: white;
    border: none;
    padding: 8px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 1em;
    cursor: pointer;
    border-radius: 4px;
}

.main-header .run-button:hover {
    background-color: #229954;
}

.main-header .run-button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

.main-tabs-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
}

.config-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    padding: 0 20px;
    background-color: #f8f9fa;
    align-items: center;
}

.tabs .tab-button {
    padding: 12px 20px;
    cursor: pointer;
    border: none;
    background: none;
    font-size: 0.95em;
    border-bottom: 3px solid transparent;
    margin-bottom: -1px;
    transition: all 0.2s ease;
}

.tabs .tab-button.active {
    border-bottom: 3px solid #3498db;
    color: #3498db;
    font-weight: bold;
    background-color: #ffffff;
}

.tabs .tab-button:hover {
    background-color: #e9ecef;
}

.tab-content {
    display: none;
    flex-grow: 1;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

/* 保存任务按钮样式 */
.save-task-btn {
    margin-left: auto;
    padding: 8px 16px;
    background-color: #27ae60;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 500;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.save-task-btn:hover {
    background-color: #229954;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.save-task-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.save-task-btn:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 旋转动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}





.form-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.1em;
    color: #555;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 0.9em;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 0.95em;
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.response-panel {
    padding: 20px;
    overflow-y: auto;
    flex-basis: 50%;
    background-color: #f8f9fa;
}

.response-panel .status-bar {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #e9ecef;
    border-radius: 4px;
    font-size: 0.9em;
}

.response-panel .status-bar span {
    margin-right: 15px;
}

.response-panel .status-bar .status-code.success {
    color: #27ae60;
    font-weight: bold;
}

.response-panel .status-bar .status-code.error {
    color: #c0392b;
    font-weight: bold;
}

/* 运行输出和历史样式 */
.log-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    gap: 15px;
}

.log-controls button {
    padding: 6px 12px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
}

.log-controls button:hover {
    background-color: #5a6268;
}

.log-controls div {
    display: flex;
    align-items: center;
    gap: 8px;
}

.log-controls input[type="checkbox"] {
    margin: 0;
}

.log-controls label {
    margin: 0;
    font-size: 0.9em;
    color: #666;
}

.status-bar {
    margin-bottom: 15px;
    padding: 15px 20px;
    background-color: #e9ecef;
    border-radius: 4px;
    font-size: 0.9em;
}

.status-bar span {
    margin-right: 15px;
}

.status-bar .status-code.success {
    color: #27ae60;
    font-weight: bold;
}

.status-bar .status-code.error {
    color: #c0392b;
    font-weight: bold;
}

.response-data {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    margin: 0 20px 20px 20px;
    line-height: 1.4;
}

.response-data .log-info {
    color: #3498db;
    display: block;
    margin-bottom: 5px;
}

.response-data .log-warning {
    color: #f39c12;
    display: block;
    margin-bottom: 5px;
}

.response-data .log-error {
    color: #e74c3c;
    display: block;
    margin-bottom: 5px;
}

.response-data .log-success {
    color: #27ae60;
    display: block;
    margin-bottom: 5px;
}

/* 状态码样式 */
.status-code {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.9em;
}

.status-code.success {
    color: #27ae60;
    background-color: #d5f4e6;
}

.status-code.error {
    color: #e74c3c;
    background-color: #fdf2f2;
}

.status-code.warning {
    color: #f39c12;
    background-color: #fef9e7;
}

.status-code.info {
    color: #3498db;
    background-color: #e3f2fd;
}

/* 运行历史样式 */
.history-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.history-controls button {
    padding: 6px 12px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
}

.history-controls button:hover {
    background-color: #2980b9;
}

.history-summary {
    font-size: 0.9em;
    color: #666;
    font-style: italic;
}

.history-item {
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 10px;
    background-color: #fff;
}

.history-item.success {
    border-left: 4px solid #27ae60;
}

.history-item.error {
    border-left: 4px solid #e74c3c;
}

.history-item .history-time {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.history-item .history-details {
    font-size: 0.9em;
    color: #666;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.history-item-header strong {
    color: #333;
    font-size: 0.95em;
}

.history-item-header span {
    font-size: 0.85em;
    color: #666;
    font-weight: normal;
}

.history-item-details {
    font-size: 0.9em;
    color: #666;
}

.history-item-details span {
    font-weight: 500;
}

.history-item-details p {
    margin: 5px 0 0 0;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.85em;
    line-height: 1.3;
    border-left: 3px solid #e0e0e0;
}

/* 队列项样式 */
.queue-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
}

.queue-item .form-group {
    margin-bottom: 5px;
}

.queue-item-header {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.queue-item-header h5 {
    margin: 0;
    font-size: 1em;
}

.queue-item-header button {
    background: none;
    border: none;
    color: #c0392b;
    cursor: pointer;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

/* 防止页面滚动条 */
body.modal-open {
    overflow: hidden;
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #888;
    width: 80%;
    max-width: 1200px;
    border-radius: 5px;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 0px 20px;
    background-color: #2c3e50;
    color: #ecf0f1;
    border-bottom: 1px solid #34495e;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.modal-header h4 {
    margin: 0;
    font-size: 1.2em;
}

.modal-close-btn {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    background: none;
    border: none;
    cursor: pointer;
}

.modal-close-btn:hover,
.modal-close-btn:focus {
    color: #ecf0f1;
    text-decoration: none;
}

.modal-body {
    padding: 0;
    flex-grow: 1;
    overflow: hidden;
    min-height: 575px;
}

.modal-footer {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    text-align: right;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.modal-footer button {
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
}

.modal-footer .btn-primary {
    background-color: #3498db;
    color: white;
    border: 1px solid #3498db;
}

.modal-footer .btn-secondary {
    background-color: #e0e0e0;
    color: #333;
    border: 1px solid #ccc;
}

/* Modal Layout - Left Sidebar + Right Fixed Panel */
.modal-split-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 0;
    height: 600px;
}

.modal-left-panel {
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

.modal-left-header {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.modal-left-header h5 {
    margin: 0;
    font-size: 1em;
    color: #333;
}

.modal-left-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.modal-left-footer {
    padding: 10px 15px;
    border-top: 1px solid #e0e0e0;
    background-color: #fff;
    flex-shrink: 0;
}

.modal-right-panel {
    background-color: #fff;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.modal-right-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    flex-shrink: 0;
}

.modal-right-header h5 {
    margin: 0;
    font-size: 1.1em;
    color: #333;
}

.modal-right-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* Environment and Template List Items */
.env-list-item, .tpl-list-item {
    padding: 12px 15px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
    background-color: #fff;
}

.env-list-item:hover, .tpl-list-item:hover {
    background-color: #f0f7ff;
}

.env-list-item.active, .tpl-list-item.active {
    background-color: #e3f2fd;
    border-left: 4px solid #3498db;
    font-weight: 500;
}

.env-list-item .item-name, .tpl-list-item .item-name {
    flex: 1;
    font-size: 0.95em;
    color: #333;
}

.env-list-item .item-actions, .tpl-list-item .item-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.env-list-item:hover .item-actions, .tpl-list-item:hover .item-actions {
    opacity: 1;
}

.env-list-item button, .tpl-list-item button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9em;
    padding: 4px 6px;
    border-radius: 3px;
    color: #666;
    transition: all 0.2s ease;
}

.env-list-item button:hover, .tpl-list-item button:hover {
    background-color: #e0e0e0;
    color: #333;
}

/* Empty state */
.modal-empty-state {
    padding: 40px 20px;
    text-align: center;
    color: #95a5a6;
    font-style: italic;
}

/* Button styles for modal */
.btn-sm {
    padding: 6px 12px;
    font-size: 0.85em;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-sm.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-sm.btn-primary:hover {
    background-color: #2980b9;
}

.btn-sm.btn-secondary {
    background-color: #f0f0f0;
    color: #333;
    border: 1px solid #ddd;
}

.btn-sm.btn-secondary:hover {
    background-color: #e0e0e0;
}

/* 按钮样式 */
button {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* CodeMirror 自定义样式 */
.CodeMirror {
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    height: auto;
    min-height: 200px;
}

.CodeMirror-focused {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.CodeMirror-scroll {
    min-height: 200px;
    max-height: 400px;
}

/* CodeMirror 主题调整 */
.cm-s-material .CodeMirror {
    background-color: #263238;
    color: #eeffff;
}

.cm-s-material .CodeMirror-gutters {
    background-color: #37474f;
    border-right: 1px solid #37474f;
}

.cm-s-material .CodeMirror-linenumber {
    color: #546e7a;
}

/* 代码编辑器容器 */
.code-editor-container {
    position: relative;
}

.code-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    font-size: 0.85em;
}

.code-editor-toolbar .toolbar-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.code-editor-toolbar .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-selector {
    padding: 4px 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 0.8em;
    background-color: white;
}

.code-editor-container .CodeMirror {
    border-radius: 0 0 4px 4px;
    border-top: none;
}

/* Bootstrap Flexbox 工具类 */
.d-flex {
    display: flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}

/* For side-by-side layout of columns */
.row {
    display: flex;
    gap: 15px; /* Space between the columns */
    margin-bottom: 15px; /* Spacing below the row, similar to form-group */
}
.row .col-md-6 { /* When used for two columns */
    flex: 1; /* Each column takes equal share of the space */
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .main-content {
        flex-direction: column;
    }

    .request-panel,
    .response-panel {
        flex-basis: auto;
    }

    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* 预览弹窗特定样式 */
#previewModal .modal-content {
    max-width: 600px;
}

#previewModal .modal-header h3 {
    margin: 0;
    font-size: 1.3em;
    color: #ecf0f1;
    display: flex;
    align-items: center;
}



#previewModal .modal-body {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

#previewContent {
    white-space: pre-wrap;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Fira Mono', 'Droid Sans Mono', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.6;
    padding: 20px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    background: #f8f9fa;
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

#previewContent.success {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-color: #27ae60;
    color: #2c3e50;
}

#previewContent.error {
    background: linear-gradient(135deg, #fdf2f2 0%, #fef5f5 100%);
    border-color: #e74c3c;
    color: #c0392b;
}



