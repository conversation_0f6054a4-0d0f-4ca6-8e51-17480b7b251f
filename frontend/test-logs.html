<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .console-output {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .log-info { color: #4CAF50; }
        .log-warning { color: #FF9800; }
        .log-error { color: #F44336; }
        button {
            margin: 10px 5px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>日志测试页面</h1>
    
    <div>
        <button onclick="testTask3Logs()">测试任务3日志</button>
        <button onclick="clearLogs()">清空日志</button>
        <button onclick="testAPI()">测试API连接</button>
    </div>
    
    <div id="consoleOutput" class="console-output">
        <div class="log-info">日志测试页面已加载</div>
    </div>

    <script>
        // 简单的API客户端
        class SimpleAPI {
            async get(url) {
                const response = await fetch(url);
                return response.json();
            }
        }

        const api = new SimpleAPI();

        function appendLog(level, message) {
            const consoleOutput = document.getElementById('consoleOutput');
            const logElement = document.createElement('div');
            logElement.className = `log-${level}`;
            logElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(logElement);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('consoleOutput').innerHTML = '<div class="log-info">日志已清空</div>';
        }

        async function testAPI() {
            try {
                appendLog('info', '测试API连接...');
                const response = await api.get('/task/api/list');
                appendLog('info', `API连接成功，找到${response.length}个任务`);
                response.forEach(task => {
                    appendLog('info', `任务${task.id}: ${task.name}`);
                });
            } catch (error) {
                appendLog('error', `API连接失败: ${error.message}`);
            }
        }

        async function testTask3Logs() {
            try {
                appendLog('info', '正在获取任务3的日志...');
                const response = await api.get('/task/api/logs/task/3');
                
                if (response.success) {
                    appendLog('info', `成功获取任务3日志，共${response.logs.length}条`);
                    
                    if (response.logs.length > 0) {
                        appendLog('info', '最新5条日志:');
                        response.logs.slice(-5).forEach(log => {
                            appendLog(log.level, `[${log.timestamp}] ${log.message}`);
                        });
                    } else {
                        appendLog('warning', '任务3没有日志数据');
                    }
                } else {
                    appendLog('error', '获取任务3日志失败');
                }
            } catch (error) {
                appendLog('error', `获取任务3日志出错: ${error.message}`);
            }
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                appendLog('info', '自动测试API连接...');
                testAPI();
            }, 1000);
        });
    </script>
</body>
</html>
