/**
 * 主应用程序
 */
class App {
    constructor() {
        this.currentTab = 'config';
        this.runningTask = null;
        this.runTimer = null;
        this.newTaskQueueCounter = 0;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initTabs();
        this.loadInitialData();
    }

    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
    }

    /**
     * 初始化标签页
     */
    initTabs() {
        // 检查URL参数是否指定了任务ID，如果有则切换到运行输出标签页
        const urlParams = new URLSearchParams(window.location.search);
        const taskId = urlParams.get('task');

        if (taskId) {
            this.switchTab('run-output');
        } else {
            this.switchTab('config');
        }

        this.initNewTaskModal();
        this.initRunOutputFeatures();
        this.initRunHistoryFeatures();
        this.initLogSettings();
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });

        const targetButton = document.querySelector(`.tab-button[data-tab="${tabName}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
        } else {
            console.error('找不到tab按钮:', tabName);
            return;
        }

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // 将连字符转换为驼峰命名
        const contentId = this.getTabContentId(tabName);
        const targetContent = document.getElementById(contentId);
        if (targetContent) {
            targetContent.classList.add('active');
        } else {
            console.error('找不到tab内容:', contentId);
            return;
        }

        this.currentTab = tabName;

        // 根据标签页加载相应数据
        this.onTabSwitch(tabName);
    }

    /**
     * 获取tab内容的ID
     */
    getTabContentId(tabName) {
        // 将连字符转换为驼峰命名
        const camelCase = tabName.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
        return `${camelCase}TabContent`;
    }

    /**
     * 标签页切换时的处理
     */
    onTabSwitch(tabName) {
        switch (tabName) {
            case 'config':
                if (window.taskManager && window.taskManager.currentTask) {
                    if (window.queueManager) {
                        window.queueManager.renderQueues(window.taskManager.currentTask.queues || []);
                    }
                }
                if (window.environmentManager) {
                    window.environmentManager.loadEnvironments();
                }
                break;
            case 'run-output':
                // 运行输出tab切换时的处理
                this.initRunOutputTab();
                break;
            case 'run-history':
                this.loadRunHistory();
                break;
            case 'charts':
                // 统计图表tab切换时的处理
                this.initChartsTab();
                break;
        }
    }

    /**
     * 初始化运行输出tab
     */
    initRunOutputTab() {
        // 如果没有运行任务，显示默认状态
        if (!this.runningTask) {
            this.resetRunStatus();
            const consoleOutput = document.getElementById('consoleOutput');
            if (consoleOutput) {
                consoleOutput.innerHTML = `
                    <span class="log-info">欢迎使用 KafkaTool！</span>
                    <span class="log-info">请配置任务参数并点击运行按钮开始测试。</span>
                `;
            }
        }
    }

    /**
     * 初始化统计图表tab
     */
    initChartsTab() {
        // 统计图表的初始化逻辑
        console.log('统计图表tab已激活');

        // 检查Chart.js是否加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js 未加载，无法显示图表');
            this.showChartsEmptyState('Chart.js 库未加载，请刷新页面重试');
            return;
        }

        // 延迟加载图表，确保DOM完全渲染
        setTimeout(() => {
            this.loadStatisticsCharts();
        }, 100);
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            // 显示加载状态
            this.showLoading();

            // 并行加载各种数据
            await Promise.all([
                taskManager.loadTasks(),
                environmentManager.loadEnvironments(),
                templateManager.loadTemplates()
            ]);

            this.hideLoading();
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.hideLoading();
            this.showError('加载数据失败，请刷新页面重试');
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const consoleOutput = document.getElementById('consoleOutput');
        consoleOutput.textContent = '正在加载数据...';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const consoleOutput = document.getElementById('consoleOutput');
        if (consoleOutput.textContent === '正在加载数据...') {
            consoleOutput.textContent = '欢迎使用 KafkaTool！\n请配置任务参数并点击运行按钮开始测试。';
        }
    }

    /**
     * 开始任务运行监控
     */
    startTaskMonitoring(runId, taskName) {
        this.runningTask = {
            runId: runId,
            taskName: taskName,
            startTime: Date.now(),
            messageCount: 0,
            cycleCount: 0,
            errorCount: 0
        };

        // 更新状态显示
        this.updateRunStatus('running', '运行中');

        // 开始计时器
        this.startRunTimer();

        // 开始轮询任务状态
        this.pollTaskStatus();
    }

    /**
     * 开始运行计时器
     */
    startRunTimer() {
        this.runTimer = setInterval(() => {
            if (this.runningTask) {
                const elapsed = Date.now() - this.runningTask.startTime;
                this.updateElapsedTime(elapsed);
            }
        }, 1000);
    }

    /**
     * 停止运行计时器
     */
    stopRunTimer() {
        if (this.runTimer) {
            clearInterval(this.runTimer);
            this.runTimer = null;
        }
    }

    /**
     * 轮询任务状态
     */
    async pollTaskStatus() {
        if (!this.runningTask) return;

        try {
            const runningTasks = await api.getRunningTasks();

            if (runningTasks.success && runningTasks.tasks) {
                const currentTask = runningTasks.tasks.find(task =>
                    task.run_id === this.runningTask.runId
                );

                if (currentTask) {
                    // 更新统计信息
                    this.updateRunStats(currentTask);
                } else {
                    // 任务已结束
                    this.stopTaskMonitoring();
                }
            }
        } catch (error) {
            console.error('轮询任务状态失败:', error);
        }

        // 如果任务还在运行，继续轮询
        if (this.runningTask) {
            setTimeout(() => this.pollTaskStatus(), 2000);
        }
    }

    /**
     * 更新运行统计信息
     */
    updateRunStats(taskData) {
        if (taskData.message_count !== undefined) {
            this.runningTask.messageCount = taskData.message_count;
            document.getElementById('runMessageCount').textContent = taskData.message_count;
        }

        if (taskData.cycle_count !== undefined) {
            this.runningTask.cycleCount = taskData.cycle_count;
            document.getElementById('runCycleCount').textContent = taskData.cycle_count;
        }

        if (taskData.error_count !== undefined) {
            this.runningTask.errorCount = taskData.error_count;
            const errorElement = document.getElementById('runErrorCount');
            errorElement.textContent = taskData.error_count;
            errorElement.className = taskData.error_count > 0 ? 'status-code error' : 'status-code';
        }

        // 更新控制台输出
        if (taskData.latest_log) {
            this.appendToConsole(taskData.latest_log);
        }
    }

    /**
     * 停止任务监控
     */
    stopTaskMonitoring() {
        this.runningTask = null;
        this.stopRunTimer();
        this.updateRunStatus('completed', '已完成');
    }

    /**
     * 更新运行状态
     */
    updateRunStatus(status, statusText) {
        const statusElement = document.getElementById('runStatus');
        statusElement.textContent = statusText;

        statusElement.className = 'status-code';
        if (status === 'running') {
            statusElement.classList.add('success');
        } else if (status === 'error') {
            statusElement.classList.add('error');
        }
    }

    /**
     * 更新运行时长
     */
    updateElapsedTime(elapsed) {
        const hours = Math.floor(elapsed / 3600000);
        const minutes = Math.floor((elapsed % 3600000) / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);

        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('runElapsedTime').textContent = timeString;
    }

    /**
     * 添加到控制台输出
     */
    appendToConsole(message) {
        const consoleOutput = document.getElementById('consoleOutput');
        const timestamp = new Date().toLocaleTimeString();

        // 将新日志添加到现有内容
        const newLogLine = `[${timestamp}] ${message}`;
        const currentContent = consoleOutput.textContent;

        // 分割现有日志行
        const lines = currentContent ? currentContent.split('\n').filter(line => line.trim()) : [];

        // 添加新日志行
        lines.push(newLogLine);

        // 限制日志条数（从配置文件读取，默认10条用于调试）
        const maxLogLines = window.AppConfig?.logging?.maxConsoleLogLines || 10;
        if (lines.length > maxLogLines) {
            lines.splice(0, lines.length - maxLogLines);
        }

        // 更新控制台内容
        consoleOutput.textContent = lines.join('\n');

        // 自动滚动到底部
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }

    /**
     * 清空控制台
     */
    clearConsole() {
        document.getElementById('consoleOutput').textContent = '';
    }

    /**
     * 重置运行状态
     */
    resetRunStatus() {
        document.getElementById('runStatus').textContent = '就绪';
        document.getElementById('runMessageCount').textContent = '0';
        document.getElementById('runCycleCount').textContent = '0';
        document.getElementById('runErrorCount').textContent = '0';
        document.getElementById('runElapsedTime').textContent = '00:00:00';

        const statusElement = document.getElementById('runStatus');
        statusElement.className = 'status-code';
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error(message);
        this.appendToConsole(`错误: ${message}`);
        this.updateRunStatus('error', '错误');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log(message);
        this.appendToConsole(message);
    }

    /**
     * 导出配置
     */
    exportConfig() {
        if (!taskManager.currentTask) {
            alert('请先选择一个任务');
            return;
        }

        const config = {
            task: taskManager.currentTask,
            environment: environmentManager.currentEnvironment,
            timestamp: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `kafkatool-config-${taskManager.currentTask.name || 'task'}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    /**
     * 导入配置
     */
    importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const config = JSON.parse(e.target.result);
                        this.loadConfig(config);
                    } catch (error) {
                        this.showError('配置文件格式错误');
                    }
                };
                reader.readAsText(file);
            }
        };

        input.click();
    }

    /**
     * 加载配置
     */
    loadConfig(config) {
        if (config.task) {
            taskManager.selectTask(config.task);
        }

        if (config.environment) {
            environmentManager.selectEnvironment(config.environment.id);
        }

        this.showSuccess('配置导入成功');
    }

    /**
     * 初始化新增任务弹窗
     */
    initNewTaskModal() {
        const newTaskBtn = document.getElementById('newTaskBtn');
        const createNewTaskBtn = document.getElementById('createNewTaskBtn');
        const newTaskAddQueueBtn = document.getElementById('newTaskAddQueueBtn');

        if (newTaskBtn) {
            newTaskBtn.addEventListener('click', () => {
                this.resetNewTaskModal();
                this.loadEnvironmentsForNewTask();
                document.getElementById('newTaskModal').style.display = 'block';
            });
        }

        if (newTaskAddQueueBtn) {
            newTaskAddQueueBtn.addEventListener('click', () => {
                this.addQueueToNewTask();
            });
        }

        if (createNewTaskBtn) {
            createNewTaskBtn.addEventListener('click', async () => {
                await this.createTaskFromModal();
            });
        }

        // 环境选择变化事件
        const newEnvironmentSelect = document.getElementById('newEnvironmentSelect');
        if (newEnvironmentSelect) {
            newEnvironmentSelect.addEventListener('change', (e) => {
                this.updateNewTaskEnvironmentPreview(e.target.value);
            });
        }

        // 绑定新建任务弹窗的关闭事件
        this.bindNewTaskModalCloseEvents();
    }

    /**
     * 绑定新建任务弹窗的关闭事件
     */
    bindNewTaskModalCloseEvents() {
        // 关闭按钮（右上角的 × 按钮）
        const closeBtn = document.querySelector('[data-modal-id="newTaskModal"].modal-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeNewTaskModal();
            });
        }

        // 取消按钮（底部的取消按钮）
        const cancelBtn = document.querySelector('[data-modal-id="newTaskModal"].btn-secondary');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.closeNewTaskModal();
            });
        }

        // 注意：不添加点击外部关闭功能，避免用户意外丢失数据
    }

    /**
     * 关闭新建任务弹窗
     */
    closeNewTaskModal() {
        document.getElementById('newTaskModal').style.display = 'none';
    }

    /**
     * 重置新建任务弹窗
     */
    resetNewTaskModal() {
        document.getElementById('newTaskName').value = '';
        document.getElementById('newTaskDescription').value = '';
        document.getElementById('newCycleDuration').value = '60';
        document.getElementById('newTotalDuration').value = '300';
        document.getElementById('newEnvironmentSelect').value = '';
        document.getElementById('newEnvVariablesPreview').value = '';

        // 清空队列容器
        const queuesContainer = document.getElementById('newTaskQueuesContainer');
        queuesContainer.innerHTML = `
            <div style="text-align: center; color: #95a5a6; padding: 20px; border: 1px dashed #ddd; border-radius: 4px;">
                点击"添加队列"开始配置消息队列
            </div>
        `;

        this.newTaskQueueCounter = 0;
    }

    /**
     * 为新建任务加载环境列表
     */
    async loadEnvironmentsForNewTask() {
        try {
            const environments = await api.getEnvironments();
            const select = document.getElementById('newEnvironmentSelect');

            select.innerHTML = '<option value="">选择环境...</option>';
            environments.forEach(env => {
                const option = document.createElement('option');
                option.value = env.id;
                option.textContent = `${env.name} (${env.description || '无描述'})`;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('加载环境列表失败:', error);
        }
    }

    /**
     * 更新新建任务的环境变量预览
     */
    async updateNewTaskEnvironmentPreview(environmentId) {
        const preview = document.getElementById('newEnvVariablesPreview');

        if (!environmentId) {
            preview.value = '';
            return;
        }

        try {
            const environment = await api.getEnvironment(environmentId);
            const envVars = environment.variables || {};

            let previewText = '';

            // 兼容两种格式：对象格式和数组格式
            if (Array.isArray(envVars)) {
                // 数组格式：[{key: "KEY", value: "VALUE"}, ...]
                envVars.forEach(item => {
                    previewText += `${item.key}=${item.value}\n`;
                });
            } else if (typeof envVars === 'object') {
                // 对象格式：{"KEY": "VALUE", ...}
                for (const [key, value] of Object.entries(envVars)) {
                    previewText += `${key}=${value}\n`;
                }
            }

            preview.value = previewText;
        } catch (error) {
            console.error('加载环境变量失败:', error);
            preview.value = '加载环境变量失败';
        }
    }

    /**
     * 为新建任务添加队列
     */
    addQueueToNewTask() {
        if (!this.newTaskQueueCounter) {
            this.newTaskQueueCounter = 0;
        }
        this.newTaskQueueCounter++;

        const queuesContainer = document.getElementById('newTaskQueuesContainer');

        // 如果是第一个队列，清空提示信息
        if (this.newTaskQueueCounter === 1) {
            queuesContainer.innerHTML = '';
        }

        const queueItem = document.createElement('div');
        queueItem.className = 'queue-item';
        queueItem.innerHTML = `
            <div class="queue-item-header">
                <h5>队列 ${this.newTaskQueueCounter}</h5>
                <button class="remove-queue-btn" onclick="this.parentElement.parentElement.remove()">✕</button>
            </div>
            <div class="form-group">
                <label>消息模板</label>
                <select class="new-task-template-select">
                    <option value="">选择模板...</option>
                </select>
            </div>
            <div class="form-group">
                <label>Topic (从模板自动获取)</label>
                <input type="text" class="new-task-topic-input" readonly style="background-color: #f8f9fa;" placeholder="请先选择消息模板">
            </div>
            <div class="form-group">
                <label>每周期发送次数</label>
                <input type="number" class="new-task-count-input" value="1" min="1">
            </div>
        `;

        queuesContainer.appendChild(queueItem);

        // 加载模板列表
        this.loadTemplatesForNewTaskQueue(queueItem.querySelector('.new-task-template-select'));

        // 绑定模板选择事件
        const templateSelect = queueItem.querySelector('.new-task-template-select');
        const topicInput = queueItem.querySelector('.new-task-topic-input');

        templateSelect.addEventListener('change', async (e) => {
            if (e.target.value) {
                try {
                    const template = await api.getTemplate(e.target.value);
                    topicInput.value = template.default_topic || '';
                } catch (error) {
                    console.error('获取模板信息失败:', error);
                    topicInput.value = '';
                }
            } else {
                topicInput.value = '';
            }
        });
    }

    /**
     * 为新建任务的队列加载模板列表
     */
    async loadTemplatesForNewTaskQueue(selectElement) {
        try {
            const templates = await api.getTemplates();

            selectElement.innerHTML = '<option value="">选择模板...</option>';
            templates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = template.name;
                selectElement.appendChild(option);
            });
        } catch (error) {
            console.error('加载模板列表失败:', error);
        }
    }

    /**
     * 从弹窗创建任务
     */
    async createTaskFromModal() {
        const name = document.getElementById('newTaskName').value.trim();
        const description = document.getElementById('newTaskDescription').value.trim();
        const cycleDuration = parseInt(document.getElementById('newCycleDuration').value) || 60;
        const totalDuration = parseInt(document.getElementById('newTotalDuration').value) || 300;
        const environmentId = document.getElementById('newEnvironmentSelect').value || null;

        if (!name) {
            alert('请输入任务名称');
            return;
        }

        // 收集队列配置
        const queues = [];
        const queueItems = document.querySelectorAll('#newTaskQueuesContainer .queue-item');

        for (const queueItem of queueItems) {
            const templateSelect = queueItem.querySelector('.new-task-template-select');
            const countInput = queueItem.querySelector('.new-task-count-input');

            if (templateSelect.value) {
                queues.push({
                    template_id: templateSelect.value,
                    count_per_cycle: parseInt(countInput.value) || 1
                });
            }
        }

        try {
            const taskData = {
                name: name,
                description: description,
                cycle_duration: cycleDuration,
                total_duration: totalDuration,
                queues: queues,
                environment_id: environmentId
            };

            // 使用TaskManager的方法创建任务
            if (window.taskManager) {
                await window.taskManager.createTaskFromModal(taskData);
            }

            // 关闭弹窗
            this.closeNewTaskModal();

        } catch (error) {
            console.error('创建任务失败:', error);
            alert('创建任务失败: ' + error.message);
        }
    }

    /**
     * 初始化运行输出功能
     */
    initRunOutputFeatures() {
        const clearLogBtn = document.getElementById('clearLogBtn');
        const autoScrollLog = document.getElementById('autoScrollLog');
        const logFilterInput = document.getElementById('logFilterInput');

        if (clearLogBtn) {
            clearLogBtn.addEventListener('click', () => {
                const consoleOutput = document.getElementById('consoleOutput');
                if (consoleOutput) {
                    consoleOutput.innerHTML = '<span class="log-info"></span>';
                }
            });
        }

        if (logFilterInput) {
            logFilterInput.addEventListener('input', (e) => {
                const filterText = e.target.value.toLowerCase();
                const consoleOutput = document.getElementById('consoleOutput');
                if (consoleOutput) {
                    const logLines = consoleOutput.querySelectorAll('span');
                    logLines.forEach(line => {
                        if (filterText === '' || line.textContent.toLowerCase().includes(filterText)) {
                            line.style.display = 'block';
                        } else {
                            line.style.display = 'none';
                        }
                    });
                }
            });
        }
    }

    /**
     * 初始化运行历史功能
     */
    initRunHistoryFeatures() {
        const refreshHistoryBtn = document.getElementById('refreshHistoryBtn');
        const historyFilterInput = document.getElementById('historyFilterInput');
        const clearTaskHistoryBtn = document.getElementById('clearTaskHistoryBtn');

        if (refreshHistoryBtn) {
            refreshHistoryBtn.addEventListener('click', () => {
                this.loadRunHistory();
            });
        }

        if (clearTaskHistoryBtn) {
            clearTaskHistoryBtn.addEventListener('click', () => {
                this.clearCurrentTaskHistory();
            });
        }

        if (historyFilterInput) {
            historyFilterInput.addEventListener('input', (e) => {
                const filterText = e.target.value.toLowerCase();
                const historyItems = document.querySelectorAll('.history-item');
                historyItems.forEach(item => {
                    if (filterText === '' || item.textContent.toLowerCase().includes(filterText)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }
    }



    /**
     * 初始化日志设置功能
     */
    initLogSettings() {
        const maxLogLinesInput = document.getElementById('maxLogLinesInput');
        const applyLogSettingsBtn = document.getElementById('applyLogSettingsBtn');

        // 从localStorage加载用户设置
        this.loadLogSettings();

        if (applyLogSettingsBtn) {
            applyLogSettingsBtn.addEventListener('click', () => {
                this.applyLogSettings();
            });
        }

        if (maxLogLinesInput) {
            // 当用户按回车键时也应用设置
            maxLogLinesInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyLogSettings();
                }
            });

            // 实时验证输入值
            maxLogLinesInput.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                if (value < 5) {
                    e.target.value = 5;
                } else if (value > 1000) {
                    e.target.value = 1000;
                }
            });
        }
    }

    /**
     * 加载日志设置
     */
    loadLogSettings() {
        try {
            const savedSettings = localStorage.getItem('kafkatool_log_settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                // 更新配置对象
                if (window.AppConfig && window.AppConfig.logging) {
                    window.AppConfig.logging.maxConsoleLogLines = settings.maxLogLines || 10;
                }

                // 更新界面
                const maxLogLinesInput = document.getElementById('maxLogLinesInput');
                if (maxLogLinesInput) {
                    maxLogLinesInput.value = settings.maxLogLines || 10;
                }
            }
        } catch (error) {
            console.error('加载日志设置失败:', error);
        }
    }

    /**
     * 应用日志设置
     */
    applyLogSettings() {
        const maxLogLinesInput = document.getElementById('maxLogLinesInput');
        if (!maxLogLinesInput) return;

        const maxLogLines = parseInt(maxLogLinesInput.value) || 10;

        // 验证范围
        if (maxLogLines < 5 || maxLogLines > 1000) {
            alert('日志条数必须在5-1000之间');
            return;
        }

        try {
            // 更新配置对象
            if (window.AppConfig && window.AppConfig.logging) {
                window.AppConfig.logging.maxConsoleLogLines = maxLogLines;
            }

            // 保存到localStorage
            const settings = {
                maxLogLines: maxLogLines,
                timestamp: new Date().toISOString()
            };
            localStorage.setItem('kafkatool_log_settings', JSON.stringify(settings));

            // 立即清理现有日志以应用新设置
            this.cleanupExistingLogs(maxLogLines);

            // 显示成功提示
            this.showLogSettingsSuccess(maxLogLines);

        } catch (error) {
            console.error('应用日志设置失败:', error);
            alert('应用设置失败，请重试');
        }
    }

    /**
     * 清理现有日志以应用新设置
     */
    cleanupExistingLogs(maxLogLines) {
        const consoleOutput = document.getElementById('consoleOutput');
        if (!consoleOutput) return;

        const logElements = consoleOutput.children;
        if (logElements.length > maxLogLines) {
            const elementsToRemove = logElements.length - maxLogLines;
            for (let i = 0; i < elementsToRemove; i++) {
                if (logElements[0]) {
                    consoleOutput.removeChild(logElements[0]);
                }
            }
        }
    }

    /**
     * 显示日志设置成功提示
     */
    showLogSettingsSuccess(maxLogLines) {
        const applyBtn = document.getElementById('applyLogSettingsBtn');
        if (applyBtn) {
            const originalText = applyBtn.innerHTML;
            applyBtn.innerHTML = '<i data-feather="check" style="width: 14px; height: 14px; margin-right: 4px;"></i>已应用';
            applyBtn.style.background = '#28a745';

            setTimeout(() => {
                applyBtn.innerHTML = originalText;
                applyBtn.style.background = '#28a745';
                feather.replace(); // 重新渲染图标
            }, 2000);
        }

        // 在控制台显示设置更新消息
        if (window.taskManager && typeof window.taskManager.appendToConsole === 'function') {
            window.taskManager.appendToConsole('info', `日志设置已更新：最大显示 ${maxLogLines} 条日志`);
        }
    }

    /**
     * 加载运行历史
     */
    async loadRunHistory() {
        const historyContainer = document.getElementById('historyItemsContainer');
        const historySummary = document.getElementById('historySummary');

        if (historyContainer) {
            // 显示加载状态
            historyContainer.innerHTML = `
                <div style="text-align: center; color: #6c757d; padding: 40px;">
                    <i data-feather="loader" style="width: 32px; height: 32px; margin-bottom: 12px; animation: spin 1s linear infinite;"></i>
                    <p style="margin: 0;">正在加载历史记录...</p>
                </div>
            `;
            feather.replace();
        }

        if (historySummary) {
            historySummary.textContent = '正在加载...';
        }

        try {
            // 获取当前选中的任务ID
            const currentTaskId = this.getCurrentTaskId();

            // 构建API URL，如果有选中的任务则添加task_id参数
            let apiUrl = '/history/api/list';
            if (currentTaskId) {
                apiUrl += `?task_id=${currentTaskId}`;
            }

            // 调用API获取历史记录
            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const histories = await response.json();
            this.renderHistoryList(histories, currentTaskId);

        } catch (error) {
            console.error('加载运行历史失败:', error);
            if (historyContainer) {
                historyContainer.innerHTML = `
                    <div style="text-align: center; color: #dc3545; padding: 40px;">
                        <i data-feather="alert-circle" style="width: 32px; height: 32px; margin-bottom: 12px;"></i>
                        <p style="margin: 0;">加载历史记录失败: ${error.message}</p>
                        <button onclick="app.loadRunHistory()" style="margin-top: 12px; padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">重试</button>
                    </div>
                `;
                feather.replace();
            }
            if (historySummary) {
                historySummary.textContent = '加载失败';
            }
        }
    }

    /**
     * 获取当前选中的任务ID
     */
    getCurrentTaskId() {
        // 优先从任务管理器获取当前任务ID
        if (window.taskManager && window.taskManager.currentTask && window.taskManager.currentTask.id) {
            return window.taskManager.currentTask.id;
        }

        // 备用方案：从DOM查询获取
        const selectedTask = document.querySelector('.task-item.selected');
        if (selectedTask) {
            return selectedTask.dataset.taskId;
        }
        return null;
    }

    /**
     * 获取当前选中的任务名称
     */
    getCurrentTaskName() {
        // 优先从任务管理器获取当前任务名称
        if (window.taskManager && window.taskManager.currentTask && window.taskManager.currentTask.name) {
            return window.taskManager.currentTask.name;
        }

        // 备用方案：从DOM查询获取
        const selectedTask = document.querySelector('.task-item.selected');
        if (selectedTask) {
            const taskNameElement = selectedTask.querySelector('.task-name');
            return taskNameElement ? taskNameElement.textContent.trim() : '未知任务';
        }
        return '未选择任务';
    }

    /**
     * 渲染历史记录列表
     */
    renderHistoryList(histories, currentTaskId = null) {
        const historyContainer = document.getElementById('historyItemsContainer');
        const historySummary = document.getElementById('historySummary');
        const currentTaskName = this.getCurrentTaskName();

        if (!histories || histories.length === 0) {
            // 显示空状态
            if (historyContainer) {
                const emptyMessage = currentTaskId
                    ? `任务 "${currentTaskName}" 暂无运行历史记录`
                    : '暂无运行历史记录';

                historyContainer.innerHTML = `
                    <div id="historyEmptyState" style="text-align: center; color: #95a5a6; padding: 60px 20px; background: #f8f9fa; border-radius: 6px; border: 2px dashed #dee2e6;">
                        <i data-feather="clock" style="width: 48px; height: 48px; margin-bottom: 16px; color: #adb5bd;"></i>
                        <h5 style="margin: 0 0 8px 0; color: #6c757d;">${emptyMessage}</h5>
                        <p style="margin: 0; color: #adb5bd; font-size: 14px;">运行任务后，历史记录将显示在这里</p>
                    </div>
                `;
                feather.replace();
            }
            if (historySummary) {
                const summaryText = currentTaskId
                    ? `任务 "${currentTaskName}" 暂无历史记录`
                    : '暂无历史记录';
                historySummary.textContent = summaryText;
            }
            return;
        }

        // 渲染历史记录列表
        const historyHtml = histories.map(history => this.renderHistoryItem(history)).join('');

        if (historyContainer) {
            historyContainer.innerHTML = historyHtml;
        }

        // 更新统计信息
        if (historySummary) {
            const runningCount = histories.filter(h => h.status === 'running').length;
            const completedCount = histories.filter(h => h.status === 'completed').length;
            const failedCount = histories.filter(h => h.status === 'failed').length;
            const stoppedCount = histories.filter(h => h.status === 'stopped').length;

            let summaryText = `任务 "${currentTaskName}" 共 ${histories.length} 条记录`;

            if (runningCount > 0) summaryText += ` (${runningCount} 运行中`;
            if (completedCount > 0) summaryText += `, ${completedCount} 已完成`;
            if (failedCount > 0) summaryText += `, ${failedCount} 失败`;
            if (stoppedCount > 0) summaryText += `, ${stoppedCount} 已停止`;
            if (runningCount > 0 || completedCount > 0 || failedCount > 0 || stoppedCount > 0) summaryText += ')';

            historySummary.textContent = summaryText;
        }

        // 重新渲染图标
        feather.replace();
    }



    /**
     * 渲染单个历史记录项
     */
    renderHistoryItem(history) {
        const startTime = history.start_time ? new Date(history.start_time).toLocaleString('zh-CN') : '未知时间';
        const duration = this.formatDuration(history.duration);
        const statusClass = this.getStatusClass(history.status);
        const statusText = this.getStatusText(history.status);

        // 获取消息总数和主要topic
        const messageCount = history.message_count || 0;
        const mainTopic = history.main_topic || '未知';
        const lastMessage = history.last_message_content || '暂无消息内容';

        return `
            <div class="history-item ${statusClass}" style="margin-bottom: 12px; padding: 16px; border: 1px solid #e9ecef; border-radius: 6px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                <div class="history-item-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <div>
                        <strong style="color: #2c3e50; font-size: 16px;">${startTime} - ${statusText}</strong>
                        <div style="margin-top: 4px; color: #6c757d; font-size: 14px;">
                            <span>任务: ${history.task_name || '未知任务'}</span>
                            ${history.environment_name ? ` | 环境: ${history.environment_name}` : ''}
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="color: #495057; font-weight: 500;">运行时长: ${duration}</div>
                        <div style="color: #6c757d; font-size: 14px; margin-top: 2px;">周期数: ${history.cycle_count || 0}</div>
                    </div>
                </div>
                <div class="history-item-details" style="border-top: 1px solid #f1f3f4; padding-top: 12px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 12px;">
                        <div>
                            <span style="color: #6c757d; font-size: 13px;">发送消息数</span>
                            <div style="font-weight: 500; color: #2c3e50;">${messageCount} 条</div>
                        </div>
                        <div>
                            <span style="color: #6c757d; font-size: 13px;">主要Topic</span>
                            <div style="font-weight: 500; color: #2c3e50;">${mainTopic}</div>
                        </div>
                    </div>
                    <div style="margin-bottom: 12px;">
                        <span style="color: #6c757d; font-size: 13px;">最后一条消息</span>
                        <div style="margin-top: 4px; font-family: 'Courier New', monospace; background: #f8f9fa; padding: 8px; border-radius: 4px; border: 1px solid #e9ecef; font-size: 13px; color: #495057; max-height: 100px; overflow-y: auto;">
                            ${lastMessage || '暂无消息内容'}
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px; justify-content: flex-end;">
                        <button onclick="app.viewHistoryDetails(${history.id})" style="padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px;">
                            <i data-feather="eye" style="width: 14px; height: 14px; margin-right: 4px;"></i>查看详情
                        </button>
                        ${history.status === 'stopped' ? `
                            <button onclick="app.rerunHistory(${history.id})" style="padding: 6px 12px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px;">
                                <i data-feather="play" style="width: 14px; height: 14px; margin-right: 4px;"></i>重新运行
                            </button>
                        ` : ''}
                        <button onclick="app.deleteHistory(${history.id})" style="padding: 6px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px;">
                            <i data-feather="trash-2" style="width: 14px; height: 14px; margin-right: 4px;"></i>删除
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取状态对应的CSS类
     */
    getStatusClass(status) {
        switch (status) {
            case 'running': return 'running';
            case 'completed': return 'success';
            case 'failed': return 'error';
            case 'stopped': return 'warning';
            default: return 'info';
        }
    }

    /**
     * 获取状态显示文本
     */
    getStatusText(status) {
        switch (status) {
            case 'running': return '运行中';
            case 'completed': return '已完成';
            case 'failed': return '运行失败';
            case 'stopped': return '已停止';
            default: return '未知状态';
        }
    }

    /**
     * 格式化持续时间
     */
    formatDuration(seconds) {
        if (!seconds || seconds < 0) return '00:00:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * 清空所有历史记录
     */
    async clearAllHistory() {
        if (!confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch('/history/api/delete_all', {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                alert(`成功清空 ${result.count || 0} 条历史记录`);
                this.loadRunHistory(); // 重新加载历史记录
            } else {
                throw new Error(result.error || '清空失败');
            }
        } catch (error) {
            console.error('清空历史记录失败:', error);
            alert(`清空历史记录失败: ${error.message}`);
        }
    }

    /**
     * 清空当前任务的历史记录
     */
    async clearCurrentTaskHistory() {
        const currentTaskId = this.getCurrentTaskId();

        if (!currentTaskId) {
            alert('请先选择一个任务');
            return;
        }

        // 获取当前任务名称用于确认对话框
        let taskName = '当前任务';
        if (window.taskManager && window.taskManager.currentTask) {
            taskName = window.taskManager.currentTask.name;
        }

        if (!confirm(`确定要清空任务"${taskName}"的所有历史记录吗？此操作不可撤销。`)) {
            return;
        }

        try {
            const response = await fetch(`/history/api/clear_task/${currentTaskId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                alert(result.message || `成功清空 ${result.deleted_count || 0} 条历史记录`);
                this.loadRunHistory(); // 重新加载历史记录
            } else {
                throw new Error(result.error || '清空失败');
            }
        } catch (error) {
            console.error('清空任务历史记录失败:', error);
            alert(`清空任务历史记录失败: ${error.message}`);
        }
    }

    /**
     * 查看历史记录详情
     */
    async viewHistoryDetails(historyId) {
        try {
            const response = await fetch(`/history/api/get/${historyId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const historyDetail = await response.json();
            this.showHistoryDetailModal(historyDetail);
        } catch (error) {
            console.error('获取历史记录详情失败:', error);
            alert(`获取历史记录详情失败: ${error.message}`);
        }
    }

    /**
     * 显示历史记录详情模态框
     */
    showHistoryDetailModal(historyDetail) {
        // 这里可以实现详情模态框，暂时使用alert显示
        const info = `
任务名称: ${historyDetail.task_name || '未知'}
开始时间: ${historyDetail.start_time ? new Date(historyDetail.start_time).toLocaleString('zh-CN') : '未知'}
结束时间: ${historyDetail.end_time ? new Date(historyDetail.end_time).toLocaleString('zh-CN') : '未结束'}
状态: ${this.getStatusText(historyDetail.status)}
周期数: ${historyDetail.cycle_count || 0}
消息数: ${historyDetail.message_count || 0}
错误数: ${historyDetail.error_count || 0}
        `;
        alert(info);
    }

    /**
     * 重新运行历史记录
     */
    async rerunHistory(historyId) {
        if (!confirm('确定要重新运行这个任务吗？')) {
            return;
        }

        try {
            const response = await fetch(`/history/api/rerun/${historyId}`, {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                alert('任务已重新开始运行');
                this.loadRunHistory(); // 重新加载历史记录
            } else {
                throw new Error(result.error || '重新运行失败');
            }
        } catch (error) {
            console.error('重新运行任务失败:', error);
            alert(`重新运行任务失败: ${error.message}`);
        }
    }

    /**
     * 删除历史记录
     */
    async deleteHistory(historyId) {
        if (!confirm('确定要删除这条历史记录吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch(`/history/api/delete/${historyId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                this.loadRunHistory(); // 重新加载历史记录
            } else {
                throw new Error(result.error || '删除失败');
            }
        } catch (error) {
            console.error('删除历史记录失败:', error);
            alert(`删除历史记录失败: ${error.message}`);
        }
    }

    /**
     * 显示运行时的示例日志
     */
    showRunningLogs() {
        const consoleOutput = document.getElementById('consoleOutput');
        if (consoleOutput) {
            consoleOutput.innerHTML = `
                <span class="log-info">[2023-10-27 10:00:01] INFO: 任务 "订单处理模拟" 开始运行...</span>
                <span class="log-info">[2023-10-27 10:00:02] INFO: 队列 1 (orders.new) 发送消息: {"orderId": "xyz123", "amount": 99.99}</span>
                <span class="log-info">[2023-10-27 10:00:02] INFO: 队列 2 (inventory.update) 发送消息: {"itemId": "item001", "stock": 99}</span>
                <span class="log-warning">[2023-10-27 10:01:02] WARNING: 队列 1 (orders.new) 消息发送超时.</span>
                <span class="log-error">[2023-10-27 10:01:05] ERROR: 队列 2 (inventory.update) 连接 Kafka 失败.</span>
                <span class="log-info">[2023-10-27 10:01:10] INFO: 周期 2 完成.</span>
            `;
        }
    }

    /**
     * 更新运行状态显示
     */
    updateRunStatus(status, messageCount = 0, cycleCount = 0, errorCount = 0, elapsedTime = '00:00:00') {
        const runStatus = document.getElementById('runStatus');
        const runMessageCount = document.getElementById('runMessageCount');
        const runCycleCount = document.getElementById('runCycleCount');
        const runErrorCount = document.getElementById('runErrorCount');
        const runElapsedTime = document.getElementById('runElapsedTime');

        if (runStatus) {
            runStatus.textContent = status;
            // 移除所有状态类
            runStatus.classList.remove('success', 'error', 'warning', 'info');

            // 根据状态添加相应的类
            switch (status) {
                case '运行中':
                    runStatus.classList.add('success');
                    break;
                case '已停止':
                case '错误':
                    runStatus.classList.add('error');
                    break;
                case '警告':
                    runStatus.classList.add('warning');
                    break;
                default:
                    runStatus.classList.add('info');
                    break;
            }
        }

        if (runMessageCount) runMessageCount.textContent = messageCount;
        if (runCycleCount) runCycleCount.textContent = cycleCount;
        if (runErrorCount) {
            runErrorCount.textContent = errorCount;
            // 错误数的样式
            runErrorCount.classList.remove('success', 'error', 'warning', 'info');
            if (errorCount > 0) {
                runErrorCount.classList.add('error');
            } else {
                runErrorCount.classList.add('success');
            }
        }
        if (runElapsedTime) runElapsedTime.textContent = elapsedTime;
    }

    /**
     * 加载统计图表
     */
    async loadStatisticsCharts() {
        const currentTaskId = this.getCurrentTaskId();
        const chartTaskName = document.getElementById('chartTaskName');
        const chartsContainer = document.getElementById('chartsContainer');
        const chartsEmptyState = document.getElementById('chartsEmptyState');

        console.log('开始加载统计图表数据，当前任务ID:', currentTaskId);

        // 更新任务名称显示
        if (chartTaskName) {
            if (currentTaskId && window.taskManager && window.taskManager.currentTask) {
                chartTaskName.textContent = `任务: ${window.taskManager.currentTask.name}`;
            } else {
                chartTaskName.textContent = '未选择任务';
            }
        }

        if (!currentTaskId) {
            console.warn('没有选择任务，显示空状态');
            this.showChartsEmptyState('请先选择一个任务');
            return;
        }

        try {
            console.log('发送统计API请求...');
            // 获取最后一次运行的统计数据
            const response = await fetch(`/statistics/api/latest/${currentTaskId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('统计API响应:', data);

            if (!data.has_data) {
                this.showChartsEmptyState(data.message || '该任务暂无运行历史记录');
                return;
            }

            console.log('统计数据加载成功，开始渲染图表');

            // 隐藏空状态，显示图表
            if (chartsEmptyState) chartsEmptyState.style.display = 'none';
            if (chartsContainer) {
                chartsContainer.style.display = 'block';
                const chartsGrid = chartsContainer.querySelector('.charts-grid');
                if (chartsGrid) chartsGrid.style.display = 'grid';
            }

            // 更新统计摘要
            this.updateChartsSummary(data);

            // 渲染图表
            console.log('开始渲染图表，统计数据:', data.statistics);
            this.renderCharts(data.statistics);

        } catch (error) {
            console.error('加载统计图表失败:', error);
            this.showChartsEmptyState('加载统计数据失败，请稍后重试');
        }
    }

    /**
     * 显示图表空状态
     */
    showChartsEmptyState(message) {
        const chartsContainer = document.getElementById('chartsContainer');
        const chartsEmptyState = document.getElementById('chartsEmptyState');

        if (chartsContainer) {
            // 隐藏图表区域，但保持图表控制区域可见
            const chartsGrid = chartsContainer.querySelector('.charts-grid');
            if (chartsGrid) chartsGrid.style.display = 'none';
        }

        if (chartsEmptyState) {
            chartsEmptyState.style.display = 'block';
            const messageElement = chartsEmptyState.querySelector('p');
            if (messageElement) {
                messageElement.textContent = message;
            }
        }
    }

    /**
     * 更新图表摘要信息
     */
    updateChartsSummary(data) {
        const chartsSummary = document.getElementById('chartsSummary');
        const chartRunStatus = document.getElementById('chartRunStatus');
        const chartTotalMessages = document.getElementById('chartTotalMessages');
        const chartDuration = document.getElementById('chartDuration');

        if (chartsSummary) {
            const startTime = new Date(data.start_time).toLocaleString();
            chartsSummary.textContent = `运行开始时间: ${startTime}`;
        }

        if (chartRunStatus) {
            chartRunStatus.textContent = this.getStatusText(data.status);
            chartRunStatus.style.color = this.getStatusColor(data.status);
        }

        if (chartTotalMessages) {
            chartTotalMessages.textContent = data.statistics?.total_messages || 0;
        }

        if (chartDuration) {
            chartDuration.textContent = this.formatDuration(data.duration || 0);
        }
    }

    /**
     * 渲染图表
     */
    renderCharts(statistics) {
        // 确保图表容器可见
        const chartsContainer = document.getElementById('chartsContainer');
        if (!chartsContainer || chartsContainer.style.display === 'none') {
            console.warn('图表容器不可见，跳过渲染');
            return;
        }

        this.renderMessagesTrendChart(statistics);
        this.renderQueueDistributionChart(statistics);
    }

    /**
     * 渲染消息发送趋势图
     */
    renderMessagesTrendChart(statistics) {
        // 检查Chart.js是否加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js 未加载');
            return;
        }

        const ctx = document.getElementById('messagesTrendChart');
        if (!ctx) {
            console.error('找不到messagesTrendChart canvas元素');
            return;
        }

        // 销毁现有图表
        if (this.messagesTrendChart) {
            this.messagesTrendChart.destroy();
        }

        const timelineData = statistics.messages_timeline || [];

        try {
            this.messagesTrendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timelineData.map(item => {
                        if (item.timestamp) {
                            return new Date(item.timestamp).toLocaleTimeString();
                        } else if (item.cycle) {
                            return `周期 ${item.cycle}`;
                        }
                        return '';
                    }),
                    datasets: [{
                        label: '消息数量',
                        data: timelineData.map(item => item.count || item.messages || 0),
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        } catch (error) {
            console.error('创建消息趋势图时出错:', error);
        }
    }

    /**
     * 渲染队列分布图
     */
    renderQueueDistributionChart(statistics) {
        // 检查Chart.js是否加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js 未加载');
            return;
        }

        const ctx = document.getElementById('queueDistributionChart');
        if (!ctx) {
            console.error('找不到queueDistributionChart canvas元素');
            return;
        }

        // 销毁现有图表
        if (this.queueDistributionChart) {
            this.queueDistributionChart.destroy();
        }

        const queueData = statistics.queue_distribution || {};
        const labels = Object.keys(queueData);
        const data = Object.values(queueData);

        if (labels.length === 0) {
            // 如果没有数据，显示空状态
            ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
            return;
        }

        try {
            this.queueDistributionChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#3498db',
                            '#e74c3c',
                            '#2ecc71',
                            '#f39c12',
                            '#9b59b6',
                            '#1abc9c',
                            '#34495e',
                            '#e67e22'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        } catch (error) {
            console.error('创建队列分布图时出错:', error);
        }
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'running': '运行中',
            'completed': '已完成',
            'stopped': '已停止',
            'error': '错误'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取状态颜色
     */
    getStatusColor(status) {
        const colorMap = {
            'running': '#2ecc71',
            'completed': '#3498db',
            'stopped': '#f39c12',
            'error': '#e74c3c'
        };
        return colorMap[status] || '#6c757d';
    }

    /**
     * 格式化持续时间
     */
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟${secs}秒`;
        } else {
            return `${secs}秒`;
        }
    }
}

// 当页面加载完成时启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();

    // 添加模态框关闭功能
    setupModalCloseHandlers();
});

/**
 * 设置模态框关闭处理器
 */
function setupModalCloseHandlers() {
    // 点击模态框外部关闭（排除新建任务弹窗）
    window.addEventListener('click', (event) => {
        if (event.target.classList.contains('modal') && event.target.id !== 'newTaskModal') {
            event.target.style.display = 'none';
        }
    });

    // 按ESC键关闭模态框（排除新建任务弹窗）
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal[style*="block"]');
            openModals.forEach(modal => {
                if (modal.id !== 'newTaskModal') {
                    modal.style.display = 'none';
                }
            });
        }
    });
}


