/**
 * API调用封装类
 */
class API {
    constructor(baseURL = '') {
        this.baseURL = baseURL;
    }

    /**
     * 发送HTTP请求
     */
    async request(url, options = {}) {
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(this.baseURL + url, config);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    /**
     * GET请求
     */
    async get(url) {
        return this.request(url, { method: 'GET' });
    }

    /**
     * POST请求
     */
    async post(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     */
    async put(url, data) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }

    // ===== 任务相关API =====

    /**
     * 获取任务列表
     */
    async getTasks() {
        return this.get('/task/api/list');
    }

    /**
     * 获取任务详情
     */
    async getTask(taskId) {
        return this.get(`/task/api/get/${taskId}`);
    }

    /**
     * 创建任务
     */
    async createTask(taskData) {
        return this.post('/task/api/save', taskData);
    }

    /**
     * 更新任务
     */
    async updateTask(taskId, taskData) {
        return this.post('/task/api/save', { ...taskData, id: taskId });
    }

    /**
     * 删除任务
     */
    async deleteTask(taskId) {
        return this.delete(`/task/api/delete/${taskId}`);
    }

    /**
     * 运行任务
     */
    async runTask(taskId) {
        return this.post(`/task/api/run/${taskId}`, {});
    }

    /**
     * 停止任务
     */
    async stopTask(runId) {
        return this.post(`/task/api/stop/${runId}`, {});
    }

    /**
     * 获取运行中的任务
     */
    async getRunningTasks() {
        return this.get('/task/api/running_tasks');
    }

    /**
     * 获取任务运行日志
     */
    async getTaskLogs(runId) {
        return this.get(`/task/api/logs/${runId}`);
    }

    /**
     * 根据任务ID获取当前运行的日志
     */
    async getTaskLogsByTaskId(taskId) {
        return this.get(`/task/api/logs/task/${taskId}`);
    }

    // ===== 环境变量相关API =====

    /**
     * 获取环境变量列表
     */
    async getEnvironments() {
        return this.get('/env/api/list');
    }

    /**
     * 获取环境变量详情
     */
    async getEnvironment(envId) {
        return this.get(`/env/api/get/${envId}`);
    }

    /**
     * 创建环境变量
     */
    async createEnvironment(envData) {
        return this.post('/env/api/save', envData);
    }

    /**
     * 更新环境变量
     */
    async updateEnvironment(envId, envData) {
        return this.post('/env/api/save', { ...envData, id: envId });
    }

    /**
     * 删除环境变量
     */
    async deleteEnvironment(envId) {
        return this.delete(`/env/api/delete/${envId}`);
    }

    // ===== 模板相关API =====

    /**
     * 获取模板列表
     */
    async getTemplates() {
        return this.get('/template/api/list');
    }

    /**
     * 获取模板详情
     */
    async getTemplate(templateId) {
        return this.get(`/template/api/get/${templateId}`);
    }

    /**
     * 创建模板
     */
    async createTemplate(templateData) {
        return this.post('/template/api/save', templateData);
    }

    /**
     * 更新模板
     */
    async updateTemplate(templateId, templateData) {
        return this.post('/template/api/save', { ...templateData, id: templateId });
    }

    /**
     * 删除模板
     */
    async deleteTemplate(templateId) {
        return this.delete(`/template/api/delete/${templateId}`);
    }

    /**
     * 测试模板
     */
    async testTemplate(templateData) {
        return this.post('/template/api/test', templateData);
    }

    /**
     * 预览模板
     */
    async previewTemplate(templateData) {
        return this.post('/template/api/preview', templateData);
    }

    // ===== 历史记录相关API =====

    /**
     * 获取历史记录列表
     */
    async getHistory() {
        return this.get('/history/api/list');
    }

    /**
     * 获取历史记录详情
     */
    async getHistoryDetail(historyId) {
        return this.get(`/history/api/get/${historyId}`);
    }

    /**
     * 删除历史记录
     */
    async deleteHistory(historyId) {
        return this.delete(`/history/api/delete/${historyId}`);
    }

    /**
     * 重新运行历史记录
     */
    async rerunHistory(historyId) {
        return this.post(`/history/api/rerun/${historyId}`, {});
    }

    // ===== 仪表盘相关API =====

    /**
     * 获取仪表盘统计数据
     */
    async getDashboardStats() {
        return this.get('/api/stats');
    }
}

// 创建全局API实例
window.api = new API();
