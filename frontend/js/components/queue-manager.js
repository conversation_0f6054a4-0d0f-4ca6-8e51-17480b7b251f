/**
 * 队列管理组件
 */
class QueueManager {
    constructor() {
        this.templates = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadTemplates();
    }

    bindEvents() {
        // 添加队列按钮
        document.getElementById('addQueueBtn').addEventListener('click', () => {
            this.addQueue();
        });
    }

    /**
     * 加载模板列表
     */
    async loadTemplates() {
        try {
            this.templates = await api.getTemplates();
        } catch (error) {
            console.error('加载模板列表失败:', error);
            this.templates = [];
        }
    }

    /**
     * 渲染队列列表
     */
    renderQueues(queues = []) {
        const container = document.getElementById('queuesContainer');
        container.innerHTML = '';

        if (queues.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #95a5a6;">暂无队列配置，点击"添加队列"开始配置</p>';
            return;
        }

        queues.forEach((queue, index) => {
            const queueElement = this.createQueueElement(queue, index);
            container.appendChild(queueElement);
        });
    }

    /**
     * 创建队列元素
     */
    createQueueElement(queue, index) {
        const div = document.createElement('div');
        div.className = 'queue-item';
        div.dataset.index = index;

        // 确保Topic从模板中正确获取
        let displayTopic = queue.topic || '';
        if (queue.template_id && this.templates) {
            const selectedTemplate = this.templates.find(template => template.id == queue.template_id);
            if (selectedTemplate && selectedTemplate.topic) {
                displayTopic = selectedTemplate.topic;
                // 同时更新队列数据中的topic
                queue.topic = selectedTemplate.topic;
            }
        }

        div.innerHTML = `
            <div class="queue-item-header">
                <h5>队列 ${index + 1}</h5>
                <button onclick="queueManager.removeQueue(${index})">✕</button>
            </div>
            <div class="form-group">
                <label>消息模板</label>
                <select onchange="queueManager.updateTemplateAndTopic(${index}, this.value)">
                    <option value="">选择模板...</option>
                    ${this.renderTemplateOptions(queue.template_id)}
                </select>
            </div>
            <div class="form-group">
                <label>Topic (从模板自动获取)</label>
                <input type="text" value="${displayTopic}"
                       readonly
                       style="background-color: #f8f9fa; cursor: not-allowed;"
                       placeholder="请先选择消息模板">
            </div>
            <div class="form-group">
                <label>每周期发送次数</label>
                <input type="number" value="${queue.messages_per_cycle || 1}" min="1"
                       onchange="queueManager.updateQueue(${index}, 'messages_per_cycle', parseInt(this.value))">
            </div>
        `;

        return div;
    }

    /**
     * 渲染模板选项
     */
    renderTemplateOptions(selectedTemplateId) {
        return this.templates.map(template =>
            `<option value="${template.id}" ${template.id == selectedTemplateId ? 'selected' : ''}>
                ${template.name}
            </option>`
        ).join('');
    }

    /**
     * 添加队列
     */
    addQueue() {
        if (!taskManager.currentTask) {
            alert('请先选择或创建一个任务');
            return;
        }

        if (!taskManager.currentTask.queues) {
            taskManager.currentTask.queues = [];
        }

        const newQueue = {
            topic: '',
            template_id: null,
            messages_per_cycle: 1
        };

        taskManager.currentTask.queues.push(newQueue);
        this.renderQueues(taskManager.currentTask.queues);
    }

    /**
     * 移除队列
     */
    removeQueue(index) {
        if (!taskManager.currentTask || !taskManager.currentTask.queues) return;

        if (confirm('确定要删除这个队列配置吗？')) {
            taskManager.currentTask.queues.splice(index, 1);
            this.renderQueues(taskManager.currentTask.queues);
        }
    }

    /**
     * 更新队列配置
     */
    updateQueue(index, field, value) {
        if (!taskManager.currentTask || !taskManager.currentTask.queues) return;

        if (taskManager.currentTask.queues[index]) {
            taskManager.currentTask.queues[index][field] = value;
        }
    }

    /**
     * 更新模板并自动设置Topic
     */
    updateTemplateAndTopic(index, templateId) {
        if (!taskManager.currentTask || !taskManager.currentTask.queues) return;

        const queue = taskManager.currentTask.queues[index];
        if (!queue) return;

        // 更新模板ID
        queue.template_id = templateId ? parseInt(templateId) : null;

        // 根据模板自动设置Topic
        if (templateId) {
            const selectedTemplate = this.templates.find(template => template.id == templateId);
            if (selectedTemplate && selectedTemplate.topic) {
                queue.topic = selectedTemplate.topic;
            } else {
                queue.topic = '';
            }
        } else {
            queue.topic = '';
        }

        // 重新渲染队列以更新Topic显示
        this.renderQueues(taskManager.currentTask.queues);
    }

    /**
     * 验证队列配置
     */
    validateQueues(queues) {
        const errors = [];

        queues.forEach((queue, index) => {
            if (!queue.topic) {
                errors.push(`队列 ${index + 1}: Topic 不能为空`);
            }

            if (!queue.template_id) {
                errors.push(`队列 ${index + 1}: 必须选择消息模板`);
            }

            if (!queue.messages_per_cycle || queue.messages_per_cycle < 1) {
                errors.push(`队列 ${index + 1}: 每周期发送次数必须大于0`);
            }
        });

        return errors;
    }

    /**
     * 获取队列配置摘要
     */
    getQueuesSummary(queues) {
        if (!queues || queues.length === 0) {
            return '无队列配置';
        }

        const totalMessages = queues.reduce((sum, q) => sum + (q.messages_per_cycle || 0), 0);

        return `${queues.length} 个队列，每周期 ${totalMessages} 条消息`;
    }

    /**
     * 导入队列配置
     */
    importQueues(queuesData) {
        try {
            if (Array.isArray(queuesData)) {
                if (!taskManager.currentTask) {
                    alert('请先选择或创建一个任务');
                    return false;
                }

                taskManager.currentTask.queues = queuesData;
                this.renderQueues(taskManager.currentTask.queues);
                return true;
            }
        } catch (error) {
            console.error('导入队列配置失败:', error);
        }
        return false;
    }

    /**
     * 导出队列配置
     */
    exportQueues() {
        if (!taskManager.currentTask || !taskManager.currentTask.queues) {
            return [];
        }

        return JSON.parse(JSON.stringify(taskManager.currentTask.queues));
    }

    /**
     * 批量操作队列
     */
    batchOperation(operation) {
        if (!taskManager.currentTask || !taskManager.currentTask.queues) return;

        switch (operation) {
            case 'clear_all':
                if (confirm('确定要清空所有队列配置吗？')) {
                    taskManager.currentTask.queues = [];
                }
                break;
        }

        this.renderQueues(taskManager.currentTask.queues);
    }

    /**
     * 复制队列配置
     */
    duplicateQueue(index) {
        if (!taskManager.currentTask || !taskManager.currentTask.queues) return;

        const originalQueue = taskManager.currentTask.queues[index];
        if (originalQueue) {
            const duplicatedQueue = JSON.parse(JSON.stringify(originalQueue));
            duplicatedQueue.topic = duplicatedQueue.topic + '_copy';

            taskManager.currentTask.queues.splice(index + 1, 0, duplicatedQueue);
            this.renderQueues(taskManager.currentTask.queues);
        }
    }

    /**
     * 移动队列位置
     */
    moveQueue(fromIndex, toIndex) {
        if (!taskManager.currentTask || !taskManager.currentTask.queues) return;

        const queues = taskManager.currentTask.queues;
        if (fromIndex >= 0 && fromIndex < queues.length &&
            toIndex >= 0 && toIndex < queues.length) {

            const [movedQueue] = queues.splice(fromIndex, 1);
            queues.splice(toIndex, 0, movedQueue);

            this.renderQueues(queues);
        }
    }

    /**
     * 获取可用的Topic建议
     */
    getTopicSuggestions() {
        // 这里可以从历史记录或配置中获取Topic建议
        return [
            'orders.new',
            'orders.updated',
            'orders.cancelled',
            'inventory.update',
            'user.registered',
            'user.login',
            'payment.success',
            'payment.failed',
            'notification.email',
            'notification.sms'
        ];
    }

    /**
     * 清空队列配置
     */
    clearQueues() {
        const container = document.getElementById('queuesContainer');
        if (container) {
            container.innerHTML = '<p style="text-align: center; color: #95a5a6;">暂无队列配置，点击"添加队列"开始配置</p>';
        }
    }

    /**
     * 强制重新渲染
     */
    forceRerender() {
        // 重新加载模板列表
        this.loadTemplates().then(() => {
            // 如果有当前任务，重新渲染队列
            if (taskManager.currentTask && taskManager.currentTask.queues) {
                this.renderQueues(taskManager.currentTask.queues);
            } else {
                this.clearQueues();
            }
        });
    }
}

// 创建全局队列管理器实例
window.queueManager = new QueueManager();
