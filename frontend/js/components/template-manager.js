/**
 * 模板管理组件
 */
class TemplateManager {
    constructor() {
        this.templates = [];
        this.currentEditingTemplate = null;
        this.codeEditor = null;
        this.previewEnvVars = {}; // 临时预览环境变量
        this.environments = []; // 环境列表
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadTemplates();
    }

    bindEvents() {
        // 模板管理按钮
        document.getElementById('tpl-management-btn').addEventListener('click', () => {
            this.showTemplateModal();
        });
    }

    /**
     * 加载模板列表
     */
    async loadTemplates() {
        try {
            this.templates = await api.getTemplates();
            // 通知队列管理器更新模板选项
            if (window.queueManager) {
                queueManager.templates = this.templates;
                if (taskManager.currentTask && taskManager.currentTask.queues) {
                    queueManager.renderQueues(taskManager.currentTask.queues);
                }
            }
        } catch (error) {
            console.error('加载模板列表失败:', error);
            this.templates = [];
        }
    }

    /**
     * 显示模板管理模态框
     */
    showTemplateModal() {
        const modal = document.getElementById('tplManagementModal');
        this.currentEditingTemplate = null;

        // 防止页面滚动
        document.body.classList.add('modal-open');

        // 渲染模板列表
        this.renderTemplateList();

        // 清空编辑器
        this.clearTemplateEditor();

        // 初始化CodeMirror
        this.initCodeEditor();

        // 绑定事件
        this.bindModalEvents();

        modal.style.display = 'block';

        // 设置侧边栏激活状态
        this.setActiveSidebarItem();
    }

    /**
     * 渲染模板列表
     */
    renderTemplateList() {
        const tplList = document.getElementById('tplList');

        if (this.templates.length === 0) {
            tplList.innerHTML = '<div class="modal-empty-state">暂无模板配置</div>';
            return;
        }

        tplList.innerHTML = this.templates.map(template => `
            <div class="tpl-list-item" data-tpl-id="${template.id}">
                <span class="item-name">${template.name}</span>
                <span class="item-actions">
                    <button title="复制" onclick="templateManager.duplicateTemplate(${template.id})">📋</button>
                    <button title="删除" onclick="templateManager.deleteTemplate(${template.id})">🗑</button>
                </span>
            </div>
        `).join('');

        // 绑定点击事件
        tplList.querySelectorAll('.tpl-list-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.tagName === 'BUTTON') return;

                const tplId = parseInt(item.dataset.tplId);
                this.selectTemplateInModal(tplId);
            });
        });
    }

    /**
     * 在模态框中选择模板
     */
    selectTemplateInModal(tplId) {
        // 更新激活状态
        document.querySelectorAll('.tpl-list-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tpl-id="${tplId}"]`).classList.add('active');

        // 加载模板到编辑器
        const template = this.templates.find(t => t.id === tplId);
        if (template) {
            this.loadTemplateToEditor(template);
            this.currentEditingTemplate = template;
        }
    }

    /**
     * 初始化CodeMirror编辑器
     */
    initCodeEditor() {
        if (this.codeEditor) {
            return; // 已经初始化过了
        }

        const textarea = document.getElementById('tplContentTextarea');
        if (!textarea) {
            console.error('找不到代码编辑器textarea元素');
            return;
        }

        this.codeEditor = CodeMirror.fromTextArea(textarea, {
            mode: 'python',
            theme: 'default',
            lineNumbers: true,
            lineWrapping: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            styleActiveLine: true,
            foldGutter: true,
            gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
            extraKeys: {
                'Ctrl-Space': 'autocomplete',
                'F11': function(cm) {
                    cm.setOption('fullScreen', !cm.getOption('fullScreen'));
                },
                'Esc': function(cm) {
                    if (cm.getOption('fullScreen')) cm.setOption('fullScreen', false);
                }
            },
            placeholder: `def generate_message(env, device_id, device_ip, index, timestamp):
    """
    生成自定义消息

    :param env: 环境变量字典，包含所有必要的参数
    :param device_id: 设备ID
    :param device_ip: 设备IP地址
    :param index: 消息索引
    :param timestamp: 时间戳
    :return: 消息字典
    """
    import random
    import json
    import time

    # 在这里编写消息生成逻辑
    message = {
        'id': index,
        'timestamp': timestamp,
        'device_id': device_id,
        'device_ip': device_ip,
        'data': 'your message data'
    }

    return message`
        });

        // 绑定主题选择器
        const themeSelector = document.getElementById('themeSelector');
        if (themeSelector) {
            themeSelector.addEventListener('change', (e) => {
                this.codeEditor.setOption('theme', e.target.value);
            });
        }

        // 设置编辑器高度
        this.codeEditor.setSize(null, '300px');
    }

    /**
     * 加载模板到编辑器
     */
    loadTemplateToEditor(template) {
        document.getElementById('tplName').value = template.name || '';
        document.getElementById('tplTopic').value = template.topic || '';

        if (this.codeEditor) {
            this.codeEditor.setValue(template.python_code || '');
        } else {
            document.getElementById('tplContentTextarea').value = template.python_code || '';
        }

        // 加载模板的环境变量
        if (template.env_vars) {
            try {
                let envVars = template.env_vars;
                if (typeof envVars === 'string') {
                    envVars = JSON.parse(envVars);
                }
                this.previewEnvVars = envVars;

                // 更新环境变量按钮状态
                const envBtn = document.getElementById('previewEnvBtn');
                if (envBtn) {
                    if (Object.keys(envVars).length > 0) {
                        envBtn.style.background = '#27ae60';
                        envBtn.title = '环境变量已配置';
                    } else {
                        envBtn.style.background = '#95a5a6';
                        envBtn.title = '配置预览环境变量';
                    }
                }
            } catch (e) {
                console.error('解析模板环境变量失败:', e);
                this.previewEnvVars = {};
            }
        } else {
            this.previewEnvVars = {};

            // 重置环境变量按钮状态
            const envBtn = document.getElementById('previewEnvBtn');
            if (envBtn) {
                envBtn.style.background = '#95a5a6';
                envBtn.title = '配置预览环境变量';
            }
        }
    }

    /**
     * 清空模板编辑器
     */
    clearTemplateEditor() {
        document.getElementById('tplName').value = '';
        document.getElementById('tplTopic').value = '';

        if (this.codeEditor) {
            this.codeEditor.setValue('');
        } else {
            document.getElementById('tplContentTextarea').value = '';
        }

        // 清空环境变量
        this.previewEnvVars = {};

        // 重置环境变量按钮状态
        const envBtn = document.getElementById('previewEnvBtn');
        if (envBtn) {
            envBtn.style.background = '#95a5a6';
            envBtn.title = '配置预览环境变量';
        }
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        // 新建模板按钮
        document.getElementById('newTplBtn').onclick = () => {
            this.createTemplate();
        };

        // 保存按钮
        document.getElementById('saveTplBtn').onclick = () => {
            this.saveCurrentTemplate();
        };

        // 预览环境变量按钮
        document.getElementById('previewEnvBtn').onclick = () => {
            this.showPreviewEnvModal();
        };

        // 预览按钮
        document.getElementById('previewTplBtn').onclick = () => {
            this.previewCurrentTemplate();
        };

        // 导入按钮
        document.getElementById('importTplBtn').onclick = () => {
            this.importTemplate();
        };

        // 关闭按钮
        document.querySelectorAll('[data-modal-id="tplManagementModal"]').forEach(btn => {
            btn.onclick = () => {
                this.closeModal();
            };
        });

        // 预览弹窗关闭按钮
        document.querySelectorAll('[data-modal-id="previewModal"]').forEach(btn => {
            btn.onclick = () => {
                this.closePreviewModal();
            };
        });

        // 预览环境变量弹窗关闭按钮
        document.querySelectorAll('[data-modal-id="previewEnvModal"]').forEach(btn => {
            btn.onclick = () => {
                this.closePreviewEnvModal();
            };
        });

        // 保存预览环境变量按钮
        document.getElementById('savePreviewEnvBtn').onclick = () => {
            this.savePreviewEnvVars();
        };
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        document.getElementById('tplManagementModal').style.display = 'none';
        document.body.classList.remove('modal-open');
    }

    /**
     * 关闭预览弹窗
     */
    closePreviewModal() {
        document.getElementById('previewModal').style.display = 'none';
    }

    /**
     * 关闭预览环境变量弹窗
     */
    closePreviewEnvModal() {
        document.getElementById('previewEnvModal').style.display = 'none';
    }

    /**
     * 设置侧边栏激活状态
     */
    setActiveSidebarItem() {
        // 移除所有激活状态
        document.querySelectorAll('.task-list li, .management-list li').forEach(item => {
            item.classList.remove('active');
        });

        // 激活模板管理项
        document.getElementById('tpl-management-btn').classList.add('active');
    }

    /**
     * 创建模板
     */
    createTemplate() {
        this.currentEditingTemplate = null;
        this.clearTemplateEditor();

        // 移除列表中的激活状态
        document.querySelectorAll('.tpl-list-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    /**
     * 编辑模板
     */
    async editTemplate(templateId) {
        try {
            const template = await api.getTemplate(templateId);
            this.showTemplateForm(template);
        } catch (error) {
            console.error('加载模板详情失败:', error);
            this.showError('加载模板详情失败');
        }
    }

    /**
     * 显示模板表单
     */
    showTemplateForm(template = null) {
        const modalBody = document.getElementById('modalBody');
        const isEdit = template !== null;

        modalBody.innerHTML = `
            <h3>${isEdit ? '编辑' : '新建'}模板</h3>
            <form id="templateForm">
                <div class="form-group">
                    <label for="templateName">模板名称</label>
                    <input type="text" id="templateName" value="${template ? template.name : ''}" required>
                </div>
                <div class="form-group">
                    <label for="templateDescription">描述</label>
                    <input type="text" id="templateDescription" value="${template ? template.description || '' : ''}">
                </div>
                <div class="form-group">
                    <label for="templateTopic">默认Topic</label>
                    <input type="text" id="templateTopic" value="${template ? template.topic || '' : ''}"
                           placeholder="例如: orders.new">
                </div>
                <div class="form-group">
                    <label for="templateCode">Python代码</label>
                    <textarea id="templateCode" rows="15" required placeholder="def generate_message(env, device_id, device_ip, index, timestamp):
    # 在这里编写消息生成逻辑
    return {
        'id': index,
        'timestamp': timestamp,
        'data': 'your message data'
    }">${template ? template.python_code || '' : ''}</textarea>
                </div>
                <div style="margin-top: 20px;">
                    <button type="button" onclick="templateManager.testCurrentTemplate()"
                            style="margin-right: 10px; background-color: #f39c12;">测试代码</button>
                    <button type="submit" style="margin-right: 10px;">保存</button>
                    <button type="button" onclick="templateManager.showTemplateModal()">取消</button>
                </div>
            </form>
            <div id="testResult" style="margin-top: 20px; display: none;">
                <h4>测试结果</h4>
                <pre id="testOutput" style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;"></pre>
            </div>
        `;

        // 绑定表单提交事件
        document.getElementById('templateForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveTemplate(template ? template.id : null);
        });
    }

    /**
     * 显示预览环境变量弹窗
     */
    async showPreviewEnvModal() {
        // 显示当前的预览环境变量
        const previewEnvVarsTextarea = document.getElementById('previewEnvVars');
        if (previewEnvVarsTextarea && this.previewEnvVars && Object.keys(this.previewEnvVars).length > 0) {
            previewEnvVarsTextarea.value = JSON.stringify(this.previewEnvVars, null, 2);
        } else if (previewEnvVarsTextarea) {
            previewEnvVarsTextarea.value = '';
        }

        // 显示当前模板名称
        const currentTemplateNameSpan = document.getElementById('currentTemplateName');
        if (currentTemplateNameSpan) {
            if (this.currentEditingTemplate) {
                currentTemplateNameSpan.textContent = `当前模板: ${this.currentEditingTemplate.name}`;
            } else {
                currentTemplateNameSpan.textContent = '新建模板';
            }
        }

        // 显示弹窗
        document.getElementById('previewEnvModal').style.display = 'block';
    }

    /**
     * 保存预览环境变量
     */
    savePreviewEnvVars() {
        const envVarsText = document.getElementById('previewEnvVars').value.trim();

        if (envVarsText) {
            try {
                this.previewEnvVars = JSON.parse(envVarsText);
                this.closePreviewEnvModal();

                // 更新按钮样式表示已配置
                const envBtn = document.getElementById('previewEnvBtn');
                envBtn.style.background = '#27ae60';
                envBtn.title = '环境变量已配置';
            } catch (error) {
                alert('环境变量JSON格式错误，请检查语法');
            }
        } else {
            this.previewEnvVars = {};
            this.closePreviewEnvModal();

            // 重置按钮样式
            const envBtn = document.getElementById('previewEnvBtn');
            envBtn.style.background = '#95a5a6';
            envBtn.title = '配置预览环境变量';
        }
    }

    /**
     * 预览当前模板代码
     */
    async previewCurrentTemplate() {
        const code = this.codeEditor ? this.codeEditor.getValue() : document.getElementById('tplContentTextarea').value;

        if (!code.trim()) {
            alert('请输入Python代码');
            return;
        }

        const previewData = {
            code: code,
            env: this.previewEnvVars  // 使用配置的环境变量
        };

        try {
            const result = await api.previewTemplate(previewData);
            this.showPreviewResult(result);
        } catch (error) {
            console.error('预览模板失败:', error);
            this.showPreviewResult({
                success: false,
                error: error.message || '预览失败'
            });
        }
    }

    /**
     * 保存当前模板
     */
    async saveCurrentTemplate() {
        const name = document.getElementById('tplName').value.trim();
        const topic = document.getElementById('tplTopic').value.trim();
        const pythonCode = this.codeEditor ? this.codeEditor.getValue().trim() : document.getElementById('tplContentTextarea').value.trim();

        if (!name) {
            alert('请输入模板名称');
            return;
        }

        if (!pythonCode) {
            alert('请输入Python代码');
            return;
        }

        const templateData = {
            name,
            topic,
            code: pythonCode,
            env_vars: JSON.stringify(this.previewEnvVars) // 保存预览环境变量到模板
        };

        try {
            if (this.currentEditingTemplate && this.currentEditingTemplate.id) {
                await api.updateTemplate(this.currentEditingTemplate.id, templateData);
                this.showSuccess('模板更新成功');
            } else {
                const result = await api.createTemplate(templateData);
                this.showSuccess('模板创建成功');
                this.currentEditingTemplate = result;
            }

            await this.loadTemplates();
            this.renderTemplateList();

            // 如果是新建的模板，选中它
            if (this.currentEditingTemplate && this.currentEditingTemplate.id) {
                this.selectTemplateInModal(this.currentEditingTemplate.id);
            }
        } catch (error) {
            console.error('保存模板失败:', error);
            this.showError('保存模板失败');
        }
    }

    /**
     * 复制模板
     */
    async duplicateTemplate(tplId) {
        const template = this.templates.find(t => t.id === tplId);
        if (!template) return;

        const newTemplateData = {
            name: template.name + ' (副本)',
            topic: template.topic,
            python_code: template.python_code
        };

        try {
            await api.createTemplate(newTemplateData);
            this.showSuccess('模板复制成功');
            await this.loadTemplates();
            this.renderTemplateList();
        } catch (error) {
            console.error('复制模板失败:', error);
            this.showError('复制模板失败');
        }
    }

    /**
     * 导入模板
     */
    importTemplate() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const templateData = JSON.parse(e.target.result);
                        this.loadTemplateToEditor(templateData);
                        this.currentEditingTemplate = null; // 标记为新模板
                        this.showSuccess('模板数据导入成功，请检查后保存');
                    } catch (error) {
                        this.showError('模板文件格式错误');
                    }
                };
                reader.readAsText(file);
            }
        };

        input.click();
    }

    /**
     * 显示预览结果
     */
    showPreviewResult(result) {
        const previewContent = document.getElementById('previewContent');
        const previewModal = document.getElementById('previewModal');

        if (!previewContent || !previewModal) {
            console.error('找不到预览弹窗元素');
            return;
        }

        if (result.success) {
            const content = `✅ 预览成功！

📋 生成的消息:
${JSON.stringify(result.message, null, 2)}

💡 说明:
这是模板的预览模式，显示了模板生成的消息结构。
要测试模板的实际执行效果，请保存模板后在任务管理中使用。`;

            previewContent.textContent = content;
            previewContent.className = 'success';
        } else {
            const content = `❌ 预览失败

🐛 错误信息:
${result.error}

💡 建议:
请检查Python代码语法是否正确，确保所有变量都已定义。`;

            previewContent.textContent = content;
            previewContent.className = 'error';
        }

        // 显示弹窗
        previewModal.style.display = 'block';
    }

    /**
     * 保存模板
     */
    async saveTemplate(templateId) {
        const name = document.getElementById('templateName').value.trim();
        const description = document.getElementById('templateDescription').value.trim();
        const topic = document.getElementById('templateTopic').value.trim();
        const pythonCode = document.getElementById('templateCode').value.trim();

        if (!name) {
            alert('请输入模板名称');
            return;
        }

        if (!pythonCode) {
            alert('请输入Python代码');
            return;
        }

        const templateData = {
            name,
            description,
            topic,
            python_code: pythonCode
        };

        try {
            if (templateId) {
                await api.updateTemplate(templateId, templateData);
                this.showSuccess('模板更新成功');
            } else {
                await api.createTemplate(templateData);
                this.showSuccess('模板创建成功');
            }

            await this.loadTemplates();
            this.showTemplateModal();
        } catch (error) {
            console.error('保存模板失败:', error);
            this.showError('保存模板失败');
        }
    }

    /**
     * 测试模板
     */
    async testTemplate(templateId) {
        try {
            const template = await api.getTemplate(templateId);
            const result = await api.testTemplate(template);

            const modal = document.getElementById('modal');
            const modalBody = document.getElementById('modalBody');

            modalBody.innerHTML = `
                <h3>模板测试结果</h3>
                <h4>${template.name}</h4>
                <div style="margin: 20px 0;">
                    ${result.success ?
                        `<div style="color: #27ae60;">
                            <strong>✓ 测试成功</strong>
                            <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 10px; max-height: 300px; overflow-y: auto;">${JSON.stringify(result.message, null, 2)}</pre>
                        </div>` :
                        `<div style="color: #c0392b;">
                            <strong>✗ 测试失败</strong>
                            <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 10px; max-height: 300px; overflow-y: auto;">${result.error}</pre>
                        </div>`
                    }
                </div>
                <button onclick="templateManager.showTemplateModal()">返回</button>
            `;

            modal.style.display = 'block';
        } catch (error) {
            console.error('测试模板失败:', error);
            this.showError('测试模板失败');
        }
    }

    /**
     * 删除模板
     */
    async deleteTemplate(templateId) {
        if (!confirm('确定要删除这个模板吗？')) return;

        try {
            await api.deleteTemplate(templateId);
            this.showSuccess('模板删除成功');
            await this.loadTemplates();
            this.showTemplateModal();
        } catch (error) {
            console.error('删除模板失败:', error);
            this.showError('删除模板失败');
        }
    }

    /**
     * 获取模板代码示例
     */
    getTemplateExamples() {
        return {
            'simple': `def generate_message(env, device_id, device_ip, index, timestamp):
    """简单消息模板"""
    return {
        'id': index,
        'timestamp': timestamp,
        'device_id': device_id,
        'message': f'Hello from device {device_id}'
    }`,
            'order': `def generate_message(env, device_id, device_ip, index, timestamp):
    """订单消息模板"""
    import random

    return {
        'order_id': f'ORDER_{timestamp}_{index}',
        'customer_id': f'CUST_{random.randint(1000, 9999)}',
        'amount': round(random.uniform(10.0, 1000.0), 2),
        'status': 'pending',
        'timestamp': timestamp,
        'device_id': device_id
    }`,
            'iot': `def generate_message(env, device_id, device_ip, index, timestamp):
    """IoT设备数据模板"""
    import random

    return {
        'device_id': device_id,
        'device_ip': device_ip,
        'timestamp': timestamp,
        'temperature': round(random.uniform(20.0, 35.0), 1),
        'humidity': round(random.uniform(30.0, 80.0), 1),
        'battery': random.randint(10, 100),
        'signal_strength': random.randint(-100, -30)
    }`
        };
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error(message);
        alert('错误: ' + message);
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log(message);
        // 这里可以实现更好的成功提示UI
    }
}

// 创建全局模板管理器实例
window.templateManager = new TemplateManager();
