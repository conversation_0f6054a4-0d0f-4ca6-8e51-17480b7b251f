/**
 * 环境管理组件
 */
class EnvironmentManager {
    constructor() {
        this.environments = [];
        this.currentEnvironment = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadEnvironments();
    }

    bindEvents() {
        // 环境选择变化
        document.getElementById('environmentSelect').addEventListener('change', (e) => {
            this.selectEnvironment(e.target.value);
        });

        // 环境管理按钮
        document.getElementById('env-management-btn').addEventListener('click', () => {
            this.showEnvironmentModal();
        });
    }

    /**
     * 加载环境列表
     */
    async loadEnvironments() {
        try {
            const response = await api.getEnvironments();

            // 处理不同的响应格式
            if (response && response.success && response.environments) {
                this.environments = response.environments;
            } else if (Array.isArray(response)) {
                this.environments = response;
            } else if (response && Array.isArray(response.data)) {
                this.environments = response.data;
            } else {
                this.environments = [];
            }

            this.renderEnvironmentSelect();

            // 如果当前任务有环境ID，选择对应环境
            if (taskManager.currentTask && taskManager.currentTask.environment_id) {
                this.selectEnvironment(taskManager.currentTask.environment_id);
            }
        } catch (error) {
            console.error('加载环境列表失败:', error);
            this.environments = [];
            this.renderEnvironmentSelect();
        }
    }

    /**
     * 渲染环境选择下拉框
     */
    renderEnvironmentSelect() {
        const select = document.getElementById('environmentSelect');
        if (!select) return;

        select.innerHTML = '<option value="">选择环境...</option>';

        this.environments.forEach(env => {
            const option = document.createElement('option');
            option.value = env.id;
            option.textContent = `${env.name} (${env.description || 'No description'})`;

            // 如果当前任务有环境ID且匹配，设置为选中
            if (taskManager.currentTask && taskManager.currentTask.environment_id == env.id) {
                option.selected = true;
            }

            select.appendChild(option);
        });

        // 如果有选中的环境，触发预览更新
        if (select.value) {
            this.selectEnvironment(select.value);
        }
    }

    /**
     * 选择环境
     */
    async selectEnvironment(envId) {
        if (!envId) {
            this.currentEnvironment = null;
            this.renderEnvironmentPreview();
            return;
        }

        try {
            this.currentEnvironment = await api.getEnvironment(envId);
            this.renderEnvironmentPreview();

            // 更新当前任务的环境ID
            if (taskManager.currentTask) {
                taskManager.currentTask.environment_id = parseInt(envId);
            }
        } catch (error) {
            console.error('加载环境详情失败:', error);
            this.showError('加载环境详情失败');
        }
    }

    /**
     * 渲染环境变量预览
     */
    renderEnvironmentPreview() {
        const preview = document.getElementById('envVariablesPreview');

        if (!this.currentEnvironment) {
            preview.value = '请选择一个环境查看变量。';
            return;
        }

        let previewText = '';
        if (this.currentEnvironment.variables) {
            if (Array.isArray(this.currentEnvironment.variables)) {
                // 如果是数组格式
                previewText = this.currentEnvironment.variables
                    .map(v => `${v.key}=${v.value}`)
                    .join('\n');
            } else if (typeof this.currentEnvironment.variables === 'object') {
                // 如果是对象格式
                previewText = Object.entries(this.currentEnvironment.variables)
                    .map(([key, value]) => `${key}=${value}`)
                    .join('\n');
            }
        }

        preview.value = previewText || '该环境暂无变量配置。';
    }

    /**
     * 显示环境管理模态框
     */
    showEnvironmentModal() {
        const modal = document.getElementById('envManagementModal');
        this.currentEditingEnv = null;

        // 防止页面滚动
        document.body.classList.add('modal-open');

        // 渲染环境列表
        this.renderEnvironmentList();

        // 清空编辑器
        this.clearEnvironmentEditor();

        // 绑定事件
        this.bindModalEvents();

        modal.style.display = 'block';

        // 设置侧边栏激活状态
        this.setActiveSidebarItem();
    }

    /**
     * 渲染环境列表
     */
    renderEnvironmentList() {
        const envList = document.getElementById('envList');

        if (this.environments.length === 0) {
            envList.innerHTML = '<div class="modal-empty-state">暂无环境配置</div>';
            return;
        }

        envList.innerHTML = this.environments.map(env => `
            <div class="env-list-item" data-env-id="${env.id}">
                <span class="item-name">${env.name}</span>
                <span class="item-actions">
                    <button title="复制" onclick="environmentManager.duplicateEnvironment(${env.id})">📋</button>
                    <button title="删除" onclick="environmentManager.deleteEnvironment(${env.id})">🗑</button>
                </span>
            </div>
        `).join('');

        // 绑定点击事件
        envList.querySelectorAll('.env-list-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.tagName === 'BUTTON') return;

                const envId = parseInt(item.dataset.envId);
                this.selectEnvironmentInModal(envId);
            });
        });
    }

    /**
     * 获取变量数量
     */
    getVariableCount(variables) {
        if (!variables) return 0;
        if (Array.isArray(variables)) return variables.length;
        if (typeof variables === 'object') return Object.keys(variables).length;
        return 0;
    }

    /**
     * 在模态框中选择环境
     */
    selectEnvironmentInModal(envId) {
        // 更新激活状态
        document.querySelectorAll('.env-list-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-env-id="${envId}"]`).classList.add('active');

        // 加载环境到编辑器
        const env = this.environments.find(e => e.id === envId);
        if (env) {
            this.loadEnvironmentToEditor(env);
            this.currentEditingEnv = env;
        }
    }

    /**
     * 加载环境到编辑器
     */
    loadEnvironmentToEditor(env) {
        document.getElementById('envName').value = env.name || '';
        document.getElementById('envDesc').value = env.description || '';
        document.getElementById('envVarsTextarea').value = this.formatVariablesForEdit(env.variables);
    }

    /**
     * 清空环境编辑器
     */
    clearEnvironmentEditor() {
        document.getElementById('envName').value = '';
        document.getElementById('envDesc').value = '';
        document.getElementById('envVarsTextarea').value = '';
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        // 新建环境按钮
        document.getElementById('newEnvBtn').onclick = () => {
            this.createEnvironment();
        };

        // 保存按钮
        document.getElementById('saveEnvBtn').onclick = () => {
            this.saveCurrentEnvironment();
        };

        // 导入按钮
        document.getElementById('importEnvBtn').onclick = () => {
            this.importEnvironment();
        };

        // 关闭按钮
        document.querySelectorAll('[data-modal-id="envManagementModal"]').forEach(btn => {
            btn.onclick = () => {
                this.closeModal();
            };
        });
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        document.getElementById('envManagementModal').style.display = 'none';
        document.body.classList.remove('modal-open');
    }

    /**
     * 设置侧边栏激活状态
     */
    setActiveSidebarItem() {
        // 移除所有激活状态
        document.querySelectorAll('.task-list li, .management-list li').forEach(item => {
            item.classList.remove('active');
        });

        // 激活环境管理项
        document.getElementById('env-management-btn').classList.add('active');
    }

    /**
     * 创建环境
     */
    createEnvironment() {
        this.currentEditingEnv = null;
        this.clearEnvironmentEditor();

        // 移除列表中的激活状态
        document.querySelectorAll('.env-list-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    /**
     * 编辑环境
     */
    async editEnvironment(envId) {
        try {
            const env = await api.getEnvironment(envId);
            this.showEnvironmentForm(env);
        } catch (error) {
            console.error('加载环境详情失败:', error);
            this.showError('加载环境详情失败');
        }
    }

    /**
     * 显示环境表单
     */
    showEnvironmentForm(env = null) {
        const modalBody = document.getElementById('modalBody');
        const isEdit = env !== null;

        modalBody.innerHTML = `
            <h3>${isEdit ? '编辑' : '新建'}环境</h3>
            <form id="environmentForm">
                <div class="form-group">
                    <label for="envName">环境名称</label>
                    <input type="text" id="envName" value="${env ? env.name : ''}" required>
                </div>
                <div class="form-group">
                    <label for="envDescription">描述</label>
                    <input type="text" id="envDescription" value="${env ? env.description || '' : ''}">
                </div>
                <div class="form-group">
                    <label for="envVariables">环境变量 (每行一个，格式: KEY=VALUE)</label>
                    <textarea id="envVariables" rows="10" placeholder="KAFKA_BROKERS=localhost:9092\nSCHEMA_REGISTRY_URL=http://localhost:8081">${this.formatVariablesForEdit(env ? env.variables : null)}</textarea>
                </div>
                <div style="margin-top: 20px;">
                    <button type="submit" style="margin-right: 10px;">保存</button>
                    <button type="button" onclick="environmentManager.showEnvironmentModal()">取消</button>
                </div>
            </form>
        `;

        // 绑定表单提交事件
        document.getElementById('environmentForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveEnvironment(env ? env.id : null);
        });
    }

    /**
     * 格式化变量用于编辑
     */
    formatVariablesForEdit(variables) {
        if (!variables) return '';

        if (Array.isArray(variables)) {
            return variables.map(v => `${v.key}=${v.value}`).join('\n');
        } else if (typeof variables === 'object') {
            return Object.entries(variables).map(([key, value]) => `${key}=${value}`).join('\n');
        }

        return '';
    }

    /**
     * 解析变量文本
     */
    parseVariablesText(text) {
        const variables = {};
        const lines = text.split('\n').filter(line => line.trim());

        lines.forEach(line => {
            const [key, ...valueParts] = line.split('=');
            if (key && key.trim()) {
                variables[key.trim()] = valueParts.join('=').trim();
            }
        });

        return variables;
    }

    /**
     * 保存当前环境
     */
    async saveCurrentEnvironment() {
        const name = document.getElementById('envName').value.trim();
        const description = document.getElementById('envDesc').value.trim();
        const variablesText = document.getElementById('envVarsTextarea').value;

        if (!name) {
            alert('请输入环境名称');
            return;
        }

        const variables = this.parseVariablesText(variablesText);

        const envData = {
            name,
            description,
            variables
        };

        try {
            if (this.currentEditingEnv && this.currentEditingEnv.id) {
                await api.updateEnvironment(this.currentEditingEnv.id, envData);
                this.showSuccess('环境更新成功');
            } else {
                const result = await api.createEnvironment(envData);
                this.showSuccess('环境创建成功');
                this.currentEditingEnv = result;
            }

            await this.loadEnvironments();
            this.renderEnvironmentList();

            // 如果是新建的环境，选中它
            if (this.currentEditingEnv && this.currentEditingEnv.id) {
                this.selectEnvironmentInModal(this.currentEditingEnv.id);
            }
        } catch (error) {
            console.error('保存环境失败:', error);
            this.showError('保存环境失败');
        }
    }

    /**
     * 复制环境
     */
    async duplicateEnvironment(envId) {
        const env = this.environments.find(e => e.id === envId);
        if (!env) return;

        const newEnvData = {
            name: env.name + ' (副本)',
            description: env.description,
            variables: env.variables
        };

        try {
            await api.createEnvironment(newEnvData);
            this.showSuccess('环境复制成功');
            await this.loadEnvironments();
            this.renderEnvironmentList();
        } catch (error) {
            console.error('复制环境失败:', error);
            this.showError('复制环境失败');
        }
    }

    /**
     * 导入环境
     */
    importEnvironment() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const envData = JSON.parse(e.target.result);
                        this.loadEnvironmentToEditor(envData);
                        this.currentEditingEnv = null; // 标记为新环境
                        this.showSuccess('环境数据导入成功，请检查后保存');
                    } catch (error) {
                        this.showError('环境文件格式错误');
                    }
                };
                reader.readAsText(file);
            }
        };

        input.click();
    }

    /**
     * 删除环境
     */
    async deleteEnvironment(envId) {
        if (!confirm('确定要删除这个环境吗？')) return;

        try {
            await api.deleteEnvironment(envId);
            this.showSuccess('环境删除成功');
            await this.loadEnvironments();
            this.showEnvironmentModal();
        } catch (error) {
            console.error('删除环境失败:', error);
            this.showError('删除环境失败');
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error(message);
        alert('错误: ' + message);
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log(message);
        // 这里可以实现更好的成功提示UI
    }

    /**
     * 清空环境预览
     */
    clearPreview() {
        const previewContent = document.getElementById('previewContent');
        if (previewContent) {
            previewContent.innerHTML = '<p style="text-align: center; color: #95a5a6;">请选择环境查看变量</p>';
            previewContent.className = 'preview-content';
        }
    }

    /**
     * 强制重新渲染
     */
    async forceRerender() {
        // 重新加载环境列表
        await this.loadEnvironments();

        // 清空当前选择
        this.selectedEnvironmentId = null;

        // 重新渲染环境选择器
        this.renderEnvironmentSelector();

        // 清空预览
        this.clearPreview();
    }
}

// 创建全局环境管理器实例
window.environmentManager = new EnvironmentManager();
