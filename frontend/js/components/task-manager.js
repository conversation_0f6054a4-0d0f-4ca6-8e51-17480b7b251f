/**
 * 任务管理组件
 */
class TaskManager {
    constructor() {
        this.currentTask = null;
        this.tasks = [];
        this.runningTasks = new Map();
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadTasks();
        this.startPolling();
        this.startRunningTasksPolling(); // 开始轮询运行状态
    }

    bindEvents() {
        // 运行按钮
        document.getElementById('runBtn').addEventListener('click', () => {
            if (this.isRunning) {
                this.stopCurrentTask();
            } else {
                this.runCurrentTask();
            }
        });

        // 保存任务按钮
        document.getElementById('saveTaskBtn').addEventListener('click', () => {
            this.saveCurrentTaskWithFeedback();
        });

        // 任务名称输入框变化
        document.getElementById('taskNameInput').addEventListener('input', (e) => {
            if (this.currentTask) {
                this.currentTask.name = e.target.value;
                this.updateTaskInList(this.currentTask);
            }
        });

        // 任务描述输入框变化
        document.getElementById('taskDescription').addEventListener('input', (e) => {
            if (this.currentTask) {
                this.currentTask.description = e.target.value;
            }
        });

        // 周期时长输入框变化
        document.getElementById('cycleDuration').addEventListener('input', (e) => {
            if (this.currentTask) {
                this.currentTask.cycle_duration = parseInt(e.target.value) || 60;
            }
        });

        // 总运行时长输入框变化
        document.getElementById('totalDuration').addEventListener('input', (e) => {
            if (this.currentTask) {
                this.currentTask.total_duration = parseInt(e.target.value) || 0;
            }
        });
    }

    /**
     * 开始轮询运行任务状态
     */
    startRunningTasksPolling() {
        // 立即检查一次
        this.checkRunningTasks();

        // 每5秒检查一次运行状态
        this.runningTasksInterval = setInterval(() => {
            this.checkRunningTasks();
        }, 5000);
    }

    /**
     * 检查正在运行的任务
     */
    async checkRunningTasks() {
        try {
            const response = await fetch('/task/api/running_tasks');
            const data = await response.json();

            // 更新运行任务状态
            this.runningTasks.clear();

            // data.tasks 是一个对象，键是run_id，值是任务信息
            if (data.success && data.tasks) {
                Object.entries(data.tasks).forEach(([runId, task]) => {
                    // 提取任务ID
                    let taskId;
                    if (runId.startsWith('ws_')) {
                        // WebSocket任务：ws_3 -> 3
                        taskId = parseInt(runId.replace('ws_', ''));
                    } else {
                        // 传统任务：使用task_id字段
                        taskId = task.task_id;
                    }

                    if (taskId) {
                        this.runningTasks.set(taskId, {
                            run_id: runId,
                            status: task.status,
                            message_count: task.message_count,
                            cycle_count: task.cycle_count,
                            error_count: task.error_count
                        });
                    }
                });
            }

            // 更新当前任务的运行状态
            if (this.currentTask && this.currentTask.id) {
                const isCurrentTaskRunning = this.runningTasks.has(this.currentTask.id);
                if (isCurrentTaskRunning !== this.isRunning) {
                    this.isRunning = isCurrentTaskRunning;
                    if (isCurrentTaskRunning) {
                        const taskInfo = this.runningTasks.get(this.currentTask.id);
                        this.currentRunId = taskInfo.run_id;
                    } else {
                        this.currentRunId = null;
                    }
                    this.updateRunButton();
                }
            }

            // 重新渲染任务列表以更新按钮状态
            this.renderTaskList();

        } catch (error) {
            console.error('检查运行任务状态失败:', error);
        }
    }

    /**
     * 加载任务列表
     */
    async loadTasks() {
        try {
            this.tasks = await api.getTasks();
            this.renderTaskList();

            // 如果有任务，选择第一个（或者URL参数指定的任务）
            if (this.tasks.length > 0 && !this.currentTask) {
                // 检查URL参数是否指定了任务ID
                const urlParams = new URLSearchParams(window.location.search);
                const taskId = urlParams.get('task');

                if (taskId) {
                    const targetTask = this.tasks.find(task => task.id == taskId);
                    if (targetTask) {
                        this.selectTask(targetTask);
                    } else {
                        this.selectTask(this.tasks[0]);
                    }
                } else {
                    this.selectTask(this.tasks[0]);
                }
            }
        } catch (error) {
            console.error('加载任务列表失败:', error);
            this.showError('加载任务列表失败');
        }
    }

    /**
     * 渲染任务列表
     */
    renderTaskList() {
        const taskList = document.getElementById('taskList');
        taskList.innerHTML = '';

        if (this.tasks.length === 0) {
            taskList.innerHTML = '<li style="text-align: center; color: #95a5a6;">暂无任务</li>';
            return;
        }

        this.tasks.forEach(task => {
            const li = document.createElement('li');
            // 添加task-item类名和data-task-id属性，用于历史记录筛选
            li.className = `task-item ${this.currentTask && this.currentTask.id === task.id ? 'active selected' : ''}`;
            li.setAttribute('data-task-id', task.id);

            // 检查任务是否正在运行（基于后台实际状态）
            const isTaskRunning = this.runningTasks.has(task.id);

            // 根据运行状态设置按钮文本和点击事件
            const runButtonText = isTaskRunning ? '⏸' : '▶';
            const runButtonTitle = isTaskRunning ? '停止' : '运行';
            const runButtonAction = isTaskRunning ?
                `taskManager.stopTaskFromList(${task.id})` :
                `taskManager.runTaskFromList(${task.id})`;

            const taskName = task.name || '未命名任务';

            li.innerHTML = `
                <span class="task-name" title="${taskName}">${taskName}</span>
                <span class="task-actions">
                    <button title="${runButtonTitle}" onclick="${runButtonAction}" class="${isTaskRunning ? 'running' : ''}">${runButtonText}</button>
                    <button title="复制" onclick="taskManager.duplicateTask(${task.id})">❏</button>
                    <button title="删除" onclick="taskManager.deleteTask(${task.id})">🗑</button>
                </span>
            `;

            li.addEventListener('click', (e) => {
                if (e.target.tagName === 'BUTTON') return;
                this.selectTask(task);
            });

            taskList.appendChild(li);
        });
    }

    /**
     * 选择任务
     */
    async selectTask(task) {
        try {
            // 如果选择的是当前任务，不需要切换
            if (this.currentTask && this.currentTask.id === task.id) {
                return;
            }

            // 开始切换动效
            this.startTaskSwitchAnimation();

            // 保存当前任务
            if (this.currentTask && this.currentTask.id) {
                await this.saveCurrentTask();
            }

            // 加载任务详情
            if (task.id) {
                this.currentTask = await api.getTask(task.id);
            } else {
                this.currentTask = task;
            }

            // 强制重新渲染所有内容
            await this.forceRerenderContent();

            // 更新选中状态
            this.renderTaskList();

            // 加入WebSocket房间以接收实时日志
            if (this.currentTask && this.currentTask.id && window.webSocketManager) {
                window.webSocketManager.joinTaskRoom(this.currentTask.id);
                // 绑定WebSocket事件处理器
                this.bindWebSocketEvents();
            }

            // 结束切换动效
            this.endTaskSwitchAnimation();

        } catch (error) {
            console.error('选择任务失败:', error);
            this.showError('加载任务详情失败');
            // 确保结束动效
            this.endTaskSwitchAnimation();
        }
    }

    /**
     * 开始任务切换动效
     */
    startTaskSwitchAnimation() {
        // 添加切换状态到主内容区域
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.add('switching');
        }
    }

    /**
     * 结束任务切换动效
     */
    endTaskSwitchAnimation() {
        // 移除切换状态
        setTimeout(() => {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.classList.remove('switching');
            }
        }, 150); // 与CSS动画时间一致
    }

    /**
     * 强制重新渲染所有内容
     */
    async forceRerenderContent() {
        // 清空所有表单内容
        this.clearAllForms();

        // 重新渲染任务表单
        this.renderTaskForm();

        // 重新渲染其他组件
        this.rerenderAllComponents();

        // 重新加载运行历史（如果当前在历史标签页）
        this.reloadHistoryIfActive();

        // 重新加载统计图表（如果当前在图表标签页）
        this.reloadChartsIfActive();
    }

    /**
     * 清空所有表单内容
     */
    clearAllForms() {
        // 清空基本信息
        document.getElementById('taskNameInput').value = '';
        document.getElementById('taskDescription').value = '';
        document.getElementById('cycleDuration').value = '';
        document.getElementById('totalDuration').value = '';

        // 清空环境选择
        const environmentSelect = document.getElementById('environmentSelect');
        if (environmentSelect) {
            environmentSelect.value = '';
        }

        // 清空队列配置
        if (window.queueManager) {
            window.queueManager.clearQueues();
        }

        // 清空环境预览
        if (window.environmentManager) {
            window.environmentManager.clearPreview();
        }

        // 清空控制台输出
        this.clearConsole();
    }

    /**
     * 重新渲染所有组件
     */
    rerenderAllComponents() {
        // 重新渲染队列管理器
        if (window.queueManager) {
            window.queueManager.forceRerender();
        }

        // 重新渲染环境管理器
        if (window.environmentManager) {
            window.environmentManager.forceRerender();
        }
    }

    /**
     * 重新加载运行历史（如果当前在历史标签页）
     */
    reloadHistoryIfActive() {
        // 检查当前是否在运行历史标签页
        const currentTab = window.app && window.app.currentTab;
        if (currentTab === 'run-history') {
            // 如果在历史标签页，重新加载历史记录
            if (window.app && typeof window.app.loadRunHistory === 'function') {
                window.app.loadRunHistory();
            }
        }
    }

    /**
     * 重新加载统计图表（如果当前在图表标签页）
     */
    reloadChartsIfActive() {
        // 检查当前是否在统计图表标签页
        const currentTab = window.app && window.app.currentTab;
        if (currentTab === 'charts') {
            // 如果在图表标签页，重新加载统计图表
            if (window.app && typeof window.app.loadStatisticsCharts === 'function') {
                window.app.loadStatisticsCharts();
            }
        }
    }

    /**
     * 清空控制台输出
     */
    clearConsole() {
        const consoleOutput = document.getElementById('consoleOutput');
        if (consoleOutput) {
            consoleOutput.innerHTML = '';
        }
    }

    /**
     * 渲染任务表单
     */
    renderTaskForm() {
        if (!this.currentTask) return;

        document.getElementById('taskNameInput').value = this.currentTask.name || '';
        document.getElementById('taskDescription').value = this.currentTask.description || '';
        document.getElementById('cycleDuration').value = this.currentTask.cycle_duration || 60;
        document.getElementById('totalDuration').value = this.currentTask.total_duration || 0;

        // 渲染队列配置
        if (window.queueManager) {
            window.queueManager.renderQueues(this.currentTask.queues || []);
        }

        // 渲染环境配置
        if (window.environmentManager) {
            // 设置环境选择器的值
            const environmentSelect = document.getElementById('environmentSelect');
            if (environmentSelect && this.currentTask.environment_id) {
                environmentSelect.value = this.currentTask.environment_id;
                // 触发环境选择事件来更新预览
                window.environmentManager.selectEnvironment(this.currentTask.environment_id);
            } else if (environmentSelect) {
                environmentSelect.value = '';
                window.environmentManager.selectEnvironment('');
            }
        }

        // 更新运行按钮状态
        this.updateRunButton();
    }

    /**
     * 创建新任务（从弹窗）
     */
    async createTaskFromModal(taskData) {
        try {
            const result = await api.createTask(taskData);

            // 创建任务对象
            const newTask = {
                id: result.id,
                name: taskData.name,
                description: taskData.description,
                cycle_duration: taskData.cycle_duration || 60,
                total_duration: taskData.total_duration || 300,
                queues: taskData.queues || [],
                environment_id: taskData.environment_id || null
            };

            // 添加到任务列表
            this.tasks.unshift(newTask);

            // 选择新创建的任务
            this.selectTask(newTask);

            this.showSuccess('任务创建成功');
            return result;
        } catch (error) {
            console.error('创建任务失败:', error);
            this.showError('创建任务失败: ' + error.message);
            throw error;
        }
    }

    /**
     * 保存当前任务
     */
    async saveCurrentTask() {
        if (!this.currentTask) return;

        try {
            const taskData = {
                name: this.currentTask.name,
                description: this.currentTask.description,
                cycle_duration: this.currentTask.cycle_duration,
                total_duration: this.currentTask.total_duration,
                queues: this.currentTask.queues || [],
                environment_id: this.currentTask.environment_id
            };

            if (this.currentTask.id) {
                await api.updateTask(this.currentTask.id, taskData);
            } else {
                const result = await api.createTask(taskData);
                this.currentTask.id = result.id;
            }

            this.updateTaskInList(this.currentTask);
        } catch (error) {
            console.error('保存任务失败:', error);
            this.showError('保存任务失败');
        }
    }

    /**
     * 保存当前任务并提供用户反馈
     */
    async saveCurrentTaskWithFeedback() {
        if (!this.currentTask) {
            this.showError('没有可保存的任务');
            return;
        }

        // 验证任务数据
        if (!this.currentTask.name || this.currentTask.name.trim() === '') {
            this.showError('请输入任务名称');
            return;
        }

        const saveBtn = document.getElementById('saveTaskBtn');
        const originalText = saveBtn.innerHTML;

        try {
            // 显示保存中状态
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i data-feather="loader" style="width: 16px; height: 16px; margin-right: 6px; animation: spin 1s linear infinite;"></i>保存中...';

            await this.saveCurrentTask();

            // 显示保存成功状态
            saveBtn.innerHTML = '<i data-feather="check" style="width: 16px; height: 16px; margin-right: 6px;"></i>保存成功';
            saveBtn.style.backgroundColor = '#27ae60';

            this.showSuccess('任务保存成功');

            // 2秒后恢复原状态
            setTimeout(() => {
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                saveBtn.style.backgroundColor = '';
                // 重新初始化feather图标
                if (window.feather) {
                    feather.replace();
                }
            }, 2000);

        } catch (error) {
            // 显示保存失败状态
            saveBtn.innerHTML = '<i data-feather="x" style="width: 16px; height: 16px; margin-right: 6px;"></i>保存失败';
            saveBtn.style.backgroundColor = '#e74c3c';

            setTimeout(() => {
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                saveBtn.style.backgroundColor = '';
                // 重新初始化feather图标
                if (window.feather) {
                    feather.replace();
                }
            }, 2000);

            throw error;
        }
    }

    /**
     * 运行当前任务
     */
    async runCurrentTask() {
        if (!this.currentTask || !this.currentTask.id) {
            this.showError('请先保存任务');
            return;
        }

        try {
            const result = await api.runTask(this.currentTask.id);

            if (result.success) {
                this.showSuccess('任务开始运行');

                // 立即更新运行状态到本地
                this.currentRunId = result.run_id;
                this.isRunning = true;

                // 添加到运行任务集合（立即更新UI）
                this.runningTasks.set(this.currentTask.id, {
                    run_id: result.run_id,
                    status: 'running',
                    message_count: 0,
                    cycle_count: 0,
                    error_count: 0
                });

                // 立即更新按钮状态
                this.updateRunButton();
                this.renderTaskList(); // 更新任务列表中的按钮

                // 如果支持WebSocket，使用WebSocket运行任务
                if (result.use_websocket && window.webSocketManager && window.webSocketManager.isSupported) {
                    this.startWebSocketTask(result);
                } else {
                    // 使用传统方式监控任务
                    this.startTaskMonitoring(result.run_id);
                }
            } else {
                this.showError(result.error || '运行任务失败');
            }
        } catch (error) {
            console.error('运行任务失败:', error);
            this.showError('运行任务失败');
        }
    }

    /**
     * 停止当前任务
     */
    async stopCurrentTask() {
        if (!this.currentRunId) {
            this.showError('没有正在运行的任务');
            return;
        }

        try {
            const result = await api.stopTask(this.currentRunId);

            if (result.success) {
                this.showSuccess('任务已停止');

                // 立即更新运行状态到本地
                this.isRunning = false;
                this.currentRunId = null;

                // 从运行任务集合中移除（立即更新UI）
                if (this.currentTask && this.currentTask.id) {
                    this.runningTasks.delete(this.currentTask.id);
                }

                // 立即更新按钮状态
                this.updateRunButton();
                this.renderTaskList(); // 更新任务列表中的按钮

                // 如果使用WebSocket，停止WebSocket任务
                if (window.webSocketManager && window.webSocketManager.isSupported) {
                    this.stopWebSocketTask();
                }
            } else {
                this.showError(result.error || '停止任务失败');
            }
        } catch (error) {
            console.error('停止任务失败:', error);
            this.showError('停止任务失败');
        }
    }

    /**
     * 使用WebSocket启动任务
     */
    startWebSocketTask(result) {
        const taskId = this.currentTask.id;
        const taskData = result.task_data;

        // 加入任务房间
        if (window.webSocketManager.joinTaskRoom(taskId)) {
            // 绑定WebSocket事件处理器
            this.bindWebSocketEvents();

            // 启动任务
            if (window.webSocketManager.startTask(taskId, taskData)) {
                this.currentRunId = result.run_id;
                this.isRunning = true;
                console.log('WebSocket任务启动成功');
            } else {
                this.showError('WebSocket任务启动失败');
            }
        } else {
            this.showError('无法连接到WebSocket服务器');
        }
    }

    /**
     * 绑定WebSocket事件处理器
     */
    bindWebSocketEvents() {
        if (!window.webSocketManager) return;

        // 移除之前的事件处理器
        this.unbindWebSocketEvents();

        // 任务日志事件
        this.webSocketLogHandler = (data) => {
            this.appendToConsole(data.level, `[${data.timestamp}] ${data.message}`);
        };
        window.webSocketManager.on('task_log', this.webSocketLogHandler);

        // 任务状态更新事件
        this.webSocketStatusHandler = (data) => {
            this.updateTaskStatus(data);
        };
        window.webSocketManager.on('task_status', this.webSocketStatusHandler);

        // 任务启动事件
        this.webSocketStartedHandler = (data) => {
            console.log('任务已启动:', data);
        };
        window.webSocketManager.on('task_started', this.webSocketStartedHandler);

        // 任务停止事件
        this.webSocketStoppedHandler = (data) => {
            console.log('任务已停止:', data);
            this.isRunning = false;
            this.currentRunId = null;
            this.updateRunButton();
        };
        window.webSocketManager.on('task_stopped', this.webSocketStoppedHandler);

        // 错误事件
        this.webSocketErrorHandler = (data) => {
            this.showError(data.message || '任务运行出错');
        };
        window.webSocketManager.on('error', this.webSocketErrorHandler);
    }

    /**
     * 停止WebSocket任务
     */
    stopWebSocketTask() {
        if (this.currentTask && this.currentTask.id) {
            console.log('停止WebSocket任务:', this.currentTask.id);
            window.webSocketManager.stopTask(this.currentTask.id);
        }
    }

    /**
     * 解绑WebSocket事件处理器
     */
    unbindWebSocketEvents() {
        if (!window.webSocketManager) return;

        if (this.webSocketLogHandler) {
            window.webSocketManager.off('task_log', this.webSocketLogHandler);
            this.webSocketLogHandler = null;
        }

        if (this.webSocketStatusHandler) {
            window.webSocketManager.off('task_status', this.webSocketStatusHandler);
            this.webSocketStatusHandler = null;
        }

        if (this.webSocketStartedHandler) {
            window.webSocketManager.off('task_started', this.webSocketStartedHandler);
            this.webSocketStartedHandler = null;
        }

        if (this.webSocketStoppedHandler) {
            window.webSocketManager.off('task_stopped', this.webSocketStoppedHandler);
            this.webSocketStoppedHandler = null;
        }

        if (this.webSocketErrorHandler) {
            window.webSocketManager.off('error', this.webSocketErrorHandler);
            this.webSocketErrorHandler = null;
        }
    }

    /**
     * 更新任务状态（WebSocket版本）
     */
    updateTaskStatus(data) {
        // 更新状态显示
        const statusElement = document.getElementById('runStatus');
        const messageCountElement = document.getElementById('runMessageCount');
        const cycleCountElement = document.getElementById('runCycleCount');
        const errorCountElement = document.getElementById('runErrorCount');
        const elapsedTimeElement = document.getElementById('runElapsedTime');

        if (statusElement) {
            statusElement.textContent = data.status === 'running' ? '运行中' : '已停止';
            statusElement.className = 'status-code ' + (data.status === 'running' ? 'success' : 'info');
        }

        if (messageCountElement) {
            messageCountElement.textContent = data.message_count || 0;
        }

        if (cycleCountElement) {
            cycleCountElement.textContent = data.cycle_count || 0;
        }

        if (errorCountElement) {
            errorCountElement.textContent = data.error_count || 0;
            errorCountElement.className = 'status-code ' + (data.error_count > 0 ? 'error' : 'success');
        }

        if (elapsedTimeElement) {
            elapsedTimeElement.textContent = data.elapsed_time || '00:00:00';
        }
    }

    /**
     * 运行指定任务
     */
    async runTask(taskId) {
        try {
            const result = await api.runTask(taskId);

            if (result.success) {
                this.showSuccess('任务开始运行');
                this.startTaskMonitoring(result.run_id);
            } else {
                this.showError(result.error || '运行任务失败');
            }
        } catch (error) {
            console.error('运行任务失败:', error);
            this.showError('运行任务失败');
        }
    }

    /**
     * 从任务列表运行任务
     */
    async runTaskFromList(taskId) {
        try {
            const result = await api.runTask(taskId);

            if (result.success) {
                this.showSuccess('任务开始运行');

                // 立即添加到运行任务集合（立即更新UI）
                this.runningTasks.set(taskId, {
                    run_id: result.run_id,
                    status: 'running',
                    message_count: 0,
                    cycle_count: 0,
                    error_count: 0
                });

                // 如果是当前任务，更新状态
                if (this.currentTask && this.currentTask.id === taskId) {
                    this.currentRunId = result.run_id;
                    this.isRunning = true;
                    this.updateRunButton();
                }

                // 立即更新任务列表显示
                this.renderTaskList();

            } else {
                this.showError(result.error || '运行任务失败');
            }
        } catch (error) {
            console.error('运行任务失败:', error);
            this.showError('运行任务失败');
        }
    }

    /**
     * 从任务列表停止任务
     */
    async stopTaskFromList(taskId) {
        try {
            // 查找任务的运行信息
            const taskInfo = this.runningTasks.get(taskId);
            if (!taskInfo) {
                this.showError('无法找到任务的运行信息');
                return;
            }

            const result = await api.stopTask(taskInfo.run_id);

            if (result.success) {
                this.showSuccess('任务已停止');

                // 立即从运行任务集合中移除（立即更新UI）
                this.runningTasks.delete(taskId);

                // 如果是当前任务，更新状态
                if (this.currentTask && this.currentTask.id === taskId) {
                    this.isRunning = false;
                    this.currentRunId = null;
                    this.updateRunButton();
                }

                // 立即更新任务列表显示
                this.renderTaskList();

            } else {
                this.showError(result.error || '停止任务失败');
            }
        } catch (error) {
            console.error('停止任务失败:', error);
            this.showError('停止任务失败');
        }
    }



    /**
     * 复制任务
     */
    async duplicateTask(taskId) {
        try {
            const task = await api.getTask(taskId);
            const newTask = {
                ...task,
                id: null,
                name: task.name + ' (副本)'
            };

            this.tasks.unshift(newTask);
            this.selectTask(newTask);
        } catch (error) {
            console.error('复制任务失败:', error);
            this.showError('复制任务失败');
        }
    }

    /**
     * 删除任务
     */
    async deleteTask(taskId) {
        if (!confirm('确定要删除这个任务吗？')) return;

        try {
            await api.deleteTask(taskId);
            this.tasks = this.tasks.filter(task => task.id !== taskId);

            if (this.currentTask && this.currentTask.id === taskId) {
                this.currentTask = this.tasks.length > 0 ? this.tasks[0] : null;
                this.renderTaskForm();
            }

            this.renderTaskList();
            this.showSuccess('任务删除成功');
        } catch (error) {
            console.error('删除任务失败:', error);
            this.showError('删除任务失败');
        }
    }

    /**
     * 更新任务列表中的任务
     */
    updateTaskInList(task) {
        const index = this.tasks.findIndex(t => t.id === task.id);
        if (index !== -1) {
            this.tasks[index] = task;
            this.renderTaskList();
        }
    }

    /**
     * 更新运行按钮状态
     */
    updateRunButton() {
        const runBtn = document.getElementById('runBtn');

        // 检查当前任务是否正在运行（只基于后台实际状态）
        const isCurrentTaskRunning = this.currentTask && this.runningTasks.has(this.currentTask.id);

        runBtn.disabled = false; // 按钮始终可点击
        runBtn.textContent = isCurrentTaskRunning ? '⏸ 停止' : '▶ 运行';
        runBtn.className = isCurrentTaskRunning ? 'run-button running' : 'run-button';

        // 同步前端状态变量
        this.isRunning = isCurrentTaskRunning;
        if (isCurrentTaskRunning) {
            const taskInfo = this.runningTasks.get(this.currentTask.id);
            this.currentRunId = taskInfo ? taskInfo.run_id : null;
        } else {
            this.currentRunId = null;
        }
    }

    /**
     * 开始任务监控
     */
    startTaskMonitoring(runId) {
        // 这里可以实现WebSocket或轮询来监控任务状态
        console.log('开始监控任务:', runId);
    }

    /**
     * 开始轮询运行中的任务（已合并到startRunningTasksPolling）
     */
    startPolling() {
        // 这个方法已经被startRunningTasksPolling替代
        // 保留空方法以避免破坏现有调用
    }

    // 注意：fetchAndDisplayLogs 和 displayLogs 方法已移除
    // 现在日志通过WebSocket实时接收，使用 appendToConsole 方法直接添加

    /**
     * 添加日志到控制台
     */
    appendToConsole(level, message) {
        const consoleOutput = document.getElementById('consoleOutput');
        if (!consoleOutput) return;

        // 创建日志元素
        const logElement = document.createElement('div');
        logElement.className = `log-${level}`;
        logElement.textContent = message;

        // 添加到控制台
        consoleOutput.appendChild(logElement);

        // 限制日志条数，避免内存问题
        const maxLogLines = window.AppConfig?.logging?.maxConsoleLogLines || 10;
        const memoryConfig = window.AppConfig?.logging?.memoryManagement || {};
        const autoCleanup = memoryConfig.autoCleanup !== false; // 默认启用
        const cleanupBatchSize = memoryConfig.cleanupBatchSize || 5;

        if (autoCleanup) {
            const logElements = consoleOutput.children;

            // 如果超过最大条数，删除最早的日志
            if (logElements.length > maxLogLines) {
                const elementsToRemove = Math.min(
                    logElements.length - maxLogLines + cleanupBatchSize,
                    logElements.length - 1 // 至少保留一条日志
                );

                // 批量删除旧日志，提高性能
                for (let i = 0; i < elementsToRemove; i++) {
                    if (logElements[0]) {
                        consoleOutput.removeChild(logElements[0]);
                    }
                }

                // 触发垃圾回收提示（如果浏览器支持）
                if (window.gc && memoryConfig.forceGCInterval > 0) {
                    setTimeout(() => {
                        if (typeof window.gc === 'function') {
                            window.gc();
                        }
                    }, 100);
                }
            }
        }

        // 自动滚动到底部
        const autoScroll = document.getElementById('autoScrollLog');
        if (autoScroll && autoScroll.checked) {
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
    }

    /**
     * 清空控制台
     */
    clearConsole() {
        const consoleOutput = document.getElementById('consoleOutput');
        if (consoleOutput) {
            // 清空所有子元素，释放内存
            while (consoleOutput.firstChild) {
                consoleOutput.removeChild(consoleOutput.firstChild);
            }

            // 添加清除提示
            const clearElement = document.createElement('div');
            clearElement.className = 'log-info';
            clearElement.textContent = '';
            consoleOutput.appendChild(clearElement);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error(message);
        this.appendToConsole('error', `错误: ${message}`);
        // 这里可以实现更好的错误提示UI
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log(message);
        this.appendToConsole('success', message);
        // 这里可以实现更好的成功提示UI
    }
}

// 创建全局任务管理器实例
window.taskManager = new TaskManager();
