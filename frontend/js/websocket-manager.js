/**
 * WebSocket管理器
 * 负责处理与服务器的WebSocket连接和实时通信
 */
class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.currentTaskId = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.eventHandlers = {};
        
        // 检查是否支持Socket.IO
        this.isSupported = typeof io !== 'undefined';
        
        if (!this.isSupported) {
            console.warn('Socket.IO not available, WebSocket features disabled');
        }
    }
    
    /**
     * 连接到WebSocket服务器
     */
    connect() {
        if (!this.isSupported) {
            console.warn('WebSocket not supported');
            return false;
        }
        
        if (this.socket && this.isConnected) {
            console.log('WebSocket already connected');
            return true;
        }
        
        try {
            // 连接到Socket.IO服务器
            this.socket = io();
            
            // 绑定事件处理器
            this.socket.on('connect', () => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.emit('connected');
            });
            
            this.socket.on('disconnect', () => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.emit('disconnected');
                this.attemptReconnect();
            });
            
            this.socket.on('connect_error', (error) => {
                console.error('WebSocket connection error:', error);
                this.isConnected = false;
                this.emit('error', error);
                this.attemptReconnect();
            });
            
            // 任务相关事件
            this.socket.on('task_log', (data) => {
                this.emit('task_log', data);
            });
            
            this.socket.on('task_status', (data) => {
                this.emit('task_status', data);
            });
            
            this.socket.on('task_started', (data) => {
                this.emit('task_started', data);
            });
            
            this.socket.on('task_stopped', (data) => {
                this.emit('task_stopped', data);
            });
            
            this.socket.on('error', (data) => {
                this.emit('error', data);
            });
            
            return true;
        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            return false;
        }
    }
    
    /**
     * 断开WebSocket连接
     */
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        this.isConnected = false;
        this.currentTaskId = null;
    }
    
    /**
     * 尝试重新连接
     */
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectDelay * this.reconnectAttempts);
    }
    
    /**
     * 加入任务房间以接收实时更新
     */
    joinTaskRoom(taskId) {
        if (!this.isConnected || !this.socket) {
            console.warn('WebSocket not connected');
            return false;
        }
        
        // 如果已经在其他任务房间，先离开
        if (this.currentTaskId && this.currentTaskId !== taskId) {
            this.leaveTaskRoom(this.currentTaskId);
        }
        
        this.currentTaskId = taskId;
        this.socket.emit('join_task_room', { task_id: taskId });
        console.log(`Joined task room: ${taskId}`);
        return true;
    }
    
    /**
     * 离开任务房间
     */
    leaveTaskRoom(taskId) {
        if (!this.isConnected || !this.socket) {
            return false;
        }
        
        this.socket.emit('leave_task_room', { task_id: taskId });
        
        if (this.currentTaskId === taskId) {
            this.currentTaskId = null;
        }
        
        console.log(`Left task room: ${taskId}`);
        return true;
    }
    
    /**
     * 启动任务
     */
    startTask(taskId, taskData) {
        if (!this.isConnected || !this.socket) {
            console.warn('WebSocket not connected');
            return false;
        }
        
        this.socket.emit('start_task', {
            task_id: taskId,
            task_data: taskData
        });
        
        console.log(`Started task: ${taskId}`);
        return true;
    }
    
    /**
     * 停止任务
     */
    stopTask(taskId) {
        if (!this.isConnected || !this.socket) {
            console.warn('WebSocket not connected');
            return false;
        }
        
        this.socket.emit('stop_task', { task_id: taskId });
        console.log(`Stopped task: ${taskId}`);
        return true;
    }
    
    /**
     * 注册事件处理器
     */
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
    }
    
    /**
     * 移除事件处理器
     */
    off(event, handler) {
        if (!this.eventHandlers[event]) {
            return;
        }
        
        const index = this.eventHandlers[event].indexOf(handler);
        if (index > -1) {
            this.eventHandlers[event].splice(index, 1);
        }
    }
    
    /**
     * 触发事件
     */
    emit(event, data) {
        if (!this.eventHandlers[event]) {
            return;
        }
        
        this.eventHandlers[event].forEach(handler => {
            try {
                handler(data);
            } catch (error) {
                console.error(`Error in event handler for ${event}:`, error);
            }
        });
    }
    
    /**
     * 检查是否已连接
     */
    isWebSocketConnected() {
        return this.isConnected && this.socket && this.socket.connected;
    }
    
    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            supported: this.isSupported,
            connected: this.isConnected,
            currentTaskId: this.currentTaskId,
            reconnectAttempts: this.reconnectAttempts
        };
    }
}

// 创建全局WebSocket管理器实例
window.webSocketManager = new WebSocketManager();

// 页面加载完成后自动连接
document.addEventListener('DOMContentLoaded', () => {
    if (window.webSocketManager.isSupported) {
        console.log('Initializing WebSocket connection...');
        window.webSocketManager.connect();
    } else {
        console.warn('WebSocket not supported, falling back to polling');
    }
});

// 页面卸载时断开连接
window.addEventListener('beforeunload', () => {
    if (window.webSocketManager) {
        window.webSocketManager.disconnect();
    }
});
