/**
 * WebSocket客户端，用于实时接收任务日志
 */
class WebSocketClient {
    constructor() {
        this.socket = null;
        this.currentTaskId = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
    }

    /**
     * 连接WebSocket
     */
    connect() {
        try {
            // 使用Socket.IO客户端连接
            this.socket = io();
            
            this.socket.on('connect', () => {
                console.log('WebSocket连接成功');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                // 如果有当前任务，加入对应的房间
                if (this.currentTaskId) {
                    this.joinTaskRoom(this.currentTaskId);
                }
            });

            this.socket.on('disconnect', () => {
                console.log('WebSocket连接断开');
                this.isConnected = false;
                this.attemptReconnect();
            });

            this.socket.on('connect_error', (error) => {
                console.error('WebSocket连接错误:', error);
                this.isConnected = false;
                this.attemptReconnect();
            });

            // 监听任务日志事件
            this.socket.on('task_log', (logEntry) => {
                this.handleTaskLog(logEntry);
            });

            // 监听任务状态更新事件
            this.socket.on('task_status_update', (statusData) => {
                this.handleTaskStatusUpdate(statusData);
            });

        } catch (error) {
            console.error('WebSocket初始化失败:', error);
        }
    }

    /**
     * 尝试重连
     */
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectDelay * this.reconnectAttempts);
        } else {
            console.error('WebSocket重连失败，已达到最大重试次数');
        }
    }

    /**
     * 加入任务房间
     */
    joinTaskRoom(taskId) {
        if (this.socket && this.isConnected) {
            const roomId = `task_${taskId}`;
            this.socket.emit('join_task_room', { task_id: taskId });
            console.log(`加入任务房间: ${roomId}`);
            this.currentTaskId = taskId;
        }
    }

    /**
     * 离开任务房间
     */
    leaveTaskRoom(taskId) {
        if (this.socket && this.isConnected) {
            const roomId = `task_${taskId}`;
            this.socket.emit('leave_task_room', { task_id: taskId });
            console.log(`离开任务房间: ${roomId}`);
            
            if (this.currentTaskId === taskId) {
                this.currentTaskId = null;
            }
        }
    }

    /**
     * 切换任务
     */
    switchTask(newTaskId) {
        if (this.currentTaskId && this.currentTaskId !== newTaskId) {
            this.leaveTaskRoom(this.currentTaskId);
        }
        
        if (newTaskId) {
            this.joinTaskRoom(newTaskId);
        }
    }

    /**
     * 处理任务日志
     */
    handleTaskLog(logEntry) {
        console.log('收到任务日志:', logEntry);
        
        // 将日志添加到控制台
        if (window.app) {
            window.app.appendToConsole(`[${logEntry.timestamp}] ${logEntry.message}`);
        }
    }

    /**
     * 处理任务状态更新
     */
    handleTaskStatusUpdate(statusData) {
        console.log('收到任务状态更新:', statusData);
        
        // 更新任务管理器的状态
        if (window.taskManager) {
            window.taskManager.updateTaskStatus(statusData);
        }
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
            this.currentTaskId = null;
        }
    }
}

// 创建全局WebSocket客户端实例
window.wsClient = new WebSocketClient();
