<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js 测试</title>
</head>
<body>
    <h1>Chart.js 测试</h1>
    
    <div style="width: 400px; height: 300px; border: 1px solid #ccc; margin: 20px;">
        <canvas id="testChart"></canvas>
    </div>
    
    <button onclick="createChart()">创建图表</button>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        function createChart() {
            console.log('Chart.js 版本:', Chart.version);
            
            const ctx = document.getElementById('testChart');
            console.log('Canvas 元素:', ctx);
            
            if (!ctx) {
                console.error('找不到canvas元素');
                return;
            }
            
            try {
                const chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1', '2', '3', '4', '5'],
                        datasets: [{
                            label: '测试数据',
                            data: [1, 2, 3, 4, 5],
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                console.log('图表创建成功:', chart);
            } catch (error) {
                console.error('创建图表时出错:', error);
            }
        }
        
        // 页面加载完成后自动创建图表
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成');
            createChart();
        });
    </script>
</body>
</html>
