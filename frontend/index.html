<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KafkaTool - Postman Style</title>
    <link rel="stylesheet" href="css/style.css">

    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/material.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/hint/show-hint.min.css">
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>KafkaTool</h2>
        </div>
        <button class="sidebar-new-task-btn" id="newTaskBtn">＋ 新建任务</button>

        <div class="sidebar-section-title">我的任务</div>
        <ul class="task-list" id="taskList">
            <!-- 任务列表将通过JavaScript动态加载 -->
        </ul>

        <div class="sidebar-section-title">管理</div>
        <ul class="management-list">
            <li id="env-management-btn">
                <span class="management-item-name">
                    <i class="icon-globe">🌐</i> 环境变量
                </span>
            </li>
            <li id="tpl-management-btn">
                 <span class="management-item-name">
                    <i class="icon-template">📄</i> 消息模板
                </span>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="main-header">
            <input type="text" id="taskNameInput" placeholder="任务名称">
            <button class="run-button" id="runBtn">▶ 运行</button>
        </div>

        <div class="main-tabs-container">
            <div class="tabs">
                <button class="tab-button active" data-tab="config">任务配置</button>
                <button class="tab-button" data-tab="run-output">运行输出</button>
                <button class="tab-button" data-tab="run-history">运行历史</button>
                <button class="tab-button" data-tab="charts">统计图表</button>
            </div>

            <!-- 任务配置Tab -->
            <div id="configTabContent" class="tab-content active">
                <div class="config-container">
                    <!-- 基本设置 -->
                    <div class="form-section">
                        <h4>基本设置</h4>
                        <div class="form-group">
                            <label for="taskDescription">任务描述</label>
                            <input type="text" id="taskDescription" placeholder="输入任务描述">
                        </div>
                        <div class="form-group" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <label for="cycleDuration">周期时长 (秒)</label>
                                <input type="number" id="cycleDuration" value="60">
                            </div>
                            <div>
                                <label for="totalDuration">总运行时长 (秒, 0为无限)</label>
                                <input type="number" id="totalDuration" value="300">
                            </div>
                        </div>
                    </div>

                    <!-- 队列配置 -->
                    <div class="form-section">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4>队列配置</h4>
                            <button id="addQueueBtn" style="padding: 5px 10px; font-size: 0.9em;">＋ 添加队列</button>
                        </div>
                        <div id="queuesContainer">
                            <!-- 队列项将通过JavaScript动态添加 -->
                        </div>
                    </div>

                    <!-- 环境配置 -->
                    <div class="form-section">
                        <h4>环境配置</h4>
                        <div class="form-group">
                            <label for="environmentSelect">选择环境</label>
                            <select id="environmentSelect">
                                <option value="">选择环境...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="envVariablesPreview">环境变量预览 (只读)</label>
                            <textarea id="envVariablesPreview" readonly rows="8" style="background-color: #f8f9fa; font-family: 'Courier New', monospace; font-size: 13px; box-sizing: border-box;"></textarea>
                        </div>
                    </div>

                    <!-- Save Task Button Container -->
                    <div style="text-align: right; margin-top: 10px; margin-bottom: 20px; padding-right: 5px;">
                        <button class="save-task-btn" id="saveTaskBtn" title="保存当前任务配置">
                            <i data-feather="save" style="width: 16px; height: 16px; margin-right: 6px;"></i>
                            保存任务
                        </button>
                    </div>
                </div>
            </div>

            <!-- 运行输出Tab -->
            <div id="runOutputTabContent" class="tab-content">
                <div class="log-controls">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <label for="maxLogLinesInput" style="font-size: 14px; font-weight: 500; margin: 0;">最大日志条数:</label>
                        <input type="number" id="maxLogLinesInput" value="10" min="5" max="1000"
                               style="width: 80px; padding: 4px 8px; border: 1px solid #ccc; border-radius: 4px; font-size: 14px;">
                        <button id="applyLogSettingsBtn" style="padding: 4px 12px; font-size: 13px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            <i data-feather="check" style="width: 14px; height: 14px; margin-right: 4px;"></i>应用
                        </button>
                    </div>
                    <button id="clearLogBtn">清除日志</button>
                    <div>
                        <input type="checkbox" id="autoScrollLog" checked>
                        <label for="autoScrollLog">自动滚动</label>
                    </div>
                    <input type="text" id="logFilterInput" placeholder="筛选日志..." style="padding: 6px; border: 1px solid #ccc; border-radius: 4px; font-size: 0.9em;">
                </div>
                <div class="status-bar">
                    <span>状态: <span id="runStatus" class="status-code">就绪</span></span>
                    <span>消息数: <span id="runMessageCount">0</span></span>
                    <span>周期数: <span id="runCycleCount">0</span></span>
                    <span>错误数: <span id="runErrorCount" class="status-code">0</span></span>
                    <span>已运行时长: <span id="runElapsedTime">00:00:00</span></span>
                </div>
                <div class="response-data" id="consoleOutput">
                    <span class="log-info">欢迎使用 KafkaTool！</span>
                    <span class="log-info">请配置任务参数并点击运行按钮开始测试。</span>
                </div>
            </div>

            <!-- 运行历史Tab -->
            <div id="runHistoryTabContent" class="tab-content">
                <div style="padding: 20px;">
                    <div class="history-controls" style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e9ecef;">
                        <button id="refreshHistoryBtn" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                            <i data-feather="refresh-cw" style="width: 16px; height: 16px; margin-right: 6px;"></i>刷新历史
                        </button>
                        <input type="text" id="historyFilterInput" placeholder="筛选历史记录..."
                               style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        <button id="clearTaskHistoryBtn" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                            <i data-feather="trash-2" style="width: 16px; height: 16px; margin-right: 6px;"></i>清空当前任务历史
                        </button>
                        <span class="history-summary" id="historySummary" style="font-size: 14px; color: #6c757d; font-weight: 500;">暂无历史记录</span>
                    </div>

                    <div id="historyItemsContainer" style="max-height: 600px; overflow-y: auto;">
                        <div id="historyEmptyState" style="text-align: center; color: #95a5a6; padding: 60px 20px; background: #f8f9fa; border-radius: 6px; border: 2px dashed #dee2e6;">
                            <i data-feather="clock" style="width: 48px; height: 48px; margin-bottom: 16px; color: #adb5bd;"></i>
                            <h5 style="margin: 0 0 8px 0; color: #6c757d;">暂无运行历史记录</h5>
                            <p style="margin: 0; color: #adb5bd; font-size: 14px;">运行任务后，历史记录将显示在这里</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计图表Tab -->
            <div id="chartsTabContent" class="tab-content">
                <div style="padding: 20px;">
                    <div class="charts-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="margin: 0;">统计图表</h4>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <span id="chartTaskName" style="font-size: 14px; color: #6c757d;">未选择任务</span>
                            <button id="refreshChartsBtn" style="padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                                <i data-feather="refresh-cw" style="width: 16px; height: 16px; margin-right: 4px;"></i>刷新图表
                            </button>
                        </div>
                    </div>

                    <div id="chartsContainer" style="background: #fff; border: 1px solid #e0e0e0; border-radius: 6px; padding: 20px;">
                        <!-- 图表控制区域 -->
                        <div class="charts-controls" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; border: 1px solid #e9ecef;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h5 style="margin: 0 0 8px 0; color: #2c3e50;">最后一次运行统计</h5>
                                    <div id="chartsSummary" style="font-size: 14px; color: #6c757d;">正在加载统计数据...</div>
                                </div>
                                <div style="display: flex; gap: 15px; align-items: center;">
                                    <div style="text-align: center;">
                                        <div style="font-size: 12px; color: #6c757d;">运行状态</div>
                                        <div id="chartRunStatus" style="font-weight: 500; color: #495057;">-</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-size: 12px; color: #6c757d;">总消息数</div>
                                        <div id="chartTotalMessages" style="font-weight: 500; color: #495057;">-</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-size: 12px; color: #6c757d;">运行时长</div>
                                        <div id="chartDuration" style="font-weight: 500; color: #495057;">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 图表区域 -->
                        <div class="charts-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- 消息发送趋势图 -->
                            <div class="chart-card" style="border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; background: white;">
                                <h6 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 16px;">消息发送趋势</h6>
                                <div style="position: relative; height: 300px;">
                                    <canvas id="messagesTrendChart"></canvas>
                                </div>
                            </div>

                            <!-- 队列分布图 -->
                            <div class="chart-card" style="border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; background: white;">
                                <h6 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 16px;">队列消息分布</h6>
                                <div style="position: relative; height: 300px;">
                                    <canvas id="queueDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 空状态 -->
                        <div id="chartsEmptyState" style="display: none; text-align: center; color: #95a5a6; padding: 60px 20px;">
                            <i data-feather="bar-chart-2" style="width: 48px; height: 48px; margin-bottom: 16px; color: #adb5bd;"></i>
                            <h5 style="margin: 0 0 8px 0; color: #6c757d;">暂无统计数据</h5>
                            <p style="margin: 0; color: #adb5bd; font-size: 14px;">运行任务后，统计图表将显示在这里</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Task Modal -->
    <div id="newTaskModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3>新建任务</h3>
                <button class="modal-close-btn" data-modal-id="newTaskModal">&times;</button>
            </div>
            <div class="modal-body" style="padding: 20px; max-height: 60vh; overflow-y: auto;">
                    <!-- 基本设置 -->
                    <div class="form-section">
                        <h4>基本设置</h4>
                        <div class="form-group">
                            <label for="newTaskName">任务名称</label>
                            <input type="text" id="newTaskName" placeholder="输入任务名称">
                        </div>
                        <div class="form-group">
                            <label for="newTaskDescription">任务描述</label>
                            <input type="text" id="newTaskDescription" placeholder="输入任务描述">
                        </div>
                        <div class="form-group" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <label for="newCycleDuration">周期时长 (秒)</label>
                                <input type="number" id="newCycleDuration" value="60">
                            </div>
                            <div>
                                <label for="newTotalDuration">总运行时长 (秒, 0为无限)</label>
                                <input type="number" id="newTotalDuration" value="300">
                            </div>
                        </div>
                    </div>

                    <!-- 队列配置 -->
                    <div class="form-section">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4>队列配置</h4>
                            <button id="newTaskAddQueueBtn" type="button" style="padding: 5px 10px; font-size: 0.9em;">＋ 添加队列</button>
                        </div>
                        <div id="newTaskQueuesContainer">
                            <!-- 队列项将通过JavaScript动态添加 -->
                            <div style="text-align: center; color: #95a5a6; padding: 20px; border: 1px dashed #ddd; border-radius: 4px;">
                                点击"添加队列"开始配置消息队列
                            </div>
                        </div>
                    </div>

                    <!-- 环境配置 -->
                    <div class="form-section">
                        <h4>环境配置</h4>
                        <div class="form-group">
                            <label for="newEnvironmentSelect">选择环境</label>
                            <select id="newEnvironmentSelect">
                                <option value="">选择环境...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="newEnvVariablesPreview">环境变量预览 (只读)</label>
                            <textarea id="newEnvVariablesPreview" readonly rows="6" style="background-color: #f8f9fa; font-family: 'Courier New', monospace; font-size: 13px; box-sizing: border-box;" placeholder="选择环境后将显示环境变量"></textarea>
                        </div>
                    </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="newTaskModal">取消</button>
                <button class="btn-primary" id="createNewTaskBtn">创建任务</button>
            </div>
        </div>
    </div>

    <!-- Environment Management Modal -->
    <div id="envManagementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4>环境变量管理</h4>
                <button class="modal-close-btn" data-modal-id="envManagementModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="modal-split-layout">
                    <!-- Left Panel - Environment List -->
                    <div class="modal-left-panel">
                        <div class="modal-left-header">
                            <h5>环境列表</h5>
                            <button class="btn-sm btn-primary" id="newEnvBtn">新建</button>
                        </div>
                        <div class="modal-left-content">
                            <div id="envList">
                                <!-- Environment list items will be dynamically loaded -->
                            </div>
                        </div>
                        <div class="modal-left-footer">
                            <button id="importEnvBtn" class="btn-sm btn-secondary" style="width: 100%;">导入环境...</button>
                        </div>
                    </div>

                    <!-- Right Panel - Environment Editor -->
                    <div class="modal-right-panel">
                        <div class="modal-right-header">
                            <h5>环境详情</h5>
                        </div>
                        <div class="modal-right-content">
                            <div class="form-group">
                                <label for="envName">环境名称</label>
                                <input type="text" id="envName" style="width: 100%;">
                            </div>
                            <div class="form-group">
                                <label for="envDesc">描述 (可选)</label>
                                <input type="text" id="envDesc" style="width: 100%;">
                            </div>
                            <div class="form-group">
                                <label for="envVars">环境变量 (K=V 格式, 每行一个)</label>
                                <textarea id="envVarsTextarea" rows="12" style="width: 100%; font-family: monospace; font-size: 0.9em;"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="envManagementModal">取消</button>
                <button class="btn-primary" id="saveEnvBtn">保存更改</button>
            </div>
        </div>
    </div>

    <!-- Message Templates Modal -->
    <div id="tplManagementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4>消息模板管理</h4>
                <button class="modal-close-btn" data-modal-id="tplManagementModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="modal-split-layout">
                    <!-- Left Panel - Template List -->
                    <div class="modal-left-panel">
                        <div class="modal-left-header">
                            <h5>模板列表</h5>
                            <button class="btn-sm btn-primary" id="newTplBtn">新建</button>
                        </div>
                        <div class="modal-left-content">
                            <div id="tplList">
                                <!-- Template list items will be dynamically loaded -->
                            </div>
                        </div>
                        <div class="modal-left-footer">
                            <button id="importTplBtn" class="btn-sm btn-secondary" style="width: 100%;">导入模板...</button>
                        </div>
                    </div>

                    <!-- Right Panel - Template Editor -->
                    <div class="modal-right-panel">
                        <div class="modal-right-header">
                            <h5>模板详情</h5>
                        </div>
                        <div class="modal-right-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="tplName" style="font-weight: 500; font-size: 14px;">模板名称</label>
                                        <input type="text" id="tplName" style="width: 100%;">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="tplTopic" style="font-weight: 500; font-size: 14px;">默认Topic</label>
                                        <input type="text" id="tplTopic" style="width: 100%;" placeholder="例如: orders.new">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label for="tplContent" class="mb-0" style="font-weight: 500; font-size: 14px;">Python代码</label>
                                    <div style="display: flex; gap: 8px;">
                                        <button id="previewEnvBtn" class="btn btn-sm" style="background: #95a5a6; color: white; border: none; padding: 4px 8px; font-size: 12px;" title="配置预览环境变量">
                                            <i data-feather="settings" style="width: 14px; height: 14px;"></i> 预览变量
                                        </button>
                                        <button id="previewTplBtn" class="btn btn-sm" style="background: #3498db; color: white; border: none; padding: 4px 12px; font-size: 12px;">
                                            <i data-feather="eye" style="width: 14px; height: 14px; margin-right: 4px;"></i> 预览
                                        </button>
                                    </div>
                                </div>
                                <div class="code-editor-container">
                                    <div class="code-editor-toolbar">
                                        <div class="toolbar-left">
                                            <span>Python</span>
                                            <span style="color: #666;">|</span>
                                            <span style="font-size: 0.8em; color: #666;">Ctrl+Space 自动补全</span>
                                        </div>
                                        <div class="toolbar-right">
                                            <label for="themeSelector" style="font-size: 0.8em;">主题:</label>
                                            <select id="themeSelector" class="theme-selector">
                                                <option value="default">默认</option>
                                                <option value="material">Material</option>
                                                <option value="monokai">Monokai</option>
                                            </select>
                                        </div>
                                    </div>
                                    <textarea id="tplContentTextarea" style="display: none;" placeholder="def generate_message(env, device_id, device_ip, index, timestamp):
    return {
        'id': index,
        'timestamp': timestamp,
        'data': 'your message data'
    }"></textarea>
                                </div>
                            </div>
                            <div style="margin-top: 15px;">
                                <span id="testResult" style="font-size: 0.9em; color: #666;"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="tplManagementModal">取消</button>
                <button class="btn-primary" id="saveTplBtn">保存模板</button>
            </div>
        </div>
    </div>

    <!-- 预览环境变量编辑弹窗 -->
    <div id="previewEnvModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3>预览环境变量</h3>
                <button class="modal-close-btn" data-modal-id="previewEnvModal">&times;</button>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <div class="form-group">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 8px;">
                        <label for="previewEnvVars" style="font-weight: 500; margin: 0;">环境变量 (JSON格式)</label>
                        <span id="currentTemplateName" style="font-size: 12px; color: #666; font-style: italic;"></span>
                    </div>
                    <textarea id="previewEnvVars" rows="15" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1;" placeholder='{
  "API_HOST": "localhost",
  "API_PORT": "8080",
  "DEBUG": "true"
}'></textarea>
                </div>
                <div style="font-size: 12px; color: #666; margin-top: 8px;">
                    💡 提示：这些环境变量仅用于预览，保存模板时会一起保存
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="previewEnvModal">取消</button>
                <button class="btn-primary" id="savePreviewEnvBtn">确定</button>
            </div>
        </div>
    </div>

    <!-- 预览结果弹窗 -->
    <div id="previewModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3>模板预览结果</h3>
                <button class="modal-close-btn" data-modal-id="previewModal">&times;</button>
            </div>
            <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
                <div id="previewContent" style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.5; padding: 15px; background: #f8f9fa; border-radius: 5px; border: 1px solid #e9ecef;">
                    <!-- 预览内容将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="previewModal">关闭</button>
            </div>
        </div>
    </div>

    <!-- CodeMirror JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/python/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/hint/show-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/hint/python-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/selection/active-line.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/fold/foldcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/fold/foldgutter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/fold/indent-fold.min.js"></script>

    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>

    <!-- Socket.IO Client -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket-manager.js"></script>
    <script src="js/components/task-manager.js"></script>
    <script src="js/components/queue-manager.js"></script>
    <script src="js/components/environment-manager.js"></script>
    <script src="js/components/template-manager.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 初始化Feather图标
        feather.replace();

        // 绑定刷新图表按钮事件
        document.addEventListener('DOMContentLoaded', () => {
            const refreshChartsBtn = document.getElementById('refreshChartsBtn');
            if (refreshChartsBtn) {
                refreshChartsBtn.addEventListener('click', () => {
                    if (window.app) {
                        window.app.loadStatisticsCharts();
                    }
                });
            }
        });
    </script>
</body>
</html>
