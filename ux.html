<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>l - Postman Style UX Prototype</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            background-color: #f0f2f5;
            color: #333;
        }

        .sidebar {
            width: 280px;
            background-color: #2c3e50; /* Postman-like dark sidebar */
            color: #ecf0f1;
            padding: 15px;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #34495e;
        }

        .sidebar-header {
            padding: 10px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-header h2 {
            margin: 0;
            font-size: 1.4em;
            color: #ecf0f1;
        }

        .sidebar-new-task-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 1em;
            margin-bottom: 15px;
            cursor: pointer;
            border-radius: 4px;
            width: 100%;
        }
        .sidebar-new-task-btn:hover {
            background-color: #2980b9;
        }

        .sidebar-section-title {
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 10px;
            color: #bdc3c7;
            font-size: 0.9em;
            text-transform: uppercase;
        }

        .task-list {
            list-style: none;
            padding: 0;
            margin: 0;
            overflow-y: auto;
            flex-grow: 1;
        }

        .task-list li {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #34495e;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }
        .task-list li:hover, .task-list li.active {
            background-color: #34495e;
        }
        .task-list li .task-name {
            font-size: 0.95em;
        }
        .task-list li .task-actions button {
            background: none;
            border: none;
            color: #95a5a6;
            cursor: pointer;
            padding: 3px;
            font-size: 0.9em;
        }
         .task-list li .task-actions button:hover {
            color: #ecf0f1;
        }

        .management-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .management-list li {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
            transition: background-color 0.2s ease;
            font-size: 0.95em;
        }
        .management-list li:hover, .management-list li.active {
            background-color: #34495e;
        }
        .management-list li .management-item-name {
            display: flex;
            align-items: center;
        }
        .management-list li i { /* For icons */
            margin-right: 8px;
            width: 16px; /* Ensure icon takes space */
            text-align: center;
        }

        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background-color: #ffffff;
            /* overflow: hidden; */ /* Reverted */
        }

        .main-header {
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            background-color: #f8f9fa; /* Restored from user's previous diff */
            /* background-color: #f0f2f5; */ /* Match body or keep white */
            /* overflow: hidden; */ /* Reverted */
        }

        .main-header input[type="text"] {
            flex-grow: 1;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 1em;
            margin-right: 10px;
        }

        .main-header .run-button {
            background-color: #27ae60; /* Green run button */
            color: white;
            border: none;
            padding: 8px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 1em;
            cursor: pointer;
            border-radius: 4px;
        }
        .main-header .run-button:hover {
            background-color: #229954;
        }

        .main-tabs-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background-color: #ffffff;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            padding: 0 20px;
            background-color: #f8f9fa;
            align-items: center;
        }
        .tabs .tab-button {
            padding: 12px 20px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 0.95em;
            border-bottom: 3px solid transparent;
            margin-bottom: -1px; /* Overlap with container border */
            transition: all 0.2s ease;
        }
        .tabs .tab-button.active {
            border-bottom: 3px solid #3498db;
            color: #3498db;
            font-weight: bold;
            background-color: #ffffff;
        }
        .tabs .tab-button:hover {
            background-color: #e9ecef;
        }
        .tabs .active-tab-indicator { /* Optional: if we want a more distinct tab name area */
            flex-grow: 1;
            text-align: left;
            padding-left: 10px;
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
        }
        .tab-content {
            display: none;
            flex-grow: 1;
            overflow-y: auto;
        }
        .tab-content.active {
            display: block;
        }

        .config-container {
            padding: 20px;
            max-width: 1200px; /* Good for readability */
            margin: 0 auto; /* Center the container */
        }

        /* Styling for form sections to look like cards */
        .form-section {
            background-color: #ffffff; /* Sections will be white cards */
            border-radius: 8px;
            padding: 25px; /* Increased padding */
            margin-bottom: 25px; /* Space between sections */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* Softer shadow */
            transition: box-shadow 0.3s ease;
        }
        .form-section:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        }

        .form-section h4 {
            font-size: 1.3em; /* Slightly larger heading */
            color: #2c3e50; /* Dark blue, matches sidebar */
            margin-top: 0; /* Remove default top margin */
            margin-bottom: 20px; /* Space below heading */
            padding-bottom: 15px; /* Space above border */
            border-bottom: 1px solid #e9ecef; /* Lighter border */
            font-weight: 600;
        }

        /* Queue item enhancements */
        .queue-item {
            padding: 20px; /* Increased padding */
            border: 1px solid #e0e0e0; /* Slightly darker border for definition */
            box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow for queue items */
        }
        .queue-item-header h5 {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
        }

        /* 保存任务按钮样式 */
        .save-task-btn {
            padding: 8px 16px;
            background-color: #27ae60;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .save-task-btn:hover {
            background-color: #229954;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 0.9em;
        }
        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 0.95em;
        }
         .form-group textarea {
            min-height: 80px;
            resize: vertical;
         }


        .status-bar {
            margin-bottom: 15px;
            padding: 15px 20px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .status-bar span {
            margin-right: 15px;
        }
        .status-bar .status-code.success { color: #27ae60; font-weight: bold; }
        .status-bar .status-code.error { color: #c0392b; font-weight: bold; }

        .log-controls {
            padding: 10px 20px;
            background-color: #f8f9fa; /* Light background for controls */
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .log-controls label {
            font-size: 0.9em;
            margin-right: 5px;
        }
        .log-controls input[type="checkbox"] {
            margin-right: 10px;
        }
        .log-controls button {
            padding: 6px 12px;
            font-size: 0.85em;
            border-radius: 4px;
            cursor: pointer;
            background-color: #6c757d; /* Bootstrap secondary-like color */
            color: white;
            border: none;
        }
        .log-controls button:hover {
            background-color: #5a6268;
        }

        .response-data {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            min-height: 400px;
            white-space: pre-wrap; /* To show formatted JSON or text */
            font-family: monospace;
            font-size: 0.9em;
            margin: 0 20px 20px 20px;
            display: flex; /* Added for new log line structure */
            flex-direction: column; /* Added for new log line structure */
        }
        .response-data span { /* Style for individual log lines */
            display: block; /* Each log on a new line */
            padding: 2px 0; /* Minimal padding */
        }
        .log-info { color: #333; } /* Default log color */
        .log-warning { color: #ffc107; font-weight: bold; } /* Bootstrap warning yellow */
        .log-error { color: #dc3545; font-weight: bold; } /* Bootstrap danger red */
        .log-success { color: #28a745; font-weight: bold; } /* Bootstrap success green */

        /* Simple two-column grid for queue items */
        .queue-item {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .queue-item .form-group {
            margin-bottom: 5px; /* Reduce margin for items in grid */
        }
        .queue-item-header {
            grid-column: 1 / -1; /* Span full width */
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
         .queue-item-header h5 { margin: 0; font-size: 1em; }
         .queue-item-header button { background: none; border: none; color: #c0392b; cursor: pointer; }

        /* Modal Styles */
        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1000; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto; /* 10% from the top and centered */
            padding: 0;
            border: 1px solid #888;
            width: 60%; /* Could be more or less, depending on screen size */
            border-radius: 5px;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 15px 20px;
            background-color: #2c3e50; /* Match sidebar header */
            color: #ecf0f1;
            border-bottom: 1px solid #34495e;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        .modal-header h4 {
            margin: 0;
            font-size: 1.2em;
        }

        .modal-close-btn {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            background: none;
            border: none;
            cursor: pointer;
        }

        .modal-close-btn:hover,
        .modal-close-btn:focus {
            color: #ecf0f1;
            text-decoration: none;
        }

        .modal-body {
            padding: 20px;
            flex-grow: 1;
            overflow-y: auto;
            min-height: 200px; /* Minimum height for content */
            max-height: 60vh; /* Max height, scroll for more */
        }

        .modal-footer {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            text-align: right;
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        .modal-footer button {
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .modal-footer .btn-primary {
            background-color: #3498db;
            color: white;
            border: 1px solid #3498db;
        }
        .modal-footer .btn-secondary {
            background-color: #e0e0e0;
            color: #333;
            border: 1px solid #ccc;
        }

        /* For side-by-side layout of columns */
        .row {
            display: flex;
            gap: 15px; /* Space between the columns */
            margin-bottom: 15px; /* Spacing below the row, similar to form-group */
        }
        .row .col-md-6 { /* When used for two columns */
            flex: 1; /* Each column takes equal share of the space */
        }

        .history-controls {
            padding: 10px 0; /* Padding only top and bottom */
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        .history-controls button {
             padding: 6px 12px;
            font-size: 0.85em;
            border-radius: 4px;
            cursor: pointer;
            background-color: #6c757d;
            color: white;
            border: none;
        }
        .history-controls button:hover {
            background-color: #5a6268;
        }
        .history-summary {
            font-size: 0.95em;
            color: #555;
        }

        .history-item {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-left-width: 4px; /* For status indicator */
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .history-item.success {
            border-left-color: #28a745; /* Green for success */
        }
        .history-item.error {
            border-left-color: #dc3545; /* Red for error */
        }
        .history-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .history-item-header strong {
            font-size: 1.05em;
            color: #333;
        }
        .history-item-details {
            font-size: 0.9em;
            color: #555;
            line-height: 1.5;
        }
        .history-item-details span {
            margin-right: 10px;
            display: inline-block; /* Allow margin */
        }

    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>KafkaTool</h2>
        </div>
        <button class="sidebar-new-task-btn">＋ 新建任务</button>

        <div class="sidebar-section-title">我的任务</div>
        <ul class="task-list" id="taskList">
            <li class="active">
                <span class="task-name">订单处理模拟</span>
                <span class="task-actions">
                    <button title="运行">▶</button>
                    <button title="复制">❏</button>
                    <button title="删除">🗑</button>
                </span>
            </li>
            <li>
                <span class="task-name">日志收集测试</span>
                 <span class="task-actions">
                    <button title="运行">▶</button>
                    <button title="复制">❏</button>
                    <button title="删除">🗑</button>
                </span>
            </li>
            <li>
                <span class="task-name">用户行为分析</span>
                 <span class="task-actions">
                    <button title="运行">▶</button>
                    <button title="复制">❏</button>
                    <button title="删除">🗑</button>
                </span>
            </li>
        </ul>

        <div class="sidebar-section-title">管理</div>
        <ul class="management-list">
            <li id="env-management-btn">
                <span class="management-item-name">
                    <i class="icon-globe">🌐</i> 环境变量
                </span>
            </li>
            <li id="tpl-management-btn">
                 <span class="management-item-name">
                    <i class="icon-template">📄</i> 消息模板
                </span>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="main-header">
            <input type="text" id="taskNameInput" value="订单处理模拟" placeholder="任务名称">
            <button class="run-button">▶ 运行</button>
        </div>

        <div class="main-tabs-container">
            <div class="tabs">
                <button class="tab-button active" data-tab="config">任务配置</button>
                <button class="tab-button" data-tab="run-output">运行输出</button>
                <button class="tab-button" data-tab="run-history">运行历史</button>
                <button class="tab-button" data-tab="charts">统计图表</button>
                <!-- Optional: Placeholder for active tab name or other controls -->
                <!-- <div class="active-tab-indicator" id="activeTabIndicator">任务配置</div> -->
            </div>

            <!-- 任务配置Tab -->
            <div id="configTabContent" class="tab-content active">
                <div class="config-container">
                    <!-- 基本设置 -->
                    <div class="form-section">
                        <h4>基本设置</h4>
                        <div class="form-group">
                            <label for="taskDescription">任务描述</label>
                            <input type="text" id="taskDescription" value="模拟电商平台的订单处理流程">
                        </div>
                        <div class="form-group" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <label for="cycleDuration">周期时长 (秒)</label>
                                <input type="number" id="cycleDuration" value="60">
                            </div>
                            <div>
                                <label for="totalDuration">总运行时长 (秒, 0为无限)</label>
                                <input type="number" id="totalDuration" value="300">
                            </div>
                        </div>
                    </div>

                    <!-- 队列配置 -->
                    <div class="form-section">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4>队列配置</h4>
                            <button id="addQueueBtn" style="padding: 5px 10px; font-size: 0.9em;">＋ 添加队列</button>
                        </div>
                        <div id="queuesContainer">
                            <!-- Queue items will be dynamically added here -->
                            <div class="queue-item">
                                 <div class="queue-item-header">
                                    <h5>队列 1</h5>
                                    <button>✕</button>
                                </div>
                                <div class="form-group">
                                    <label>消息模板</label>
                                    <select>
                                        <option>订单创建模板</option>
                                        <option>支付成功模板</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Topic (从模板自动获取)</label>
                                    <input type="text" value="orders.new" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>每周期发送次数</label>
                                    <input type="number" value="10">
                                </div>
                            </div>
                             <div class="queue-item">
                                 <div class="queue-item-header">
                                    <h5>队列 2</h5>
                                    <button>✕</button>
                                </div>
                                <div class="form-group">
                                    <label>消息模板</label>
                                    <select>
                                        <option>库存更新模板</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Topic (从模板自动获取)</label>
                                    <input type="text" value="inventory.update" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>每周期发送次数</label>
                                    <input type="number" value="5">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 环境配置 -->
                    <div class="form-section">
                        <h4>环境配置</h4>
                        <div class="form-group">
                            <label for="environmentSelect">选择环境</label>
                            <select id="environmentSelect">
                                <option value="dev">开发环境 (DEV)</option>
                                <option value="staging">预发环境 (Staging)</option>
                                <option value="prod">生产环境 (PROD)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="envVariablesPreview">环境变量预览 (只读)</label>
                            <textarea id="envVariablesPreview" readonly rows="8" style="background-color: #f8f9fa; font-family: 'Courier New', monospace; font-size: 13px; box-sizing: border-box;">KAFKA_BROKERS=kafka-dev:9092
SCHEMA_REGISTRY_URL=http://schema-registry-dev:8081</textarea>
                        </div>
                    </div>

                    <!-- Save Task Button Container -->
                    <div style="text-align: right; margin-top: 10px; margin-bottom: 20px; padding-right: 5px;">
                        <button class="save-task-btn" id="saveTaskBtn" title="保存当前任务配置">
                            💾 保存任务
                        </button>
                    </div>
                </div>
            </div>

            <!-- 运行输出Tab -->
            <div id="runOutputTabContent" class="tab-content">
                <div class="log-controls">
                    <button id="clearLogBtn">清除日志</button>
                    <div>
                        <input type="checkbox" id="autoScrollLog" checked>
                        <label for="autoScrollLog">自动滚动</label>
                    </div>
                    <input type="text" placeholder="筛选日志..." style="padding: 6px; border: 1px solid #ccc; border-radius: 4px; font-size: 0.9em;">
                </div>
                <div class="status-bar">
                    <span>状态: <span id="runStatus" class="status-code success">运行中</span></span>
                    <span>消息数: <span id="runMessageCount">125</span></span>
                    <span>周期数: <span id="runCycleCount">2</span></span>
                    <span>错误数: <span id="runErrorCount" class="status-code error">1</span></span>
                    <span>已运行时长: <span id="runElapsedTime">00:01:30</span></span>
                </div>
                <div class="response-data" id="consoleOutput">
                    <span class="log-info">[2023-10-27 10:00:01] INFO: 任务 "订单处理模拟" 开始运行...</span>
                    <span class="log-info">[2023-10-27 10:00:02] INFO: 队列 1 (orders.new) 发送消息: {"orderId": "xyz123", "amount": 99.99}</span>
                    <span class="log-info">[2023-10-27 10:00:02] INFO: 队列 2 (inventory.update) 发送消息: {"itemId": "item001", "stock": 99}</span>
                    <span class="log-warning">[2023-10-27 10:01:02] WARNING: 队列 1 (orders.new) 消息发送超时.</span>
                    <span class="log-error">[2023-10-27 10:01:05] ERROR: 队列 2 (inventory.update) 连接 Kafka 失败.</span>
                    <span class="log-info">[2023-10-27 10:01:10] INFO: 周期 2 完成.</span>
                </div>
            </div>

            <!-- 运行历史Tab -->
            <div id="runHistoryTabContent" class="tab-content">
                <div style="padding: 20px;">
                    <div class="history-controls">
                        <button id="refreshHistoryBtn">刷新历史</button>
                        <input type="text" placeholder="筛选历史..." style="padding: 6px; border: 1px solid #ccc; border-radius: 4px; font-size: 0.9em;">
                        <span class="history-summary">共 3 条记录 (2 成功, 1 失败)</span>
                    </div>
                    <h4>本次运行历史</h4>
                    <div id="historyItemsContainer">
                        <!-- History items will be dynamically added or listed here -->
                        <div class="history-item success">
                            <div class="history-item-header">
                                <strong>2023-10-27 10:00:02 - 发送成功</strong>
                                <span>消息ID: msg-001</span>
                            </div>
                            <div class="history-item-details">
                                <span>队列: 队列1</span> | <span>Topic: orders.new</span>
                                <p style="margin-top: 5px; font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px;">请求数据: {"orderId": "xyz123", ...}</p>
                            </div>
                        </div>
                        <div class="history-item success">
                            <div class="history-item-header">
                                <strong>2023-10-27 10:00:02 - 发送成功</strong>
                                <span>消息ID: msg-002</span>
                            </div>
                            <div class="history-item-details">
                                <span>队列: 队列2</span> | <span>Topic: inventory.update</span>
                                <p style="margin-top: 5px; font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px;">请求数据: {"itemId": "item001", ...}</p>
                            </div>
                        </div>
                        <div class="history-item error">
                            <div class="history-item-header">
                                <strong>2023-10-27 10:01:02 - 发送失败</strong>
                                <span>Topic: orders.new</span>
                            </div>
                            <div class="history-item-details">
                                <span>队列: 队列1</span>
                                <p style="margin-top: 5px; color: #c0392b;">错误: 连接超时, 未能发送消息。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计图表Tab -->
            <div id="chartsTabContent" class="tab-content">
                <div style="padding: 20px;">
                    <h4>统计图表</h4>
                    <div style="background: #fff; border: 1px solid #e0e0e0; border-radius: 4px; padding: 15px;">
                        <p>这里将显示指定时间范围内的消息发送数量统计。</p>
                        <div style="margin-top: 20px;">
                            <div style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; text-align: center; max-width: 600px; margin: 0 auto;">
                                <h5>消息发送数量统计</h5>
                                <p style="font-size: 0.85em; color: #666; margin-bottom: 10px;">(横坐标: 时间, 纵坐标: 发送消息数量)</p>
                                <div style="height: 250px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #666; border-radius: 3px;">
                                    📊 图表区域
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Environment Management Modal -->
    <div id="envManagementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4>环境变量管理</h4>
                <button class="modal-close-btn" data-modal-id="envManagementModal">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Environment management UI elements -->
                <div style="display: grid; grid-template-columns: 250px 1fr; gap: 20px; height: 400px;">
                    <!-- Left side - Environment List -->
                    <div style="border-right: 1px solid #e0e0e0; padding-right: 15px;">
                        <div style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
                            <h5 style="margin: 0;">环境列表</h5>
                            <button class="btn-sm" style="padding: 4px 8px; background: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.8em;">新建</button>
                        </div>
                        <div class="env-list" style="height: 320px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px;">
                            <div class="env-list-item active" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                                <span>开发环境 (DEV)</span>
                                <span>
                                    <button title="复制" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">📋</button>
                                    <button title="删除" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">🗑</button>
                                </span>
                            </div>
                            <div class="env-list-item" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                                <span>测试环境 (TEST)</span>
                                <span>
                                    <button title="复制" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">📋</button>
                                    <button title="删除" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">🗑</button>
                                </span>
                            </div>
                            <div class="env-list-item" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                                <span>预发环境 (STAGING)</span>
                                <span>
                                    <button title="复制" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">📋</button>
                                    <button title="删除" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">🗑</button>
                                </span>
                            </div>
                            <div class="env-list-item" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                                <span>生产环境 (PROD)</span>
                                <span>
                                    <button title="复制" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">📋</button>
                                    <button title="删除" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">🗑</button>
                                </span>
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <button style="padding: 6px 10px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 3px; cursor: pointer; width: 100%;">导入环境...</button>
                        </div>
                    </div>

                    <!-- Right side - Environment Editor -->
                    <div>
                        <h5 style="margin-top: 0;">环境详情</h5>
                        <div class="form-group">
                            <label for="envName">环境名称</label>
                            <input type="text" id="envName" value="开发环境 (DEV)" style="width: 100%;">
                        </div>
                        <div class="form-group">
                            <label for="envDesc">描述 (可选)</label>
                            <input type="text" id="envDesc" value="本地开发环境配置" style="width: 100%;">
                        </div>
                        <div class="form-group">
                            <label for="envVars">环境变量 (K=V 格式, 每行一个)</label>
                            <textarea id="envVarsTextarea" rows="10" style="width: 100%; font-family: monospace; font-size: 0.9em;">KAFKA_BROKERS=kafka-dev:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-dev:8081\nTIMEOUT=5000\nCLIENT_ID=dev-client\nGROUP_ID=dev-group</textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="envManagementModal">取消</button>
                <button class="btn-primary">保存更改</button>
            </div>
        </div>
    </div>

    <!-- Message Templates Modal -->
    <div id="tplManagementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4>消息模板管理</h4>
                <button class="modal-close-btn" data-modal-id="tplManagementModal">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Template management UI elements -->
                <div style="display: grid; grid-template-columns: 250px 1fr; gap: 20px; height: 400px;">
                    <!-- Left side - Template List -->
                    <div style="border-right: 1px solid #e0e0e0; padding-right: 15px;">
                        <div style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
                            <h5 style="margin: 0;">模板列表</h5>
                            <button class="btn-sm" style="padding: 4px 8px; background: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.8em;">新建</button>
                        </div>
                        <div class="tpl-list" style="height: 320px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px;">
                            <div class="tpl-list-item active" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                                <span>订单创建模板</span>
                                <span>
                                    <button title="复制" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">📋</button>
                                    <button title="删除" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">🗑</button>
                                </span>
                            </div>
                            <div class="tpl-list-item" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                                <span>支付成功模板</span>
                                <span>
                                    <button title="复制" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">📋</button>
                                    <button title="删除" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">🗑</button>
                                </span>
                            </div>
                            <div class="tpl-list-item" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                                <span>库存更新模板</span>
                                <span>
                                    <button title="复制" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">📋</button>
                                    <button title="删除" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">🗑</button>
                                </span>
                            </div>
                            <div class="tpl-list-item" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                                <span>商品评价模板</span>
                                <span>
                                    <button title="复制" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">📋</button>
                                    <button title="删除" style="background: none; border: none; cursor: pointer; font-size: 0.9em;">🗑</button>
                                </span>
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <button style="padding: 6px 10px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 3px; cursor: pointer; width: 100%;">导入模板...</button>
                        </div>
                    </div>

                    <!-- Right side - Template Editor -->
                    <div>
                        <h5 style="margin-top: 0;">模板详情</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tplName" style="font-weight: 500; font-size: 14px;">模板名称</label>
                                    <input type="text" id="tplName" style="width: 100%;" value="订单创建模板">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tplTopic" style="font-weight: 500; font-size: 14px;">默认Topic</label>
                                    <input type="text" id="tplTopic" style="width: 100%;" placeholder="例如: orders.new">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="tplType">模板类型</label>
                            <select id="tplType" style="width: 100%;">
                                <option value="json" selected>JSON</option>
                                <option value="xml">XML</option>
                                <option value="plaintext">纯文本</option>
                                <option value="avro">Avro格式</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tplContent">模板内容</label>
                            <textarea id="tplContentTextarea" rows="10" style="width: 100%; font-family: monospace; font-size: 0.9em;">{
  "orderId": "{{random.uuid}}",
  "userId": "{{random.number(1000)}}",
  "items": [
    {
      "productId": "{{random.uuid}}",
      "name": "{{faker.commerce.productName}}",
      "price": {{random.float(10, 1000)}},
      "quantity": {{random.number(5)}}
    }
  ],
  "totalAmount": "{{expr.calculate('items[0].price * items[0].quantity')}}",
  "createdAt": "{{date.now}}"
}</textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="tplManagementModal">取消</button>
                <button class="btn-primary">保存模板</button>
            </div>
        </div>
    </div>

    <!-- New Task Modal -->
    <div id="newTaskModal" class="modal">
        <div class="modal-content" style="width: 70%; max-width: 900px;"> <!-- Wider for config -->
            <div class="modal-header">
                <h4>新建任务</h4>
                <button class="modal-close-btn" data-modal-id="newTaskModal">&times;</button>
            </div>
            <div class="modal-body" style="max-height: 70vh;">
                <!-- Content will be similar to configTabContent -->
                <div class="config-container" style="padding-top: 0;">
                    <!-- 基本设置 -->
                    <div class="form-section">
                        <h4>基本设置</h4>
                        <div class="form-group">
                            <label for="newTask_taskName">任务名称</label>
                            <input type="text" id="newTask_taskName" placeholder="请输入任务名称">
                        </div>
                        <div class="form-group">
                            <label for="newTask_taskDescription">任务描述</label>
                            <input type="text" id="newTask_taskDescription" placeholder="请输入任务描述">
                        </div>
                        <div class="form-group" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <label for="newTask_cycleDuration">周期时长 (秒)</label>
                                <input type="number" id="newTask_cycleDuration" value="60">
                            </div>
                            <div>
                                <label for="newTask_totalDuration">总运行时长 (秒, 0为无限)</label>
                                <input type="number" id="newTask_totalDuration" value="0">
                            </div>
                        </div>
                    </div>

                    <!-- 队列配置 -->
                    <div class="form-section">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4>队列配置</h4>
                            <button id="newTask_addQueueBtn" style="padding: 5px 10px; font-size: 0.9em;">＋ 添加队列</button>
                        </div>
                        <div id="newTask_queuesContainer">
                            <!-- New task queue items will be dynamically added here -->
                            <div class="queue-item">
                                 <div class="queue-item-header">
                                    <h5>队列 1</h5>
                                    <button class="remove-queue-btn">✕</button>
                                </div>
                                <div class="form-group">
                                    <label>消息模板</label>
                                    <select class="newTask_templateSelect">
                                        <option value="">选择模板...</option>
                                        <!-- TODO: Populate with actual templates from tplManagementModal or a shared source -->
                                        <option value="tpl1">订单创建模板 (示例)</option>
                                        <option value="tpl2">支付成功模板 (示例)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Topic (从模板自动获取)</label>
                                    <input type="text" class="newTask_topicInput" placeholder="请先选择消息模板" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>每周期发送次数</label>
                                    <input type="number" value="10">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 环境配置 -->
                    <div class="form-section">
                        <h4>环境配置</h4>
                        <div class="form-group">
                            <label for="newTask_environmentSelect">选择环境</label>
                            <select id="newTask_environmentSelect">
                                <!-- Options will be populated by JS -->
                                <option value="dev">开发环境 (DEV)</option>
                                <option value="staging">预发环境 (Staging)</option>
                                <option value="prod">生产环境 (PROD)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="newTask_envVariablesPreview">环境变量预览 (只读)</label>
                            <textarea id="newTask_envVariablesPreview" readonly rows="5" style="background-color: #f8f9fa; font-family: 'Courier New', monospace; font-size: 13px; box-sizing: border-box;"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="newTaskModal">取消</button>
                <button class="btn-primary" id="saveNewTaskBtn">保存任务</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Helper function to convert kebab-case to camelCase
            function kebabToCamel(kebabCaseString) {
                return kebabCaseString.replace(/-([a-z])/g, function (match, letter) {
                    return letter.toUpperCase();
                });
            }

            const mainTabsContainer = document.querySelector('.main-tabs-container');
            if (mainTabsContainer) {
                const tabButtons = mainTabsContainer.querySelectorAll(':scope > .tabs .tab-button'); // Use :scope for direct children search of .tabs
                const tabContentPanels = mainTabsContainer.querySelectorAll(':scope > .tab-content'); // Use :scope for direct children search

                console.log('Found tab buttons:', tabButtons.length);
                console.log('Found tab content panels:', tabContentPanels.length);

                tabButtons.forEach(button => {
                    button.addEventListener('click', function () {
                        console.log('Tab button clicked:', this.dataset.tab);

                        // Deactivate all buttons in this group
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        // Deactivate all content panels in this group
                        tabContentPanels.forEach(panel => panel.classList.remove('active'));

                        // Activate clicked button
                        this.classList.add('active');

                        // Activate corresponding content panel
                        const targetTabKey = this.dataset.tab; 
                        const camelCaseKey = kebabToCamel(targetTabKey); 
                        const targetPanelId = camelCaseKey + 'TabContent'; 
                        
                        console.log('Attempting to find panel with ID:', targetPanelId);
                        // Query within mainTabsContainer for the specific panel ID
                        const targetPanel = mainTabsContainer.querySelector('#' + targetPanelId);

                        if (targetPanel) {
                            targetPanel.classList.add('active');
                            console.log("Successfully activated panel:", targetPanelId);
                        } else {
                            console.error("Could not find tab content panel with ID:", targetPanelId, "Derived from data-tab:", targetTabKey);
                        }
                    });
                });

                // Ensure initial active state is correctly set (e.g., first tab and panel active)
                if (tabButtons.length > 0 && tabContentPanels.length > 0) {
                    let hasActiveButton = false;
                    tabButtons.forEach(btn => { if(btn.classList.contains('active')) hasActiveButton = true; });
                    
                    let hasActivePanel = false;
                    tabContentPanels.forEach(panel => { if(panel.classList.contains('active')) hasActivePanel = true; });

                    if (!hasActiveButton) {
                        console.log("No active button found, activating first button.");
                        tabButtons[0].classList.add('active');
                    }
                    if (!hasActivePanel) {
                         const firstButtonDataTab = tabButtons[0].dataset.tab;
                         const firstPanelId = kebabToCamel(firstButtonDataTab) + 'TabContent';
                         const firstPanel = mainTabsContainer.querySelector('#' + firstPanelId);
                         if (firstPanel) {
                            console.log("No active panel found, activating first panel:", firstPanelId);
                            firstPanel.classList.add('active');
                         } else {
                            console.error("Could not find first panel to activate:", firstPanelId);
                         }
                    }
                }

            } else {
                console.error(".main-tabs-container not found! Tab functionality will not work.");
            }
            
            // Sidebar task list item click
            const taskItems = document.querySelectorAll('.task-list li');
            taskItems.forEach(item => {
                item.addEventListener('click', function(event) {
                    if (event.target.tagName === 'BUTTON') return; // Ignore clicks on action buttons
                    taskItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    document.getElementById('taskNameInput').value = this.querySelector('.task-name').textContent;
                    // Here you would typically load the actual task data
                });
            });

            // Add queue button (very basic)
            const addQueueBtn = document.getElementById('addQueueBtn');
            const queuesContainer = document.getElementById('queuesContainer');
            let queueCounter = queuesContainer.children.length;

            if(addQueueBtn) {
                addQueueBtn.addEventListener('click', function() {
                    queueCounter++;
                    const newQueueItem = document.createElement('div');
                    newQueueItem.classList.add('queue-item');
                    newQueueItem.innerHTML = `
                        <div class="queue-item-header">
                            <h5>队列 ${queueCounter}</h5>
                            <button class="remove-queue-btn">✕</button>
                        </div>
                        <div class="form-group">
                            <label>消息模板</label>
                            <select><option>选择模板...</option></select>
                        </div>
                        <div class="form-group">
                            <label>Topic (从模板自动获取)</label>
                            <input type="text" placeholder="请先选择消息模板" readonly style="background-color: #f8f9fa;">
                        </div>
                        <div class="form-group">
                            <label>每周期发送次数</label>
                            <input type="number" value="10">
                        </div>
                    `;
                    queuesContainer.appendChild(newQueueItem);
                    newQueueItem.querySelector('.remove-queue-btn').addEventListener('click', function() {
                        this.closest('.queue-item').remove();
                        // Re-number remaining queues if necessary (more complex)
                    });
                });
            }

            // Initial setup for remove buttons on pre-existing queues
            document.querySelectorAll('.remove-queue-btn').forEach(button => {
                 button.addEventListener('click', function() {
                    this.closest('.queue-item').remove();
                });
            });

            // Environment select change
            const environmentSelect = document.getElementById('environmentSelect');
            const envVariablesPreview = document.getElementById('envVariablesPreview');
            if(environmentSelect && envVariablesPreview) {
                environmentSelect.addEventListener('change', function() {
                    // Mock data - in reality, fetch this from backend
                    const mockEnvData = {
                        dev: "KAFKA_BROKERS=kafka-dev:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-dev:8081",
                        staging: "KAFKA_BROKERS=kafka-staging:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-staging:8081",
                        prod: "KAFKA_BROKERS=kafka-prod1:9092,kafka-prod2:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-prod:8081"
                    };
                    envVariablesPreview.value = mockEnvData[this.value] || "请选择一个环境查看变量。";
                });
                 // Trigger change on load to populate based on default selection
                environmentSelect.dispatchEvent(new Event('change'));
            }

            function openEnvironments(element) {
                console.log('Function openEnvironments called');
                // Show the environment management modal
                const modal = document.getElementById('envManagementModal');
                console.log('Environment modal element:', modal);
                if (modal) {
                    modal.style.display = 'block';
                    console.log('Set environment modal display to block');
                }

                // Setup event handlers for the environment list items
                setupEnvListItemHandlers();

                // Keep sidebar item active
                setActiveSidebarItem(element);
            }

            function openMessageTemplates(element) {
                console.log('Function openMessageTemplates called');
                // Show the template management modal
                const modal = document.getElementById('tplManagementModal');
                console.log('Template modal element:', modal);
                if (modal) {
                    modal.style.display = 'block';
                    console.log('Set template modal display to block');
                }

                // Setup event handlers for the template list items
                setupTplListItemHandlers();

                // Keep sidebar item active
                setActiveSidebarItem(element);
            }

            function setupEnvListItemHandlers() {
                // Add click event to environment list items to select them
                document.querySelectorAll('.env-list-item').forEach(item => {
                    item.addEventListener('click', function(e) {
                        // Ignore if clicked on buttons
                        if (e.target.tagName === 'BUTTON') return;

                        // Update active state
                        document.querySelectorAll('.env-list-item').forEach(i => i.classList.remove('active'));
                        this.classList.add('active');

                        // Here you would typically load the selected environment data
                        // For now we'll just simulate by updating the form fields with hardcoded values
                        const envName = this.querySelector('span:first-child').textContent;
                        document.getElementById('envName').value = envName;

                        // Set mock description and variables based on environment
                        if (envName.includes('DEV')) {
                            document.getElementById('envDesc').value = '本地开发环境配置';
                            document.getElementById('envVarsTextarea').value = 'KAFKA_BROKERS=kafka-dev:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-dev:8081\nTIMEOUT=5000\nCLIENT_ID=dev-client\nGROUP_ID=dev-group';
                        } else if (envName.includes('TEST')) {
                            document.getElementById('envDesc').value = '测试环境配置';
                            document.getElementById('envVarsTextarea').value = 'KAFKA_BROKERS=kafka-test:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-test:8081\nTIMEOUT=8000\nCLIENT_ID=test-client\nGROUP_ID=test-group';
                        } else if (envName.includes('STAGING')) {
                            document.getElementById('envDesc').value = '预发环境配置';
                            document.getElementById('envVarsTextarea').value = 'KAFKA_BROKERS=kafka-staging:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-staging:8081\nTIMEOUT=10000\nCLIENT_ID=staging-client\nGROUP_ID=staging-group';
                        } else if (envName.includes('PROD')) {
                            document.getElementById('envDesc').value = '生产环境配置';
                            document.getElementById('envVarsTextarea').value = 'KAFKA_BROKERS=kafka-prod1:9092,kafka-prod2:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-prod:8081\nTIMEOUT=15000\nCLIENT_ID=prod-client\nGROUP_ID=prod-group';
                        }
                    });
                });
            }

            function setupTplListItemHandlers() {
                // Add click event to template list items to select them
                document.querySelectorAll('.tpl-list-item').forEach(item => {
                    item.addEventListener('click', function(e) {
                        // Ignore if clicked on buttons
                        if (e.target.tagName === 'BUTTON') return;

                        // Update active state
                        document.querySelectorAll('.tpl-list-item').forEach(i => i.classList.remove('active'));
                        this.classList.add('active');

                        // Here you would typically load the selected template data
                        // For now we'll just simulate by updating the form fields with hardcoded values
                        const tplName = this.querySelector('span:first-child').textContent;
                        document.getElementById('tplName').value = tplName;

                        // Set mock content based on template name
                        if (tplName.includes('订单创建')) {
                            document.getElementById('tplType').value = 'json';
                            document.getElementById('tplContentTextarea').value = `{
  "orderId": "{{random.uuid}}",
  "userId": "{{random.number(1000)}}",
  "items": [
    {
      "productId": "{{random.uuid}}",
      "name": "{{faker.commerce.productName}}",
      "price": {{random.float(10, 1000)}},
      "quantity": {{random.number(5)}}
    }
  ],
  "totalAmount": "{{expr.calculate('items[0].price * items[0].quantity')}}",
  "createdAt": "{{date.now}}"
}`;
                        } else if (tplName.includes('支付成功')) {
                            document.getElementById('tplType').value = 'json';
                            document.getElementById('tplContentTextarea').value = `{
  "orderId": "{{random.uuid}}",
  "paymentId": "{{random.uuid}}",
  "amount": {{random.float(10, 1000)}},
  "paymentMethod": "{{random.arrayElement(['信用卡', '微信支付', '支付宝'])}}",
  "status": "SUCCESS",
  "paidAt": "{{date.now}}"
}`;
                        } else if (tplName.includes('库存更新')) {
                            document.getElementById('tplType').value = 'json';
                            document.getElementById('tplContentTextarea').value = `{
  "productId": "{{random.uuid}}",
  "name": "{{faker.commerce.productName}}",
  "previousStock": {{random.number(100)}},
  "currentStock": {{random.number(100)}},
  "warehouseId": "{{random.arrayElement(['WH001', 'WH002', 'WH003'])}}",
  "updatedAt": "{{date.now}}"
}`;
                        } else if (tplName.includes('商品评价')) {
                            document.getElementById('tplType').value = 'json';
                            document.getElementById('tplContentTextarea').value = `{
  "reviewId": "{{random.uuid}}",
  "orderId": "{{random.uuid}}",
  "productId": "{{random.uuid}}",
  "userId": "{{random.number(1000)}}",
  "rating": {{random.number(5)}},
  "comment": "{{faker.lorem.paragraph}}",
  "reviewedAt": "{{date.now}}"
}`;
                        }
                    });
                });
            }

            function setActiveSidebarItem(target) {
                // Remove 'active' from all task-list items
                document.querySelectorAll('.task-list li').forEach(item => item.classList.remove('active'));
                // Remove 'active' from all management-list items
                document.querySelectorAll('.management-list li').forEach(item => item.classList.remove('active'));
                // Add 'active' to the clicked item
                if (target) {
                    target.classList.add('active');
                }
            }

            // Initial load: make the first task active by default if no other state is saved.
            function initializeActiveItem() {
                if (!document.querySelector('.task-list li.active') && !document.querySelector('.management-list li.active')) {
                    const firstTask = document.querySelector('.task-list li');
                    if (firstTask) {
                        firstTask.click(); // This will also call setActiveSidebarItem
                    }
                }
            }

            initializeActiveItem(); // Call on script load

            // Bind click events for environment and template management buttons
            document.getElementById('env-management-btn').addEventListener('click', function() {
                console.log('Opening environment management modal');
                openEnvironments(this);
            });

            document.getElementById('tpl-management-btn').addEventListener('click', function() {
                console.log('Opening template management modal');
                openMessageTemplates(this);
            });

            // Modal close logic
            document.querySelectorAll('.modal-close-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const modalId = this.dataset.modalId;
                    const modalToClose = document.getElementById(modalId);
                    if (modalToClose) modalToClose.style.display = 'none';
                });
            });

            // Also close modal if secondary/cancel button in footer is clicked
            document.querySelectorAll('.modal-footer .btn-secondary').forEach(button => {
                button.addEventListener('click', function() {
                    const modalId = this.dataset.modalId;
                    const modalToClose = document.getElementById(modalId);
                    if (modalToClose) modalToClose.style.display = 'none';
                });
            });

            // Add an active class styling for env-list-item and tpl-list-item
            document.head.insertAdjacentHTML('beforeend', `
                <style>
                    .env-list-item.active, .tpl-list-item.active {
                        background-color: #edf7ff;
                        border-left: 3px solid #3498db;
                    }
                </style>
            `);

            // Check that modals exist in the DOM
            console.log('Environment modal exists:', !!document.getElementById('envManagementModal'));
            console.log('Template modal exists:', !!document.getElementById('tplManagementModal'));

            // Close modal when clicking outside of the modal-content
            window.onclick = function(event) {
                document.querySelectorAll('.modal').forEach(modal => {
                    if (event.target == modal) {
                        modal.style.display = "none";
                    }
                });
            }

            const newTaskModal = document.getElementById('newTaskModal');
            const newTaskForm = {
                taskName: document.getElementById('newTask_taskName'),
                taskDescription: document.getElementById('newTask_taskDescription'),
                cycleDuration: document.getElementById('newTask_cycleDuration'),
                totalDuration: document.getElementById('newTask_totalDuration'),
                queuesContainer: document.getElementById('newTask_queuesContainer'),
                addQueueBtn: document.getElementById('newTask_addQueueBtn'),
                environmentSelect: document.getElementById('newTask_environmentSelect'),
                envVariablesPreview: document.getElementById('newTask_envVariablesPreview'),
                saveBtn: document.getElementById('saveNewTaskBtn'),
                // templateSelects and topicInputs will be handled dynamically for queues
            };

            // 1. Open New Task Modal
            const newTaskButton = document.querySelector('.sidebar-new-task-btn');
            if (newTaskButton && newTaskModal) {
                newTaskButton.addEventListener('click', function() {
                    console.log("New Task button clicked");
                    // Reset form fields (basic example)
                    if(newTaskForm.taskName) newTaskForm.taskName.value = '';
                    if(newTaskForm.taskDescription) newTaskForm.taskDescription.value = '';
                    if(newTaskForm.cycleDuration) newTaskForm.cycleDuration.value = '60';
                    if(newTaskForm.totalDuration) newTaskForm.totalDuration.value = '0';
                    if(newTaskForm.queuesContainer) newTaskForm.queuesContainer.innerHTML = ''; // Clear existing queues
                    // Add one default queue item
                    addNewQueueItem(newTaskForm.queuesContainer, true);
                    
                    populateNewTaskEnvironmentSelect();
                    updateNewTaskEnvPreview(); // Initial preview update
                    
                    newTaskModal.style.display = 'block';
                    setActiveSidebarItem(this); // Optional: make new task button active
                });
            }

            // 2. Populate Environment Select in New Task Modal & Handle Preview
            function populateNewTaskEnvironmentSelect() {
                const existingEnvSelect = document.getElementById('environmentSelect'); // From main config
                if (existingEnvSelect && newTaskForm.environmentSelect) {
                    newTaskForm.environmentSelect.innerHTML = existingEnvSelect.innerHTML;
                    // Ensure event listener for preview is attached
                    if (!newTaskForm.environmentSelect.dataset.listenerAttached) {
                        newTaskForm.environmentSelect.addEventListener('change', updateNewTaskEnvPreview);
                        newTaskForm.environmentSelect.dataset.listenerAttached = 'true';
                    }
                }
            }
            function updateNewTaskEnvPreview() {
                if (newTaskForm.environmentSelect && newTaskForm.envVariablesPreview) {
                    const selectedEnv = newTaskForm.environmentSelect.value;
                    // Mock data - in reality, fetch this from backend or shared config
                    const mockEnvData = {
                        dev: "KAFKA_BROKERS=kafka-dev:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-dev:8081\nNEW_TASK_SPECIFIC=true",
                        staging: "KAFKA_BROKERS=kafka-staging:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-staging:8081\nNEW_TASK_SPECIFIC=true",
                        prod: "KAFKA_BROKERS=kafka-prod1:9092,kafka-prod2:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-prod:8081\nNEW_TASK_SPECIFIC=true"
                    };
                    newTaskForm.envVariablesPreview.value = mockEnvData[selectedEnv] || "请选择一个环境查看变量。";
                }
            }

            // 3. Add Queue Button for New Task Modal
            let newTaskQueueCounter = 0;
            if (newTaskForm.addQueueBtn) {
                newTaskForm.addQueueBtn.addEventListener('click', function() {
                    addNewQueueItem(newTaskForm.queuesContainer, false);
                });
            }

            function addNewQueueItem(container, isFirst) {
                newTaskQueueCounter = isFirst ? 1 : container.children.length + 1;
                const newQueueItem = document.createElement('div');
                newQueueItem.classList.add('queue-item');
                newQueueItem.innerHTML = `
                    <div class="queue-item-header">
                        <h5>队列 ${newTaskQueueCounter}</h5>
                        <button class="remove-queue-btn">✕</button>
                    </div>
                    <div class="form-group">
                        <label>消息模板</label>
                        <select class="newTask_templateSelect">
                            <option value="">选择模板...</option>
                            <!-- TODO: Populate with actual templates from tplManagementModal or a shared source -->
                            <option value="tpl1">订单创建模板 (示例)</option>
                            <option value="tpl2">支付成功模板 (示例)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Topic (从模板自动获取)</label>
                        <input type="text" class="newTask_topicInput" placeholder="请先选择消息模板" readonly style="background-color: #f8f9fa;">
                    </div>
                    <div class="form-group">
                        <label>每周期发送次数</label>
                        <input type="number" value="10">
                    </div>
                `;
                container.appendChild(newQueueItem);
                newQueueItem.querySelector('.remove-queue-btn').addEventListener('click', function() {
                    this.closest('.queue-item').remove();
                    // Re-number remaining queues if necessary
                    reNumberQueueItems(container, '队列');
                });
                // Add event listener for template select in the new queue item
                const templateSelect = newQueueItem.querySelector('.newTask_templateSelect');
                const topicInput = newQueueItem.querySelector('.newTask_topicInput');
                if (templateSelect && topicInput) {
                     templateSelect.addEventListener('change', function() {
                        // Mock: Set topic based on selected template
                        if (this.value === "tpl1") topicInput.value = "orders.new.from.newtask";
                        else if (this.value === "tpl2") topicInput.value = "payments.success.from.newtask";
                        else topicInput.value = "";
                    });
                }
            }
            
            function reNumberQueueItems(container, prefix) {
                const items = container.querySelectorAll(".queue-item");
                items.forEach((item, index) => {
                    const header = item.querySelector(".queue-item-header h5");
                    if (header) {
                        header.textContent = `${prefix} ${index + 1}`;
                    }
                });
            }

            // 4. Save New Task Logic
            if (newTaskForm.saveBtn && newTaskModal) {
                newTaskForm.saveBtn.addEventListener('click', function() {
                    const taskNameValue = newTaskForm.taskName ? newTaskForm.taskName.value.trim() : '未命名任务';
                    if (!taskNameValue) {
                        alert('请输入任务名称！');
                        newTaskForm.taskName.focus();
                        return;
                    }

                    // Simulate adding to sidebar task list
                    const taskList = document.getElementById('taskList');
                    if (taskList) {
                        const newListItem = document.createElement('li');
                        newListItem.innerHTML = `
                            <span class="task-name">${taskNameValue}</span>
                            <span class="task-actions">
                                <button title="运行">▶</button>
                                <button title="复制">❏</button>
                                <button title="删除">🗑</button>
                            </span>
                        `;
                        // Add click listener to the new task item (similar to existing ones)
                        newListItem.addEventListener('click', function(event) {
                            if (event.target.tagName === 'BUTTON') return; 
                            document.querySelectorAll('.task-list li').forEach(i => i.classList.remove('active'));
                            this.classList.add('active');
                            document.getElementById('taskNameInput').value = this.querySelector('.task-name').textContent;
                            // TODO: Load actual task data into the main config panel
                            // For now, just switch to the config tab
                            const configTabButton = document.querySelector('.tabs .tab-button[data-tab="config"]');
                            if(configTabButton) configTabButton.click();
                        });
                        
                        // Add delete button functionality for the new task item
                        const deleteButton = newListItem.querySelector('button[title="删除"]');
                        if(deleteButton) {
                            deleteButton.addEventListener('click', function(e) {
                                e.stopPropagation(); // Prevent li click event
                                if(confirm(`确定要删除任务 "${taskNameValue}"吗？`)){
                                    newListItem.remove();
                                    // If the deleted task was active, clear the main config panel or load another task
                                }
                            });
                        }
                        
                        taskList.appendChild(newListItem);
                        newListItem.click(); // Make the new task active
                    }
                    newTaskModal.style.display = 'none'; // Close modal
                    console.log("New task saved (simulated):", taskNameValue);
                });
            }

            // Ensure general modal close buttons work for newTaskModal
            // This code might be duplicated if already present, ensure it's robust or refactored
            document.querySelectorAll('.modal-close-btn[data-modal-id="newTaskModal"], .modal-footer .btn-secondary[data-modal-id="newTaskModal"]').forEach(button => {
                button.addEventListener('click', function() {
                    const modalToClose = document.getElementById('newTaskModal');
                    if (modalToClose) modalToClose.style.display = 'none';
                });
            });

        });
    </script>
</body>
</html>