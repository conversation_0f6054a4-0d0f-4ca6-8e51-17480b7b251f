#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import os
import signal
import sys
import logging
from datetime import datetime
from typing import Dict, Any, List, Callable, Optional
from kafka import KafkaProducer
from api_client import ApiClient, load_config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KafkaSender:
    """Kafka消息发送器，处理与Kafka的连接和消息发送逻辑"""
    
    def __init__(self, config=None):
        """
        初始化Kafka发送器
        
        :param config: 配置字典，如果为None则自动加载
        """
        self.config = config or load_config()
        self.kafka_server = self.config.get("kafka_server", "localhost:9092")
        self.device_limit = self.config.get("device_limit", 0)  # 设备数量限制，0表示不限制
        self.producer = None
        
        # SSL配置
        self.ssl_dir = "ssl"
        self.ssl_files = {
            "cafile": os.path.join(self.ssl_dir, "CARoot.pem"),
            "certfile": os.path.join(self.ssl_dir, "certificate.pem"),
            "keyfile": os.path.join(self.ssl_dir, "key.pem")
        }
    
    def connect_kafka(self) -> bool:
        """
        连接到Kafka服务器
        
        :return: 连接是否成功
        """
        try:
            logger.info(f"正在连接Kafka服务器: {self.kafka_server}")
            self.producer = KafkaProducer(
                bootstrap_servers=self.kafka_server,
                security_protocol="SSL",
                ssl_check_hostname=False,
                ssl_cafile=self.ssl_files["cafile"],
                ssl_certfile=self.ssl_files["certfile"],
                ssl_keyfile=self.ssl_files["keyfile"],
                value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                retries=5  # 添加重试次数
            )
            logger.info("Kafka连接成功!")
            return True
        except Exception as e:
            logger.error(f"连接Kafka服务器失败: {e}", exc_info=True)
            return False
    
    def check_ssl_files(self) -> bool:
        """
        检查SSL证书文件是否存在
        
        :return: 所有文件是否都存在
        """
        logger.info("检查证书文件...")
        all_files_exist = True
        for key, path in self.ssl_files.items():
            if os.path.exists(path):
                logger.info(f"- {key}: {path} [已找到]")
            else:
                logger.warning(f"- {key}: {path} [未找到]")
                all_files_exist = False
        
        if not all_files_exist:
            logger.warning("部分证书文件不存在，请确保证书已正确放置在ssl目录中")
            proceed = input("是否继续尝试连接Kafka? (y/n): ")
            if proceed.lower() != 'y':
                logger.info("操作取消")
                return False
        
        return True
    
    def close(self):
        """关闭Kafka连接"""
        if self.producer:
            self.producer.flush()
            self.producer.close()
            self.producer = None
    
    def get_device_list(self) -> Dict[str, str]:
        """
        获取设备列表，并根据设备限制进行过滤
        
        :return: 设备ID和IP的映射字典
        """
        # 创建API客户端
        client = ApiClient(
            host=self.config["server"],
            user=self.config["user"],
            password=self.config["password"],
            site_id=self.config.get("siteId")
        )
        
        # 获取设备ID和IP映射
        logger.info("正在获取设备列表...")
        id_ip_map = client.get_device_id_ip_map()
        
        if not id_ip_map:
            logger.warning("未找到设备，请检查配置和网络连接")
            return {}
        
        # 如果设置了设备数量限制，则只取指定数量的设备
        original_device_count = len(id_ip_map)
        if self.device_limit > 0 and self.device_limit < original_device_count:
            logger.info(f"找到 {original_device_count} 个设备，根据配置限制使用前 {self.device_limit} 个设备")
            # 转换为列表，取前N个，再转回字典
            items = list(id_ip_map.items())[:self.device_limit]
            id_ip_map = dict(items)
        else:
            logger.info(f"找到 {len(id_ip_map)} 个设备")
        
        return id_ip_map
    
    def send_messages(self,
                      kafka_topic: str,
                      device_messages: Dict[str, Dict[str, Any]],
                      continuous_mode: bool = True,
                      interval_seconds: int = 300,
                      message_prepare_func: Optional[Callable[[Dict[str, Any]], Dict[str, Any]]] = None) -> bool:
        """
        发送消息到Kafka
        
        :param kafka_topic: Kafka主题
        :param device_messages: 设备消息字典，键为设备IP，值为消息模板
        :param continuous_mode: 是否持续发送
        :param interval_seconds: 持续发送的间隔秒数
        :param message_prepare_func: 消息准备函数，在发送前会调用此函数处理消息
        :return: 是否成功
        """
        # 检查SSL文件
        if not self.check_ssl_files():
            return False
        
        # 定义发送单轮消息的函数
        def send_one_round():
            # 连接Kafka
            if not self.connect_kafka():
                return False
            
            # 当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            current_timestamp = int(time.time() * 1000)
            
            # 发送消息
            logger.info(f"开始向主题 '{kafka_topic}' 发送消息... ({current_time})")
            
            success_count = 0
            error_count = 0
            
            total_devices = len(device_messages)
            current = 0
            
            for device_ip, message_template in device_messages.items():
                current += 1
                try:
                    # 更新时间戳或进行其他处理
                    if message_prepare_func:
                        message = message_prepare_func(message_template.copy())
                    else:
                        # 默认只更新时间戳
                        message = message_template.copy()
                        if "timestamp" in message:
                            message["timestamp"] = current_timestamp
                        if "startTimestamp" in message:
                            message["startTimestamp"] = current_timestamp - 312
                    
                    # 发送消息
                    future = self.producer.send(kafka_topic, message)
                    # 等待发送确认
                    record_metadata = future.get(timeout=10)
                    
                    logger.info(f"[{current}/{total_devices}] 设备 {device_ip} 的消息已发送")
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"[{current}/{total_devices}] 发送设备 {device_ip} 的消息失败: {e}", exc_info=True)
                    error_count += 1
                
                # 每发送10个消息暂停一秒，避免发送过快
                if current % 10 == 0:
                    time.sleep(1)
            
            # 关闭连接
            self.close()
            
            logger.info(f"本轮消息发送完成:")
            logger.info(f"- 成功: {success_count}个设备")
            logger.info(f"- 失败: {error_count}个设备")
            logger.info(f"- 总计: {total_devices}个设备")
            
            return success_count > 0
        
        # 单次发送模式
        if not continuous_mode:
            return send_one_round()
        
        # 连续发送模式
        round_count = 0
        start_time = time.time()
        
        # 设置信号处理
        def signal_handler(sig, frame):
            elapsed_time = time.time() - start_time
            logger.info(f"程序被用户中断")
            logger.info(f"运行时间: {elapsed_time:.2f}秒")
            logger.info(f"完成轮数: {round_count}")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        logger.info(f"进入持续发送模式，每 {interval_seconds} 秒发送一轮 (按 Ctrl+C 停止)")
        
        try:
            while True:
                round_count += 1
                round_start = time.time()
                
                logger.info(f"======== 开始第 {round_count} 轮发送 ========")
                send_one_round()
                
                round_end = time.time()
                round_duration = round_end - round_start
                
                # 计算需要等待的时间
                if round_duration < interval_seconds:
                    wait_time = interval_seconds - round_duration
                    logger.info(f"本轮发送用时 {round_duration:.2f} 秒，等待 {wait_time:.2f} 秒后开始下一轮...")
                    
                    # 显示倒计时
                    wait_start = time.time()
                    while time.time() - wait_start < wait_time:
                        remaining = wait_time - (time.time() - wait_start)
                        if remaining <= 0:
                            break
                        
                        # 每15秒更新一次倒计时
                        if int(remaining) % 15 == 0:
                            logger.info(f"距离下一轮还有 {int(remaining)} 秒...")
                        
                        time.sleep(1)
                else:
                    logger.warning(f"本轮发送用时 {round_duration:.2f} 秒，超过了设定的间隔时间 {interval_seconds} 秒")
                    logger.info("立即开始下一轮发送...")
        
        except Exception as e:
            logger.error(f"程序异常: {e}", exc_info=True)
            return False


def parse_command_args():
    """解析命令行参数"""
    import argparse
    parser = argparse.ArgumentParser(description='发送消息到Kafka')
    parser.add_argument('--once', action='store_true', help='只发送一次，不进入循环模式')
    parser.add_argument('--interval', type=int, default=300, help='循环发送的间隔时间(秒)，默认300秒(5分钟)')
    parser.add_argument('--limit', type=int, default=0, help='设备数量限制，默认0表示不限制')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    return parser.parse_args()


# 测试函数
def test_kafka_sender():
    """测试Kafka发送器"""
    logger.info("测试Kafka发送器")
    config = load_config()
    sender = KafkaSender(config)
    
    # 测试SSL文件检查
    logger.info("测试SSL文件检查:")
    has_ssl = sender.check_ssl_files()
    logger.info(f"SSL文件检查结果: {'通过' if has_ssl else '失败'}")
    
    # 测试获取设备列表
    logger.info("测试获取设备列表:")
    devices = sender.get_device_list()
    logger.info(f"找到设备数量: {len(devices)}")
    if devices:
        logger.info("设备列表示例:")
        count = 0
        for device_id, device_ip in list(devices.items())[:3]:
            logger.info(f"- ID: {device_id}, IP: {device_ip}")
            count += 1
        if count < len(devices):
            logger.info(f"... 还有 {len(devices) - count} 个设备")


if __name__ == "__main__":
    args = parse_command_args()
    
    if args.test:
        test_kafka_sender()
        sys.exit(0)
