#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import random
import sys
import hashlib
from typing import Dict, Any, List
from common_utils import DeviceIdUtils
from api_client import load_config

class MessageGenerator:
    """消息生成器基类，定义通用接口"""
    
    def __init__(self, config=None):
        """
        初始化消息生成器
        
        :param config: 配置字典，如果为None则自动加载
        """
        self.config = config or load_config()
    
    def generate_messages(self, device_id_ip_map: Dict[str, str]) -> Dict[str, Dict[str, Any]]:
        """
        生成所有设备的消息
        
        :param device_id_ip_map: 设备ID和IP的映射字典
        :return: 设备消息字典，键为设备IP，值为消息模板
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_topic(self) -> str:
        """
        获取消息主题
        
        :return: Kafka主题名称
        """
        raise NotImplementedError("子类必须实现此方法")


class MacMessageGenerator(MessageGenerator):
    """MAC地址消息生成器"""
    
    def get_topic(self) -> str:
        """
        获取消息主题
        
        :return: Kafka主题名称
        """
        return self.config.get("kafka_topic", "dev-mac")
    
    def generate_mac_list(self, device_id: str, count: int) -> List[Dict[str, str]]:
        """
        生成指定数量的MAC地址列表
        
        :param device_id: 设备ID
        :param count: 需要生成的MAC地址数量
        :return: MAC地址信息列表
        """
        mac_list = []
        for i in range(count):
            mac_addr = DeviceIdUtils.generate_mac_for_device(device_id, i)
            
            # 随机选择vlan ID (使用固定值)
            vlan_choices = ["vlan1", "vlan1003"]
            vlan = random.choice(vlan_choices)
            
            # 端口固定为Te0/49
            port = "Te0/49"
            
            mac_info = {
                "vlan": vlan,
                "port": port,
                "mac": mac_addr
            }
            mac_list.append(mac_info)
        
        return mac_list
    
    def generate_messages(self, device_id_ip_map: Dict[str, str]) -> Dict[str, Dict[str, Any]]:
        """
        生成所有设备的MAC地址消息
        
        :param device_id_ip_map: 设备ID和IP的映射字典
        :return: 设备消息字典，键为设备IP，值为消息模板
        """
        # 获取MAC地址数量配置
        mac_count = self.config.get("mac_count", 200)
        
        # 生成所有设备的消息模板
        device_messages = {}
        for device_id, device_ip in device_id_ip_map.items():
            # 构建消息ID
            numeric_id = int(''.join([part.zfill(3) for part in device_ip.split('.')])[-4:])
            device_key = f"ip:{device_ip}"
            
            # 生成MAC地址列表
            mac_list = self.generate_mac_list(device_key, mac_count)
            
            # 获取当前时间戳（后续会在发送前更新）
            current_time = int(time.time() * 1000)
            
            # 构建消息
            message = {
                "devflag": device_key,
                "macList": mac_list,
                "interval": 900,
                "id": numeric_id,
                "deviceId": device_key,
                "startTimestamp": current_time - 312,
                "timestamp": current_time
            }
            
            device_messages[device_ip] = message
        
        return device_messages


class ArpMessageGenerator(MessageGenerator):
    """ARP条目消息生成器"""
    
    # 固定的接口列表
    INTERFACE_LIST = [
        "Mg0", "Or14", "Or2001", "Vl501", "Vl666", "Vl888", "Vl1002", 
        "Te1/0/1", "Te1/0/2", "Gi1/0/1", "Gi1/0/2", "Vl1", "Vl10", "Vl100"
    ]
    
    # 设备SN映射表缓存
    device_sn_cache = {}
    
    def get_topic(self) -> str:
        """
        获取消息主题
        
        :return: Kafka主题名称
        """
        return "dev-arp"  # ARP消息固定使用dev-arp主题
    
    def get_device_sn(self, device_id: str, device_ip: str) -> str:
        """
        获取设备SN，如果没有就生成一个假的
        
        :param device_id: 设备ID
        :param device_ip: 设备IP
        :return: 设备SN
        """
        if device_id in self.device_sn_cache:
            return self.device_sn_cache[device_id]
        
        # 为不同设备类型生成不同的SN前缀
        ip_last_octet = int(device_ip.split('.')[-1])
        
        if ip_last_octet % 3 == 0:
            prefix = "G1SB0UM"  # S6150设备
            sn = f"{prefix}{random.randint(100000, 999999)}"
        elif ip_last_octet % 3 == 1:
            prefix = "G1SC17K"  # S5760设备
            sn = f"{prefix}{random.randint(100000, 999999)}"
        else:
            prefix = "G1SC2LG"  # S5750设备
            sn = f"{prefix}{random.randint(100000, 999999)}"
            
        self.device_sn_cache[device_id] = sn
        return sn
    
    def generate_ip_for_device(self, device_id: str, index: int) -> str:
        """
        根据设备ID和索引生成唯一的IP地址
        
        :param device_id: 设备ID
        :param index: IP地址索引
        :return: 格式化的IP地址字符串
        """
        # 使用设备ID和索引的组合作为种子生成哈希
        seed = f"{device_id}:ip:{index}"
        hash_obj = hashlib.md5(seed.encode())
        hash_int = int(hash_obj.hexdigest(), 16)
        
        # 使用哈希生成IP地址的各个部分
        # 避免使用0和255作为最后一个字节
        octet1 = (hash_int & 0xFF) % 223 + 1  # 1-223
        octet2 = ((hash_int >> 8) & 0xFF) % 255 + 1  # 1-255
        octet3 = ((hash_int >> 16) & 0xFF) % 255 + 1  # 1-255
        octet4 = ((hash_int >> 24) & 0xFF) % 254 + 1  # 1-254
        
        # 特殊处理前10个IP，使用设备所在网段的IP
        if index < 10 and device_id.startswith("ip:"):
            device_ip = device_id[3:]  # 去掉"ip:"前缀
            ip_parts = device_ip.split('.')
            octet1 = int(ip_parts[0])
            octet2 = int(ip_parts[1])
            octet3 = int(ip_parts[2])
            octet4 = (int(ip_parts[3]) + index + 1) % 254 + 1  # 避免0和255
        
        return f"{octet1}.{octet2}.{octet3}.{octet4}"
    
    def generate_arp_list(self, device_id: str, device_ip: str, count: int) -> List[Dict[str, str]]:
        """
        生成指定数量的ARP列表
        
        :param device_id: 设备ID
        :param device_ip: 设备IP
        :param count: 需要生成的ARP条目数量
        :return: ARP信息列表
        """
        arp_list = []
        
        # 确保最后一个ARP条目是设备自己的IP
        self_mac = DeviceIdUtils.generate_mac_for_device(device_id, 9999)
        self_entry = {
            "intf": "Mg0",
            "ip": device_ip,
            "mac": self_mac
        }
        
        # 生成其他ARP条目
        for i in range(count - 1):
            # 选择接口
            intf = random.choice(self.INTERFACE_LIST)
            
            # 生成IP地址
            ip = self.generate_ip_for_device(device_id, i)
            
            # 生成唯一的MAC地址 - 使用设备ID和索引，不要创建复合ID
            # 添加一个偏移量以避免与设备MAC地址重复
            mac_index = i   # 添加一个大的偏移量
            mac = DeviceIdUtils.generate_mac_for_device(device_id, mac_index)
            
            arp_entry = {
                "intf": intf,
                "ip": ip,
                "mac": mac
            }
            arp_list.append(arp_entry)
        
        # 添加设备自己的条目
        arp_list.append(self_entry)
        
        return arp_list
    
    def generate_messages(self, device_id_ip_map: Dict[str, str]) -> Dict[str, Dict[str, Any]]:
        """
        生成所有设备的ARP条目消息
        
        :param device_id_ip_map: 设备ID和IP的映射字典
        :return: 设备消息字典，键为设备IP，值为消息模板
        """
        # 获取ARP条目数量配置
        arp_count = self.config.get("arp_count", 20)
        
        # 生成所有设备的消息模板
        device_messages = {}
        for device_id, device_ip in device_id_ip_map.items():
            # 构建设备ID
            device_key = f"ip:{device_ip}"
            numeric_id = device_ip.split('.')[-1]  # 使用IP的最后一部分作为数字ID
            
            # 获取或生成设备SN
            device_sn = self.get_device_sn(device_key, device_ip)
            
            # 生成ARP列表
            arp_list = self.generate_arp_list(device_key, device_ip, arp_count)
            
            # 获取当前时间戳（后续会在发送前更新）
            current_time = int(time.time() * 1000)
            
            # 构建消息
            message = {
                "devflag": device_key,
                "topic": "dev-arp",
                "interval": 900,
                "id": numeric_id,
                "deviceSn": device_sn,
                "deviceId": device_key,
                "arpList": arp_list,
                "timestamp": current_time
            }
            
            device_messages[device_ip] = message
        
        return device_messages


# 测试函数
def test_message_generators():
    """测试消息生成器"""
    print("\n测试消息生成器")
    config = load_config()
    
    # 创建MAC消息生成器
    mac_generator = MacMessageGenerator(config)
    
    # 创建ARP消息生成器
    arp_generator = ArpMessageGenerator(config)
    
    # 测试设备
    test_device_map = {
        "test_device_1": "*************",
        "test_device_2": "*************"
    }
    
    # 测试MAC消息生成
    print("\n测试MAC消息生成:")
    mac_messages = mac_generator.generate_messages(test_device_map)
    print(f"生成的MAC消息数量: {len(mac_messages)}")
    if mac_messages:
        first_ip = list(mac_messages.keys())[0]
        first_message = mac_messages[first_ip]
        print(f"示例MAC消息 (设备IP: {first_ip}):")
        print(f"- 设备ID: {first_message['deviceId']}")
        print(f"- MAC地址数量: {len(first_message['macList'])}")
        print(f"- 主题: {mac_generator.get_topic()}")
    
    # 测试ARP消息生成
    print("\n测试ARP消息生成:")
    arp_messages = arp_generator.generate_messages(test_device_map)
    print(f"生成的ARP消息数量: {len(arp_messages)}")
    if arp_messages:
        first_ip = list(arp_messages.keys())[0]
        first_message = arp_messages[first_ip]
        print(f"示例ARP消息 (设备IP: {first_ip}):")
        print(f"- 设备ID: {first_message['deviceId']}")
        print(f"- 设备SN: {first_message['deviceSn']}")
        print(f"- ARP条目数量: {len(first_message['arpList'])}")
        print(f"- 主题: {arp_generator.get_topic()}")


if __name__ == "__main__":
    # 设置随机种子，确保每次运行生成的MAC地址和IP地址相同
    random.seed(42)
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='测试消息生成器')
    parser.add_argument('--test', action='store_true', help='运行测试')
    args = parser.parse_args()
    
    if args.test:
        test_message_generators()
        sys.exit(0) 