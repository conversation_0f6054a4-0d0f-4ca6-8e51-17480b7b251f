from flask import (
    Blueprint, render_template, request, redirect, url_for, jsonify, current_app, abort
)
import time
import random
import json
from app import db  # 导入数据库模块
import math

bp = Blueprint('history', __name__, url_prefix='/history')

@bp.route('/')
def index():
    """历史记录列表页面"""
    return render_template('history/index.html')

@bp.route('/view/<int:history_id>')
def view(history_id):
    """查看历史记录详情页面"""
    return render_template('history/view.html', history_id=history_id)

@bp.route('/view_by_run_id/<path:run_id>')
def view_by_run_id(run_id):
    """通过运行ID查看历史记录详情页面"""
    from app.models.history import TestRunHistory
    
    # 从数据库中查找指定的运行ID
    record = TestRunHistory.query.filter_by(run_id=run_id).first()
    
    if not record:
        abort(404, description=f"找不到运行ID为 {run_id} 的历史记录")
    
    # 重定向到标准的历史记录查看页面
    return redirect(url_for('history.view', history_id=record.id))

@bp.route('/api/list')
def api_list():
    """获取历史记录列表API"""
    from app.models.history import TestRunHistory
    from app.models.task import TestTask

    # 获取查询参数
    task_id = request.args.get('task_id', type=int)

    # 构建查询
    query = TestRunHistory.query

    # 如果指定了任务ID，则只查询该任务的历史记录
    if task_id:
        query = query.filter_by(task_id=task_id)

    # 按开始时间降序排序
    history_records = query.order_by(TestRunHistory.start_time.desc()).all()
    
    # 转换为字典列表
    histories = []
    for record in history_records:
        # 获取任务名称
        task_name = None
        if record.task_id:
            task = TestTask.query.get(record.task_id)
            if task:
                task_name = task.name
        
        # 获取环境名称
        environment_name = None
        if record.environment_id:
            # 使用应用上下文中的环境管理器获取环境名称
            env_data = current_app.env_manager.get_environment(record.environment_id)
            if env_data and 'name' in env_data:
                environment_name = env_data['name']
        
        # 计算持续时间
        duration = None
        if record.start_time:
            if record.end_time:
                duration = int((record.end_time - record.start_time).total_seconds())
            elif record.status == 'running':
                # 如果任务还在运行，计算到现在的时间
                from datetime import datetime
                duration = int((datetime.utcnow() - record.start_time).total_seconds())
        
        # 解析运行数据获取详细信息
        run_data = {}
        message_count = 0
        error_count = 0
        latest_messages = {}
        main_topic = '未知'
        last_message_content = None

        if record.run_data:
            try:
                run_data = json.loads(record.run_data)
                message_count = run_data.get('message_count', 0)
                error_count = run_data.get('error_count', 0)
                latest_messages = run_data.get('latest_messages', {})

                # 获取主要topic和最后一条消息
                if latest_messages:
                    # 获取第一个队列的信息作为主要信息
                    first_queue_key = list(latest_messages.keys())[0] if latest_messages else None
                    if first_queue_key and first_queue_key in latest_messages:
                        queue_data = latest_messages[first_queue_key]
                        main_topic = queue_data.get('topic', '未知')
                        last_message_content = queue_data.get('message')

                        # 如果消息内容是字典，转换为JSON字符串
                        if isinstance(last_message_content, dict):
                            last_message_content = json.dumps(last_message_content, ensure_ascii=False, indent=2)
                        elif last_message_content:
                            last_message_content = str(last_message_content)
            except Exception as e:
                print(f"解析运行数据时出错: {e}")

        # 构建历史记录字典
        history_dict = {
            "id": record.id,
            "run_id": record.run_id,
            "task_id": record.task_id,
            "task_name": task_name,
            "environment_id": record.environment_id,
            "environment_name": environment_name,
            "start_time": record.start_time.isoformat() if record.start_time else None,
            "end_time": record.end_time.isoformat() if record.end_time else None,
            "status": record.status,
            "cycle_count": record.cycle_count or 0,
            "duration": duration,
            "message_count": message_count,
            "error_count": error_count,
            "main_topic": main_topic,
            "last_message_content": last_message_content
        }
        
        histories.append(history_dict)
    
    return jsonify(histories)

@bp.route('/api/get/<int:history_id>')
def api_get(history_id):
    """获取历史记录详情API"""
    from app.models.history import TestRunHistory
    from app.models.task import TestTask
    from datetime import datetime
    
    # 从数据库中获取指定的历史记录
    record = TestRunHistory.query.get(history_id)
    
    if not record:
        return jsonify({"success": False, "error": "历史记录不存在"}), 404
    
    # 获取任务名称
    task_name = None
    if record.task_id:
        task = TestTask.query.get(record.task_id)
        if task:
            task_name = task.name
    
    # 计算持续时间
    duration = None
    if record.start_time:
        if record.end_time:
            duration = int((record.end_time - record.start_time).total_seconds())
        elif record.status == 'running':
            # 如果任务还在运行，计算到现在的时间
            duration = int((datetime.utcnow() - record.start_time).total_seconds())
    
    # 解析任务快照
    task_snapshot = {}
    if record.task_snapshot:
        try:
            task_snapshot = json.loads(record.task_snapshot)
        except Exception as e:
            print(f"解析任务快照时出错: {e}")
    
    # 从任务快照中获取周期时长和总时长
    cycle_duration = task_snapshot.get('cycle_duration', 300)  # 默认5分钟
    total_duration = task_snapshot.get('total_duration', 0)  # 默认无限
    
    # 解析运行数据
    run_data = {}
    cycle_elapsed = 0  # 默认值
    if record.run_data:
        try:
            run_data = json.loads(record.run_data)
            # 获取周期已过时间
            cycle_elapsed = run_data.get('cycle_elapsed', 0)
        except Exception as e:
            print(f"解析运行数据时出错: {e}")
    
    # 构建队列信息
    queues = []
    if 'queues' in task_snapshot:
        for queue_index, queue in enumerate(task_snapshot.get('queues', [])):
            # 获取模板ID
            template_id = queue.get('template_id')
            
            # 获取模板信息以获取正确的主题名
            from app.models.template import MessageTemplate
            template = MessageTemplate.query.get(template_id)
            topic = template.topic if template and template.topic else queue.get('topic', 'default-topic')
            
            # 初始化队列消息计数和消息样例
            queue_message_count = 0
            message_sample = None
            
            # 从运行数据中获取最新消息和队列特定的消息计数
            if run_data and 'latest_messages' in run_data:
                queue_key = str(queue_index)
                if queue_key in run_data['latest_messages']:
                    message_data = run_data['latest_messages'][queue_key]
                    message_sample = message_data.get('message')
                    # 使用消息数据中的实际主题名
                    if 'topic' in message_data:
                        topic = message_data['topic']
            
            # 从运行数据中获取队列特定的消息计数
            queue_key_count = f"queue_{queue_index}_count"
            if run_data and queue_key_count in run_data:
                queue_message_count = run_data[queue_key_count]
            else:
                # 如果没有队列特定的计数，则平均分配总消息数
                queue_count = len(task_snapshot.get('queues', []))
                if queue_count > 0:
                    # 从run_data中获取总消息数
                    total_message_count = run_data.get('message_count', 0) if run_data else 0
                    queue_message_count = total_message_count // queue_count
            
            queue_info = {
                "topic": topic,
                "template_id": template_id,
                "message_count": queue_message_count,
                "average_rate": "-",  # 不计算速率
                "message_sample": message_sample
            }
            queues.append(queue_info)
    
    # 构建返回数据
    result = {
        "id": record.id,
        "run_id": record.run_id,
        "task_id": record.task_id,
        "task_name": task_name,
        "environment_id": record.environment_id,
        "start_time": record.start_time.isoformat() if record.start_time else None,
        "end_time": record.end_time.isoformat() if record.end_time else None,
        "status": record.status,
        "cycle_count": record.cycle_count or 0,
        "duration": duration,
        "queues": queues,
        "task_snapshot": task_snapshot,
        "cycle_duration": cycle_duration,  # 添加周期时长
        "total_duration": total_duration,   # 添加总时长
        "cycle_elapsed": cycle_elapsed  # 默认添加周期已过时间
    }
    
    # 添加运行数据中的信息
    if run_data:
        result["message_count"] = run_data.get("message_count", 0)
        result["error_count"] = run_data.get("error_count", 0)
        result["latest_messages"] = run_data.get("latest_messages", {})
        result["cycle_elapsed"] = run_data.get("cycle_elapsed", 0)
        print(f"[api_get] 从运行数据中更新周期已过时间: {result['cycle_elapsed']}秒")
    
    # 检查是否有正在运行的任务
    if record.status == 'running' and hasattr(current_app, 'running_tasks') and record.run_id in current_app.running_tasks:
        task_status = current_app.running_tasks[record.run_id]
        # 从内存中获取最新状态
        result["running"] = task_status.get('running', True)
        result["status"] = task_status.get('status', 'running')
        result["message_count"] = task_status.get('message_count', 0)
        result["cycle_count"] = task_status.get('cycle_count', 0)
        result["error_count"] = task_status.get('error_count', 0)
        result["latest_messages"] = task_status.get('latest_messages', {})
        result["cycle_elapsed"] = task_status.get('cycle_elapsed', 0)
        print(f"[api_get] 从内存中更新周期已过时间: {result['cycle_elapsed']}秒, 总周期数: {result['cycle_count']}, 周期时长: {cycle_duration}秒")
        
        # 将最新状态保存到数据库
        try:
            # 更新运行数据
            new_run_data = {
                'message_count': task_status.get('message_count', 0),
                'cycle_count': task_status.get('cycle_count', 0),
                'error_count': task_status.get('error_count', 0),
                'latest_messages': task_status.get('latest_messages', {}),
                'cycle_elapsed': task_status.get('cycle_elapsed', 0),
                'cycle_duration': task_status.get('cycle_duration', cycle_duration),
                'last_updated': time.time()
            }
            record.run_data = json.dumps(new_run_data)
            record.cycle_count = task_status.get('cycle_count', 0)  # 确保周期计数同步到数据库
            db.session.commit()
        except Exception as e:
            print(f"保存运行数据到数据库时出错: {e}")
    
    # 生成消息数量统计数据
    if record.start_time:
        start_ts = record.start_time.timestamp()
        end_ts = record.end_time.timestamp() if record.end_time else datetime.utcnow().timestamp()
        # 使用实际的消息数量
        total_message_count = 0
        if run_data:
            total_message_count = run_data.get('message_count', 0)
        result["message_data"] = generate_rate_data(start_ts, end_ts, 60, total_message_count)  # 每分钟一个数据点
    
    return jsonify(result)

@bp.route('/api/get_by_run_id/<path:run_id>')
def api_get_by_run_id(run_id):
    """通过运行ID获取历史记录详情API"""
    from app.models.history import TestRunHistory
    
    # 从数据库中查找指定的运行ID
    record = TestRunHistory.query.filter_by(run_id=run_id).first()
    
    if not record:
        return jsonify({"success": False, "error": f"找不到运行ID为 {run_id} 的历史记录"}), 404
    
    # 重用现有的 api_get 函数获取历史记录详情
    return api_get(record.id)

@bp.route('/api/delete/<int:history_id>', methods=['DELETE'])
def api_delete(history_id):
    """删除历史记录API"""
    from app.models.history import TestRunHistory, db
    from flask import current_app
    
    # 查询指定的历史记录
    history = TestRunHistory.query.get(history_id)
    
    if not history:
        return jsonify({"success": False, "error": "历史记录不存在"}), 404
    
    try:
        # 如果任务还在运行，停止它
        if history.status == 'running' and hasattr(current_app, 'running_tasks') and history.run_id in current_app.running_tasks:
            current_app.running_tasks[history.run_id]['running'] = False
            current_app.running_tasks[history.run_id]['status'] = 'stopped'
            print(f"停止任务: {history.run_id}")
        
        # 删除历史记录
        db.session.delete(history)
        db.session.commit()
        
        return jsonify({"success": True})
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/delete_all', methods=['DELETE'])
def api_delete_all():
    """删除所有历史记录API"""
    from app.models.history import TestRunHistory, db
    from flask import current_app
    
    try:
        # 获取所有历史记录
        histories = TestRunHistory.query.all()
        
        # 停止所有正在运行的任务
        if hasattr(current_app, 'running_tasks'):
            for history in histories:
                if history.status == 'running' and history.run_id in current_app.running_tasks:
                    current_app.running_tasks[history.run_id]['running'] = False
                    current_app.running_tasks[history.run_id]['status'] = 'stopped'
                    print(f"停止任务: {history.run_id}")
        
        # 删除所有历史记录
        for history in histories:
            db.session.delete(history)
        
        db.session.commit()
        
        return jsonify({"success": True, "count": len(histories)})
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/rerun/<int:history_id>', methods=['POST'])
def api_rerun(history_id):
    """重新运行历史记录API"""
    from app.models.history import TestRunHistory, db
    from flask import current_app
    import json
    import time
    
    # 查询指定的历史记录
    history = TestRunHistory.query.get(history_id)
    
    if not history:
        return jsonify({"success": False, "error": "历史记录不存在"}), 404
    
    # 获取任务ID
    task_id = history.task_id
    if not task_id:
        return jsonify({"success": False, "error": "历史记录没有关联任务ID"}), 400
    
    try:
        # 首先停止所有可能正在运行的相关任务
        stopped_tasks = []
        
        # 记录停止开始时间
        stop_start_time = time.time()
        
        # 检查是否有正在运行的任务
        if hasattr(current_app, 'running_tasks'):
            # 首先停止与此历史记录直接相关的任务
            for run_id, task_info in list(current_app.running_tasks.items()):
                task_history_id = task_info.get('history_id')
                task_task_id = task_info.get('task_id')
                
                # 检查是否是相关任务
                is_related = False
                
                # 1. 检查是否与当前历史记录相关
                if task_history_id == history_id:
                    is_related = True
                    print(f"找到直接相关的运行任务: {run_id}, 历史ID匹配: {history_id}")
                
                # 2. 检查是否是同一个任务的其他实例
                elif task_task_id == task_id:
                    is_related = True
                    print(f"找到相关的运行任务: {run_id}, 任务ID匹配: {task_id}")
                
                # 3. 检查是否有原始历史ID关联
                elif task_info.get('original_history_id') == history_id:
                    is_related = True
                    print(f"找到相关的运行任务: {run_id}, 原始历史ID匹配: {history_id}")
                
                if is_related:
                    print(f"停止任务: {run_id}, 当前状态: {task_info.get('status')}")
                    
                    # 首先通过内存标记停止任务
                    current_app.running_tasks[run_id]['running'] = False
                    current_app.running_tasks[run_id]['status'] = 'stopped'
                    
                    # 然后通过API调用停止任务
                    with current_app.test_client() as client:
                        print(f"通过API停止任务: {run_id}")
                        stop_response = client.post(f'/task/api/stop/{run_id}')
                        if stop_response.status_code == 200:
                            print(f"成功停止任务: {run_id}")
                            stopped_tasks.append(run_id)
                        else:
                            print(f"停止任务API调用失败: {run_id}, 状态码: {stop_response.status_code}")
                            # 即使API调用失败，也记录为已停止，因为我们已经在内存中标记了停止
                            stopped_tasks.append(run_id)
        
        # 如果停止了任务，等待足够的时间以确保任务完全停止
        if stopped_tasks:
            stop_duration = time.time() - stop_start_time
            print(f"已停止 {len(stopped_tasks)} 个相关任务，耗时: {stop_duration:.2f}秒")
            
            # 如果停止操作太快，等待一点时间确保完全停止
            if stop_duration < 0.5:  # 如果停止操作不到0.5秒
                wait_time = 1.0  # 等待1秒
                print(f"等待 {wait_time} 秒确保任务完全停止...")
                time.sleep(wait_time)
        else:
            print("没有找到正在运行的相关任务")
        
        # 使用Flask的test_client发送内部HTTP请求到api_run端点开始新任务
        with current_app.test_client() as client:
            print(f"开始运行新任务，任务ID: {task_id}")
            response = client.post(f'/task/api/run/{task_id}')
            response_data = json.loads(response.data.decode('utf-8'))
            
            if response.status_code != 200 or not response_data.get('success'):
                error_msg = response_data.get('error', '未知错误')
                return jsonify({"success": False, "error": f"运行任务失败: {error_msg}"}), response.status_code
            
            # 在新任务状态中记录原始历史ID，便于未来引用
            new_run_id = response_data.get('run_id')
            new_history_id = response_data.get('history_id')
            
            if hasattr(current_app, 'running_tasks') and new_run_id in current_app.running_tasks:
                current_app.running_tasks[new_run_id]['original_history_id'] = history_id
                print(f"已记录原始历史ID: {history_id} 到新任务: {new_run_id}")
            
            print(f"新任务已开始运行，运行ID: {new_run_id}, 历史ID: {new_history_id}")
            
            # 返回响应数据
            return jsonify({
                "success": True,
                "run_id": new_run_id,
                "history_id": new_history_id,
                "task_id": task_id,
                "message": "任务已重新运行"
            })
    except Exception as e:
        print(f"重新运行任务时出错: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/resume/<int:history_id>', methods=['POST'])
def api_resume(history_id):
    """恢复已停止的任务API"""
    import uuid
    from app.models.history import TestRunHistory, db
    from datetime import datetime
    import threading
    
    # 查询指定的历史记录
    history = TestRunHistory.query.get(history_id)
    
    if not history:
        return jsonify({"success": False, "error": "历史记录不存在"}), 404
    
    # 检查任务状态是否为已停止
    if history.status != 'stopped':
        return jsonify({"success": False, "error": f"只能恢复已停止的任务，当前状态为: {history.status}"}), 400
    
    try:
        # 创建新的运行ID
        run_id = f"run_{uuid.uuid4().hex}"
        
        # 创建新的历史记录，继承原历史记录的配置
        new_history = TestRunHistory(
            run_id=run_id,
            task_id=history.task_id,
            environment_id=history.environment_id,
            status='running',
            # 复制环境变量快照和任务配置快照
            environment_snapshot=history.environment_snapshot,
            task_snapshot=history.task_snapshot
        )
        
        # 解析原运行数据，获取消息计数和周期计数
        run_data = {}
        message_count = 0
        cycle_count = 0
        error_count = 0
        latest_messages = {}
        
        if history.run_data:
            try:
                run_data = json.loads(history.run_data)
                message_count = run_data.get('message_count', 0)
                cycle_count = run_data.get('cycle_count', 0)
                error_count = run_data.get('error_count', 0)
                latest_messages = run_data.get('latest_messages', {})
            except Exception as e:
                print(f"解析运行数据时出错: {e}")
        
        db.session.add(new_history)
        db.session.commit()
        
        # 获取当前应用实例的引用
        app = current_app._get_current_object()
        
        # 全局运行状态字典
        if not hasattr(app, 'running_tasks'):
            app.running_tasks = {}
        
        # 初始化任务状态，继承原任务的计数
        app.running_tasks[run_id] = {
            'status': 'running',
            'message_count': message_count,
            'cycle_count': cycle_count,
            'error_count': error_count,
            'running': True,
            'latest_messages': latest_messages,
            'task_id': history.task_id,
            'history_id': new_history.id
        }
        
        # 导入run_test函数
        from app.views.task import run_test
        
        # 启动后台线程
        test_thread = threading.Thread(
            target=run_test,
            args=(app, run_id, new_history.task_snapshot, new_history.environment_snapshot),
            daemon=True
        )
        test_thread.start()
        
        # 返回运行ID和历史记录ID
        return jsonify({
            "success": True,
            "run_id": run_id,
            "history_id": new_history.id,
            "message": "任务已成功恢复"
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

def generate_rate_data(start_time, end_time, interval, message_count=None):
    """
    生成每分钟消息数量统计数据用于图表，只包含有消息的时间点
    
    :param start_time: 开始时间戳
    :param end_time: 结束时间戳
    :param interval: 数据点间隔（秒）
    :param message_count: 总消息数，如果提供则基于此生成真实分布
    :return: 消息数量数据列表
    """
    message_count_data = []
    
    # 如果没有消息，直接返回空列表
    if not message_count or message_count <= 0:
        return message_count_data
    
    current_time = start_time
    # 如果没有提供总消息数，使用默认值
    total_message_count = message_count or 1000
    
    # 计算时间点数量
    time_points = max(1, int((end_time - start_time) / interval))
    
    # 如果只有一个时间点，直接返回总消息数
    if time_points == 1:
        message_count_data.append({
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime(current_time)),
            "count": total_message_count
        })
        return message_count_data
    
    # 计算每个时间点的消息数量（正态分布）
    remaining_count = total_message_count
    
    # 根据总消息数决定在多少个时间点上分布消息
    # 这里我们用总消息数的平方根作为非空时间点的数量
    # 确保至少有1个点，最多不超过总时间点数
    non_empty_points = min(time_points, max(1, int(math.sqrt(total_message_count))))
    
    # 选择均匀分布的时间点
    if non_empty_points < time_points:
        step = time_points / non_empty_points
        selected_indices = [int(i * step) for i in range(non_empty_points)]
    else:
        selected_indices = list(range(time_points))
    
    # 为每个选中的时间点分配消息
    point_index = 0
    while current_time < end_time:
        if point_index in selected_indices:
            # 计算此时间点应分配的消息数
            points_remaining = len([i for i in selected_indices if i >= point_index])
            avg_count_per_point = remaining_count / points_remaining
            
            # 添加一些随机波动（但确保总数正确）
            if points_remaining > 1:
                # 最后一个点前的随机波动
                variation = random.uniform(0.7, 1.3)
                point_count = int(avg_count_per_point * variation)
                # 确保不会超过剩余总数
                point_count = min(point_count, remaining_count)
            else:
                # 最后一个点使用所有剩余消息数
                point_count = remaining_count
            
            # 只添加有消息的时间点
            if point_count > 0:
                message_count_data.append({
                    "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime(current_time)),
                    "count": point_count
                })
                
                remaining_count -= point_count
        
        current_time += interval
        point_index += 1
    
    return message_count_data


def get_running_task_stats(task_id):
    """获取正在运行任务的统计数据"""
    from flask import current_app

    app = current_app._get_current_object()

    # 查找该任务当前运行的实例
    if hasattr(app, 'running_tasks'):
        for run_id, task_info in app.running_tasks.items():
            if task_info.get('running', False) and task_info.get('task_id') == task_id:
                # 构建统计数据
                stats = {
                    "total_messages": task_info.get('message_count', 0),
                    "error_count": task_info.get('error_count', 0),
                    "cycle_count": task_info.get('cycle_count', 0),
                    "queue_distribution": {},
                    "messages_timeline": [],
                    "topics": {},
                    "run_id": run_id,
                    "status": task_info.get('status', 'running')
                }

                # 获取最新消息信息
                latest_messages = task_info.get('latest_messages', {})

                # 统计队列分布和主题分布
                for queue_key, queue_data in latest_messages.items():
                    if isinstance(queue_data, dict):
                        topic = queue_data.get('topic', '未知')

                        # 获取队列特定的消息计数
                        queue_count_key = f"queue_{queue_key}_count"
                        message_count = task_info.get(queue_count_key, 1)

                        # 队列分布
                        queue_name = f"队列{int(queue_key) + 1}" if queue_key.isdigit() else f"队列{queue_key}"
                        stats["queue_distribution"][queue_name] = message_count

                        # 主题分布
                        if topic not in stats["topics"]:
                            stats["topics"][topic] = 0
                        stats["topics"][topic] += message_count

                # 生成时间线数据（基于周期数的简化版本）
                cycle_count = stats["cycle_count"]
                if cycle_count > 0:
                    messages_per_cycle = max(1, stats["total_messages"] // cycle_count) if cycle_count > 0 else stats["total_messages"]
                    for i in range(min(cycle_count, 10)):  # 最多显示10个数据点
                        stats["messages_timeline"].append({
                            "cycle": i + 1,
                            "messages": messages_per_cycle * (i + 1),
                            "cumulative": True
                        })

                # 如果没有队列分布数据，使用默认数据
                if not stats["queue_distribution"] and stats["total_messages"] > 0:
                    stats["queue_distribution"]["默认队列"] = stats["total_messages"]

                # 如果没有主题数据，使用默认数据
                if not stats["topics"] and stats["total_messages"] > 0:
                    stats["topics"]["默认主题"] = stats["total_messages"]

                return stats

    return None


@bp.route('/api/clear_task/<int:task_id>', methods=['DELETE'])
def api_clear_task_history(task_id):
    """清空指定任务的所有历史记录API"""
    from app.models.history import TestRunHistory, db
    from flask import current_app

    try:
        # 获取指定任务的所有历史记录
        histories = TestRunHistory.query.filter_by(task_id=task_id).all()

        if not histories:
            return jsonify({
                "success": True,
                "deleted_count": 0,
                "message": "该任务没有历史记录"
            })

        # 停止该任务正在运行的实例
        stopped_count = 0
        if hasattr(current_app, 'running_tasks'):
            for run_id in list(current_app.running_tasks.keys()):
                task_info = current_app.running_tasks[run_id]
                if task_info.get('task_id') == task_id and task_info.get('running', False):
                    current_app.running_tasks[run_id]['running'] = False
                    current_app.running_tasks[run_id]['status'] = 'stopped'
                    stopped_count += 1
                    print(f"停止任务实例: {run_id}")

        # 删除指定任务的所有历史记录
        deleted_count = TestRunHistory.query.filter_by(task_id=task_id).delete()

        # 提交更改
        db.session.commit()

        message = f"已清空任务的 {deleted_count} 条历史记录"
        if stopped_count > 0:
            message += f"，并停止了 {stopped_count} 个正在运行的实例"

        return jsonify({
            "success": True,
            "deleted_count": deleted_count,
            "stopped_count": stopped_count,
            "message": message
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500