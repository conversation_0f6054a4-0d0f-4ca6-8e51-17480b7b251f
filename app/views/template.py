from flask import (
    Blueprint, render_template, request, redirect, url_for, jsonify, current_app, abort
)
import time
import json

bp = Blueprint('template', __name__, url_prefix='/template')

@bp.route('/')
def index():
    """消息模板列表页面"""
    return render_template('template/index.html')

@bp.route('/create')
def create():
    """创建消息模板页面"""
    # 使用 edit.html 模板，不传递 template_id 或传递 0 表示创建
    return render_template('template/edit.html', template_id=0)

@bp.route('/edit/<int:template_id>')
def edit(template_id):
    """编辑消息模板页面"""
    return render_template('template/edit.html', template_id=template_id)

@bp.route('/api/list')
def api_list():
    """获取消息模板列表API"""
    from app.models.template import MessageTemplate
    from app import db
    
    # 查询所有消息模板
    templates = MessageTemplate.query.all()
    
    # 转换为字典列表
    template_list = []
    for template in templates:
        template_dict = template.to_dict()
        # 模板现在包含topic字段，不需要额外设置
        template_list.append(template_dict)
    
    # 如果没有模板数据，创建默认模板
    if not template_list:
        # 创建默认的ARP条目消息模板
        arp_template = MessageTemplate(
            name="ARP条目消息",
            description="生成设备的ARP条目消息，包含IP地址、MAC地址和接口信息",
            python_code="""def generate_message(env, device_id, device_ip, index, timestamp):
    \"\"\"
    生成ARP条目消息
    
    :param env: 环境变量字典
    :param device_id: 设备ID
    :param device_ip: 设备IP
    :param index: 消息索引
    :param timestamp: 当前时间戳(毫秒)
    :return: 消息字典
    \"\"\"
    import random
    import hashlib
    
    # 生成设备SN (基于设备IP)
    ip_last_octet = int(device_ip.split('.')[-1])
    if ip_last_octet % 3 == 0:
        prefix = "G1SB0UM"  # S6150设备
    elif ip_last_octet % 3 == 1:
        prefix = "G1SC17K"  # S5760设备
    else:
        prefix = "G1SC2LG"  # S5750设备
    
    device_sn = f"{prefix}{random.randint(100000, 999999)}"
    
    # 接口列表
    interfaces = [
        "Mg0", "Or14", "Or2001", "Vl501", "Vl666", "Vl888", "Vl1002", 
        "Te1/0/1", "Te1/0/2", "Gi1/0/1", "Gi1/0/2", "Vl1", "Vl10", "Vl100"
    ]
    
    # 生成ARP条目数量
    arp_count = int(env.get('ARP_COUNT', 50))
    
    # 生成ARP条目列表
    arp_list = []
    for i in range(arp_count):
        # 根据设备ID和索引生成唯一的IP和MAC
        seed = f"{device_id}:ip:{i}"
        hash_obj = hashlib.md5(seed.encode())
        hash_int = int(hash_obj.hexdigest(), 16)
        
        # 生成IP地址
        octet1 = (hash_int & 0xFF) % 223 + 1  # 1-223
        octet2 = ((hash_int >> 8) & 0xFF) % 255 + 1  # 1-255
        octet3 = ((hash_int >> 16) & 0xFF) % 255 + 1  # 1-255
        octet4 = ((hash_int >> 24) & 0xFF) % 254 + 1  # 1-254
        
        # 使用设备所在网段的IP (前10个)
        if i < 10:
            ip_parts = device_ip.split('.')
            octet1 = int(ip_parts[0])
            octet2 = int(ip_parts[1])
            octet3 = int(ip_parts[2])
            octet4 = (int(ip_parts[3]) + i + 1) % 254 + 1
        
        ip_addr = f"{octet1}.{octet2}.{octet3}.{octet4}"
        
        # 生成MAC地址 (基于IP)
        mac_addr = f"{octet1:02x}:{octet2:02x}:{octet3:02x}:{hash_int & 0xFF:02x}:{(hash_int >> 8) & 0xFF:02x}:{(hash_int >> 16) & 0xFF:02x}"
        
        # 随机选择接口
        interface = random.choice(interfaces)
        
        # 创建ARP条目
        arp_entry = {
            "ip": ip_addr,
            "mac": mac_addr,
            "vlan": f"vlan{random.randint(1, 100)}",
            "type": "dynamic",
            "interface": interface
        }
        arp_list.append(arp_entry)
    
    # 构建消息
    numeric_id = int(''.join([part.zfill(3) for part in device_ip.split('.')])[-4:])
    message = {
        "devflag": f"ip:{device_ip}",
        "arpList": arp_list,
        "interval": 300,
        "id": numeric_id,
        "deviceId": f"ip:{device_ip}",
        "startTimestamp": timestamp - 312,
        "timestamp": timestamp
    }
    
    return message""",
            sample_output="""{\n  \"devflag\": \"ip:*************\",\n  \"arpList\": [\n    {\n      \"ip\": \"*************\",\n      \"mac\": \"00:11:22:33:44:55\",\n      \"vlan\": \"vlan1\",\n      \"type\": \"dynamic\",\n      \"interface\": \"Gi1/0/1\"\n    }\n  ],\n  \"interval\": 300,\n  \"id\": 1100,\n  \"deviceId\": \"ip:*************\",\n  \"startTimestamp\": 1621234567688,\n  \"timestamp\": 1621234568000\n}"""
        )
        
        mac_template = MessageTemplate(
            name="MAC地址表消息",
            description="生成设备的MAC地址表消息，包含VLAN、端口和MAC地址信息",
            python_code="""def generate_message(env, device_id, device_ip, index, timestamp):
    \"\"\"
    生成MAC地址表消息
    
    :param env: 环境变量字典
    :param device_id: 设备ID
    :param device_ip: 设备IP
    :param index: 消息索引
    :param timestamp: 当前时间戳(毫秒)
    :return: 消息字典
    \"\"\"
    import random
    import hashlib
    
    # 生成MAC地址数量
    mac_count = int(env.get('MAC_COUNT', 50))
    
    # VLAN选项
    vlan_choices = ["vlan1", "vlan10", "vlan20", "vlan100", "vlan1003"]
    
    # 端口选项
    port_choices = ["Te0/49", "Gi1/0/1", "Gi1/0/2", "Gi1/0/3", "Gi1/0/4", "Gi2/0/1"]
    
    # 生成MAC地址列表
    mac_list = []
    for i in range(mac_count):
        # 根据设备ID和索引生成唯一的MAC
        seed = f"{device_id}:mac:{i}"
        hash_obj = hashlib.md5(seed.encode())
        hash_int = int(hash_obj.hexdigest(), 16)
        
        # 生成MAC地址
        mac_addr = f"{(hash_int & 0xFF):02x}:{((hash_int >> 8) & 0xFF):02x}:{((hash_int >> 16) & 0xFF):02x}:{((hash_int >> 24) & 0xFF):02x}:{((hash_int >> 32) & 0xFF):02x}:{((hash_int >> 40) & 0xFF):02x}"
        
        # 随机选择vlan和端口
        vlan = random.choice(vlan_choices)
        port = random.choice(port_choices)
        
        mac_info = {
            "vlan": vlan,
            "port": port,
            "mac": mac_addr
        }
        mac_list.append(mac_info)
    
    # 构建消息
    numeric_id = int(''.join([part.zfill(3) for part in device_ip.split('.')])[-4:])
    message = {
        "devflag": f"ip:{device_ip}",
        "macList": mac_list,
        "interval": 900,
        "id": numeric_id,
        "deviceId": f"ip:{device_ip}",
        "startTimestamp": timestamp - 312,
        "timestamp": timestamp
    }
    
    return message""",
            sample_output="""{\n  \"devflag\": \"ip:*************\",\n  \"macList\": [\n    {\n      \"vlan\": \"vlan1\",\n      \"port\": \"Gi1/0/1\",\n      \"mac\": \"00:11:22:33:44:55\"\n    }\n  ],\n  \"interval\": 900,\n  \"id\": 1100,\n  \"deviceId\": \"ip:*************\",\n  \"startTimestamp\": 1621234567688,\n  \"timestamp\": 1621234568000\n}"""
        )
        
        # 添加并保存默认模板
        db.session.add(arp_template)
        db.session.add(mac_template)
        db.session.commit()
        
        # 重新查询模板列表
        templates = MessageTemplate.query.all()
        template_list = []
        for template in templates:
            template_dict = template.to_dict()
            template_dict['topic'] = ""  # 模板本身不包含topic
            template_list.append(template_dict)
    
    return jsonify(template_list)

@bp.route('/api/get/<int:template_id>')
def api_get(template_id):
    """获取消息模板详情API"""
    from app.models.template import MessageTemplate
    from app import db
    
    # 从数据库中获取指定ID的模板
    template = MessageTemplate.query.get(template_id)
    
    if template:
        # 转换为字典并返回
        template_dict = template.to_dict()
        # 模板现在包含topic字段，不需要额外设置
        return jsonify(template_dict)
    else:
        # 如果模板不存在，返回错误
        return jsonify({"success": False, "error": "模板不存在"}), 404

@bp.route('/api/save', methods=['POST'])
def api_save():
    """保存消息模板API"""
    from app.models.template import MessageTemplate
    from app import db
    
    template_data = request.get_json()
    print(f"\n\nReceived template data: {template_data}\n\n")
    if not template_data:
        return jsonify({"success": False, "error": "无效的请求数据"}), 400
    
    try:
        # 检查必要字段
        if not template_data.get('name') or not template_data.get('code'):
            return jsonify({"success": False, "error": "缺少必要字段"}), 400
        
        # 检查是否有同名模板
        existing_template = MessageTemplate.query.filter_by(name=template_data.get('name')).first()
        
        # 如果有ID，则更新现有模板
        if template_data.get('id'):
            template = MessageTemplate.query.get(template_data['id'])
            if not template:
                return jsonify({"success": False, "error": "模板不存在"}), 404
            
            # 如果改了名称，需要检查新名称是否已存在
            if template_data.get('name') != template.name and existing_template:
                return jsonify({"success": False, "error": "已存在同名模板"}), 400
                
            # 更新基本信息
            template.name = template_data.get('name')
            template.description = template_data.get('description', '')
            template.topic = template_data.get('topic', 'default-topic')  # 保存Kafka主题
            template.python_code = template_data.get('code')  # 前端发送的是'code'字段
            template.sample_output = template_data.get('sample_output', '')
            template.env_vars = template_data.get('env_vars', '')  # 保存环境变量
        else:
            # 创建新模板，需要检查名称是否已存在
            if existing_template:
                # 如果已存在同名模板，则更新该模板
                existing_template.description = template_data.get('description', '')
                existing_template.topic = template_data.get('topic', 'default-topic')  # 保存Kafka主题
                existing_template.python_code = template_data.get('code')  # 前端发送的是'code'字段
                existing_template.sample_output = template_data.get('sample_output', '')
                existing_template.env_vars = template_data.get('env_vars', '')  # 保存环境变量
                template = existing_template
            else:
                # 创建新模板
                template = MessageTemplate(
                    name=template_data.get('name'),
                    description=template_data.get('description', ''),
                    topic=template_data.get('topic', 'default-topic'),  # 保存Kafka主题
                    python_code=template_data.get('code'),  # 前端发送的是'code'字段
                    sample_output=template_data.get('sample_output', ''),
                    env_vars=template_data.get('env_vars', '')  # 保存环境变量
                )
                db.session.add(template)
        
        # 提交更改
        db.session.commit()
        
        return jsonify({"success": True, "id": template.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/delete/<int:template_id>', methods=['DELETE'])
def api_delete(template_id):
    """删除消息模板API"""
    from app.models.template import MessageTemplate
    from app import db
    
    # 查询指定ID的模板
    template = MessageTemplate.query.get(template_id)
    
    if not template:
        return jsonify({"success": False, "error": "模板不存在"}), 404
    
    try:
        # 删除模板
        db.session.delete(template)
        db.session.commit()
        return jsonify({"success": True})
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/example/<string:template_type>')
def api_example(template_type):
    """加载示例模板的API接口"""
    # 根据模板类型返回不同的示例代码
    if template_type == 'arp':
        # ARP条目消息模板
        python_code = """def generate_message(env):
    \"\"\"
    生成ARP条目消息
    
    :param env: 环境变量字典，包含所有必要的参数
    :return: 消息字典
    \"\"\"
    import random
    import hashlib
    
    # 从环境变量获取设备信息
    device_ip = env.get("DEVICE_IP", "*************")
    device_id = env.get("DEVICE_ID", f"ip:{device_ip}")
    timestamp = int(env.get("TIMESTAMP", str(int(time.time() * 1000))))
    
    # 生成设备SN (基于设备IP)
    ip_last_octet = int(device_ip.split('.')[-1])
    if ip_last_octet % 3 == 0:
        prefix = \"G1SB0UM\"  # S6150设备
    elif ip_last_octet % 3 == 1:
        prefix = \"G1SC17K\"  # S5760设备
    else:
        prefix = \"G1SC2LG\"  # S5750设备
    
    device_sn = f\"{prefix}{random.randint(100000, 999999)}\"
    
    # 接口列表
    interfaces = [
        \"Mg0\", \"Or14\", \"Or2001\", \"Vl501\", \"Vl666\", \"Vl888\", \"Vl1002\", 
        \"Te1/0/1\", \"Te1/0/2\", \"Gi1/0/1\", \"Gi1/0/2\", \"Vl1\", \"Vl10\", \"Vl100\"
    ]
    
    # 生成ARP条目数量
    arp_count = int(env.get('ARP_COUNT', 50))
    
    # 生成ARP条目列表
    arp_list = []
    for i in range(arp_count):
        # 根据设备ID和索引生成唯一的IP和MAC
        seed = f\"{device_id}:ip:{i}\"
        hash_obj = hashlib.md5(seed.encode())
        hash_int = int(hash_obj.hexdigest(), 16)
        
        # 生成IP地址
        octet1 = (hash_int & 0xFF) % 223 + 1  # 1-223
        octet2 = ((hash_int >> 8) & 0xFF) % 255 + 1  # 1-255
        octet3 = ((hash_int >> 16) & 0xFF) % 255 + 1  # 1-255
        octet4 = ((hash_int >> 24) & 0xFF) % 254 + 1  # 1-254
        
        # 使用设备所在网段的IP (前10个)
        if i < 10:
            ip_parts = device_ip.split('.')
            octet1 = int(ip_parts[0])
            octet2 = int(ip_parts[1])
            octet3 = int(ip_parts[2])
            octet4 = (int(ip_parts[3]) + i + 1) % 254 + 1
        
        ip_addr = f\"{octet1}.{octet2}.{octet3}.{octet4}\"
        
        # 生成MAC地址 (基于IP)
        mac_addr = f\"{octet1:02x}:{octet2:02x}:{octet3:02x}:{hash_int & 0xFF:02x}:{(hash_int >> 8) & 0xFF:02x}:{(hash_int >> 16) & 0xFF:02x}\"
        
        # 随机选择接口
        interface = random.choice(interfaces)
        
        # 创建ARP条目
        arp_entry = {
            \"ip\": ip_addr,
            \"mac\": mac_addr,
            \"vlan\": f\"vlan{random.randint(1, 100)}\",
            \"type\": \"dynamic\",
            \"interface\": interface
        }
        arp_list.append(arp_entry)
    
    # 构建消息
    numeric_id = int(''.join([part.zfill(3) for part in device_ip.split('.')])[-4:])
    message = {
        \"devflag\": f\"ip:{device_ip}\",
        \"arpList\": arp_list,
        \"interval\": 300,
        \"id\": numeric_id,
        \"deviceId\": f\"ip:{device_ip}\",
        \"startTimestamp\": timestamp - 312,
        \"timestamp\": timestamp
    }
    
    return message"""
    elif template_type == 'mac':
        # MAC地址表消息模板
        python_code = """def generate_message(env):
    \"\"\"
    生成MAC地址表消息
    
    :param env: 环境变量字典，包含所有必要的参数
    :return: 消息字典
    \"\"\"
    import random
    import hashlib
    
    # 从环境变量获取设备信息
    device_ip = env.get("DEVICE_IP", "*************")
    device_id = env.get("DEVICE_ID", f"ip:{device_ip}")
    timestamp = int(env.get("TIMESTAMP", str(int(time.time() * 1000))))
    
    # 生成MAC地址数量
    mac_count = int(env.get('MAC_COUNT', 50))
    
    # VLAN选项
    vlan_choices = [\"vlan1\", \"vlan10\", \"vlan20\", \"vlan100\", \"vlan1003\"]
    
    # 端口选项
    port_choices = [\"Te0/49\", \"Gi1/0/1\", \"Gi1/0/2\", \"Gi1/0/3\", \"Gi1/0/4\", \"Gi2/0/1\"]
    
    # 生成MAC地址列表
    mac_list = []
    for i in range(mac_count):
        # 根据设备ID和索引生成唯一的MAC
        seed = f\"{device_id}:mac:{i}\"
        hash_obj = hashlib.md5(seed.encode())
        hash_int = int(hash_obj.hexdigest(), 16)
        
        # 生成MAC地址
        mac_addr = f\"{(hash_int & 0xFF):02x}:{((hash_int >> 8) & 0xFF):02x}:{((hash_int >> 16) & 0xFF):02x}:{((hash_int >> 24) & 0xFF):02x}:{((hash_int >> 32) & 0xFF):02x}:{((hash_int >> 40) & 0xFF):02x}\"
        
        # 随机选择vlan和端口
        vlan = random.choice(vlan_choices)
        port = random.choice(port_choices)
        
        mac_info = {
            \"vlan\": vlan,
            \"port\": port,
            \"mac\": mac_addr
        }
        mac_list.append(mac_info)
    
    # 构建消息
    numeric_id = int(''.join([part.zfill(3) for part in device_ip.split('.')])[-4:])
    message = {
        \"devflag\": f\"ip:{device_ip}\",
        \"macList\": mac_list,
        \"interval\": 900,
        \"id\": numeric_id,
        \"deviceId\": f\"ip:{device_ip}\",
        \"startTimestamp\": timestamp - 312,
        \"timestamp\": timestamp
    }
    
    return message"""
    else:  # custom
        # 自定义消息模板
        python_code = """def generate_message(env):
    \"\"\"
    生成自定义消息
    
    :param env: 环境变量字典，包含所有必要的参数
    :return: 消息字典
    \"\"\"
    import random
    import json
    import time
    
    # 从环境变量获取设备信息
    device_ip = env.get("DEVICE_IP", "*************")
    device_id = env.get("DEVICE_ID", f"ip:{device_ip}")
    timestamp = int(env.get("TIMESTAMP", str(int(time.time() * 1000))))
    index = int(env.get("INDEX", "0"))
    
    # 生成随机数据
    random_value = random.randint(1, 100)
    
    # 从环境变量中获取配置
    kafka_env = env.get('KAFKA_ENV_NAME', 'test')
    device_id_value = env.get('DEVICE_ID', '1234')
    
    # 构建消息
    message = {
        "deviceId": device_id,
        "deviceIp": device_ip,
        "timestamp": timestamp,
        "data": {
            "value": random_value,
            "environment": kafka_env,
            "customId": device_id_value,
            "messageIndex": index
        }
    }
    
    return message"""
    
    return jsonify({
        "name": f"{template_type.capitalize()} 消息模板",
        "description": f"生成{template_type.capitalize()}类型的消息",
        "python_code": python_code
    })

@bp.route('/api/preview', methods=['POST'])
def api_preview():
    """预览消息模板API"""
    data = request.get_json()
    if not data or 'code' not in data:
        return jsonify({"success": False, "error": "无效的请求数据"}), 400
    
    code = data['code']
    user_env = data.get('env', {}) # 获取用户传入的环境变量

    try:
        # 构造测试环境
        env = {
            "MAC_COUNT": "10",
            "ARP_COUNT": "10",
            "MESSAGE_RATE": "10",
            # 添加模拟设备信息，这样模板可以在需要时使用这些值
            "DEVICE_ID": "ip:*************",
            "DEVICE_IP": "*************",
            "INDEX": "0",
            "TIMESTAMP": str(int(time.time() * 1000))
        }
        # 合并用户传入的环境变量，用户变量会覆盖默认值
        env.update(user_env)
        
        # 创建一个本地命名空间
        local_namespace = {}
        
        # 执行代码
        exec(code, globals(), local_namespace)
        
        # 检查是否有生成消息的函数
        if 'generate_message' not in local_namespace:
            return jsonify({"success": False, "error": "代码中未定义generate_message函数"}), 400
        
        # 调用函数生成消息，只传入env参数
        message = local_namespace['generate_message'](env)
        
        return jsonify({"success": True, "message": message})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 400
