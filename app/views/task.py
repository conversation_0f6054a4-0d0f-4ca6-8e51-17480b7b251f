from flask import (
    Blueprint, render_template, request, redirect, url_for, jsonify, current_app, abort
)
import time


bp = Blueprint('task', __name__, url_prefix='/task')

@bp.route('/')
def index():
    """任务列表页面"""
    return render_template('task/index.html')

@bp.route('/create')
def create():
    """创建任务页面"""
    return render_template('task/edit.html', task_id=0)

@bp.route('/edit/<int:task_id>')
def edit(task_id):
    """编辑任务页面"""
    return render_template('task/edit.html', task_id=task_id)

@bp.route('/run/<int:task_id>')
def run(task_id):
    """运行任务页面"""
    return render_template('task/run.html', task_id=task_id)

@bp.route('/view_run/<int:task_id>')
def view_run(task_id):
    """实时查看运行任务页面，使用历史记录视图模板"""
    return render_template('history/view.html', task_id=task_id, history_id=None, is_task_run=True)

@bp.route('/view_run_by_id/<path:run_id>')
def view_run_by_id(run_id):
    """通过运行ID查看任务运行状态"""
    app = current_app._get_current_object()
    history_id = None
    task_id = None

    # 从当前运行的任务中查找对应的记录
    if hasattr(app, 'running_tasks') and run_id in app.running_tasks:
        task_info = app.running_tasks[run_id]
        task_id = task_info.get('task_id')
        history_id = task_info.get('history_id')

    # 如果找不到运行信息，尝试从历史记录中查找
    if history_id is None:
        from app.models.history import TestRunHistory
        history = TestRunHistory.query.filter_by(run_id=run_id).first()
        if history:
            history_id = history.id
            task_id = history.task_id

    # 如果找到了历史记录，直接重定向到历史页面
    if history_id:
        return redirect(url_for('history.view', history_id=history_id))

    # 如果只找到任务ID，使用普通的任务查看页面
    if task_id:
        return render_template('history/view.html', task_id=task_id, history_id=None, is_task_run=True, run_id=run_id)

    # 都找不到，显示错误
    return abort(404, description=f"找不到运行ID为 {run_id} 的任务")

@bp.route('/api/running_tasks')
def api_running_tasks():
    """获取所有正在运行的任务"""
    from app import SOCKETIO_AVAILABLE

    running_tasks = {}

    # 如果支持WebSocket，从WebSocket事件处理器获取运行任务
    if SOCKETIO_AVAILABLE:
        try:
            from app.websocket_events import get_running_tasks
            websocket_tasks = get_running_tasks()

            for task_info in websocket_tasks:
                task_id = task_info['task_id']
                running_tasks[f"ws_{task_id}"] = {
                    'task_id': task_id,
                    'history_id': None,  # WebSocket任务可能没有history_id
                    'status': 'running',
                    'message_count': task_info.get('message_count', 0),
                    'cycle_count': task_info.get('cycle_count', 0),
                    'error_count': task_info.get('error_count', 0),
                    'cycle_duration': 60,  # 默认值
                    'cycle_elapsed': 0,
                    'start_time': None
                }
        except Exception as e:
            print(f"获取WebSocket运行任务失败: {e}")

    # 获取传统方式运行的任务
    app = current_app._get_current_object()
    if hasattr(app, 'running_tasks'):
        for run_id, task_info in app.running_tasks.items():
            if task_info.get('running', False):
                # 收集必要的任务信息
                running_tasks[run_id] = {
                    'task_id': task_info.get('task_id'),
                    'history_id': task_info.get('history_id'),
                    'status': task_info.get('status', 'running'),
                    'message_count': task_info.get('message_count', 0),
                    'cycle_count': task_info.get('cycle_count', 0),
                    'error_count': task_info.get('error_count', 0),
                    'cycle_duration': task_info.get('cycle_duration', 300),
                    'cycle_elapsed': task_info.get('cycle_elapsed', 0),
                    'start_time': task_info.get('start_time')
                }

    return jsonify({
        'success': True,
        'tasks': running_tasks
    })

@bp.route('/api/env_config_list/<int:env_id>')
def api_env_config_list(env_id=None):
    """获取指定环境的配置"""
    env_data = current_app.env_manager.get_environment(env_id)

    if not env_data:
        return jsonify({"success": False, "error": "环境不存在"}), 404

    return jsonify(env_data)


@bp.route('/api/list')
def api_list():
    """获取任务列表API"""
    from app.models.task import TestTask

    # 查询所有任务
    tasks = TestTask.query.all()

    # 转换为字典列表
    task_list = [task.to_dict() for task in tasks]

    return jsonify(task_list)

@bp.route('/api/get/<int:task_id>')
def api_get(task_id):
    """获取任务详情API"""
    from app.models.task import TestTask

    # 查询指定ID的任务
    task = TestTask.query.get(task_id)

    if task:
        # 转换为字典并返回
        return jsonify(task.to_dict())
    else:
        return jsonify({"success": False, "error": "任务不存在"}), 404

@bp.route('/api/save', methods=['POST'])
def api_save():
    """保存任务API"""
    from app.models.task import TestTask, db

    task_data = request.get_json()
    if not task_data:
        return jsonify({"success": False, "error": "无效的请求数据"}), 400

    # 检查必要字段
    if not task_data.get('name') or not task_data.get('environment_id'):
        return jsonify({"success": False, "error": "缺少必要字段"}), 400

    # 确保queues字段存在
    if 'queues' not in task_data:
        task_data['queues'] = []

    try:
        # 如果有ID，则更新现有任务
        if task_data.get('id'):
            task = TestTask.query.get(task_data['id'])
            if not task:
                return jsonify({"success": False, "error": "任务不存在"}), 404

            # 更新基本信息
            task.name = task_data.get('name')
            task.description = task_data.get('description')
            task.environment_id = task_data.get('environment_id')
            task.cycle_duration = task_data.get('cycle_duration', 300)
            task.total_duration = task_data.get('total_duration', 0)

            # 清除现有队列项
            task.queues = []

            # 添加新队列项
            from app.models.task import TaskQueue
            for queue_data in task_data.get('queues', []):
                task.queues.append(TaskQueue.from_dict(queue_data))

        else:
            # 创建新任务
            task = TestTask.from_dict(task_data)
            db.session.add(task)

        # 提交更改
        db.session.commit()

        return jsonify({"success": True, "id": task.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/delete/<int:task_id>', methods=['DELETE'])
def api_delete(task_id):
    """删除任务API"""
    from app.models.task import TestTask, db

    # 查询指定ID的任务
    task = TestTask.query.get(task_id)

    if not task:
        return jsonify({"success": False, "error": "任务不存在"}), 404

    try:
        # 删除任务
        db.session.delete(task)
        db.session.commit()
        return jsonify({"success": True})
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/environments')
def api_environments():
    """获取环境变量列表API，用于任务编辑页面"""
    env_list = current_app.env_manager.list_environments()
    return jsonify(env_list)

@bp.route('/api/templates')
def api_templates():
    """获取模板列表API，用于任务编辑页面"""
    from app.models.template import MessageTemplate

    # 查询所有消息模板
    templates = MessageTemplate.query.all()

    # 如果没有模板数据，创建默认模板
    if not templates:
        # 创建默认的ARP条目消息模板
        from app import db

        arp_template = MessageTemplate(
            name="ARP条目消息",
            description="生成设备的ARP条目消息，包含IP地址、MAC地址和接口信息",
            python_code="""def generate_message(env, device_id, device_ip, index, timestamp):
    \"\"\"
    生成ARP条目消息

    :param env: 环境变量字典
    :param device_id: 设备ID
    :param device_ip: 设备IP
    :param index: 消息索引
    :param timestamp: 当前时间戳(毫秒)
    :return: 消息字典
    \"\"\"
    import random
    import hashlib

    # 生成设备SN (基于设备IP)
    ip_last_octet = int(device_ip.split('.')[-1])
    if ip_last_octet % 3 == 0:
        prefix = "G1SB0UM"  # S6150设备
    elif ip_last_octet % 3 == 1:
        prefix = "G1SC17K"  # S5760设备
    else:
        prefix = "G1SC2LG"  # S5750设备

    device_sn = f"{prefix}{random.randint(100000, 999999)}"

    # 接口列表
    interfaces = [
        "Mg0", "Or14", "Or2001", "Vl501", "Vl666", "Vl888", "Vl1002",
        "Te1/0/1", "Te1/0/2", "Gi1/0/1", "Gi1/0/2", "Vl1", "Vl10", "Vl100"
    ]

    # 生成ARP条目数量
    arp_count = int(env.get('ARP_COUNT', 50))

    # 生成ARP条目列表
    arp_list = []
    for i in range(arp_count):
        # 根据设备ID和索引生成唯一的IP和MAC
        seed = f"{device_id}:ip:{i}"
        hash_obj = hashlib.md5(seed.encode())
        hash_int = int(hash_obj.hexdigest(), 16)

        # 生成IP地址
        octet1 = (hash_int & 0xFF) % 223 + 1  # 1-223
        octet2 = ((hash_int >> 8) & 0xFF) % 255 + 1  # 1-255
        octet3 = ((hash_int >> 16) & 0xFF) % 255 + 1  # 1-255
        octet4 = ((hash_int >> 24) & 0xFF) % 254 + 1  # 1-254

        # 使用设备所在网段的IP (前10个)
        if i < 10:
            ip_parts = device_ip.split('.')
            octet1 = int(ip_parts[0])
            octet2 = int(ip_parts[1])
            octet3 = int(ip_parts[2])
            octet4 = (int(ip_parts[3]) + i + 1) % 254 + 1

        ip_addr = f"{octet1}.{octet2}.{octet3}.{octet4}"

        # 生成MAC地址 (基于IP)
        mac_addr = f"{octet1:02x}:{octet2:02x}:{octet3:02x}:{hash_int & 0xFF:02x}:{(hash_int >> 8) & 0xFF:02x}:{(hash_int >> 16) & 0xFF:02x}"

        # 随机选择接口
        interface = random.choice(interfaces)

        # 创建ARP条目
        arp_entry = {
            "ip": ip_addr,
            "mac": mac_addr,
            "vlan": f"vlan{random.randint(1, 100)}",
            "type": "dynamic",
            "interface": interface
        }
        arp_list.append(arp_entry)

    # 构建消息
    numeric_id = int(''.join([part.zfill(3) for part in device_ip.split('.')])[-4:])
    message = {
        "devflag": f"ip:{device_ip}",
        "arpList": arp_list,
        "interval": 300,
        "id": numeric_id,
        "deviceId": f"ip:{device_ip}",
        "startTimestamp": timestamp - 312,
        "timestamp": timestamp
    }

    return message""",
            sample_output="""{\n  \"devflag\": \"ip:*************\",\n  \"arpList\": [\n    {\n      \"ip\": \"*************\",\n      \"mac\": \"00:11:22:33:44:55\",\n      \"vlan\": \"vlan1\",\n      \"type\": \"dynamic\",\n      \"interface\": \"Gi1/0/1\"\n    }\n  ],\n  \"interval\": 300,\n  \"id\": 1100,\n  \"deviceId\": \"ip:*************\",\n  \"startTimestamp\": 1621234567688,\n  \"timestamp\": 1621234568000\n}"""
        )

        mac_template = MessageTemplate(
            name="MAC地址表消息",
            description="生成设备的MAC地址表消息，包含VLAN、端口和MAC地址信息",
            python_code="""def generate_message(env, device_id, device_ip, index, timestamp):
    \"\"\"
    生成MAC地址表消息

    :param env: 环境变量字典
    :param device_id: 设备ID
    :param device_ip: 设备IP
    :param index: 消息索引
    :param timestamp: 当前时间戳(毫秒)
    :return: 消息字典
    \"\"\"
    import random
    import hashlib

    # 生成MAC地址数量
    mac_count = int(env.get('MAC_COUNT', 50))

    # VLAN选项
    vlan_choices = ["vlan1", "vlan10", "vlan20", "vlan100", "vlan1003"]

    # 端口选项
    port_choices = ["Te0/49", "Gi1/0/1", "Gi1/0/2", "Gi1/0/3", "Gi1/0/4", "Gi2/0/1"]

    # 生成MAC地址列表
    mac_list = []
    for i in range(mac_count):
        # 根据设备ID和索引生成唯一的MAC
        seed = f"{device_id}:mac:{i}"
        hash_obj = hashlib.md5(seed.encode())
        hash_int = int(hash_obj.hexdigest(), 16)

        # 生成MAC地址
        mac_addr = f"{(hash_int & 0xFF):02x}:{((hash_int >> 8) & 0xFF):02x}:{((hash_int >> 16) & 0xFF):02x}:{((hash_int >> 24) & 0xFF):02x}:{((hash_int >> 32) & 0xFF):02x}:{((hash_int >> 40) & 0xFF):02x}"

        # 随机选择vlan和端口
        vlan = random.choice(vlan_choices)
        port = random.choice(port_choices)

        mac_info = {
            "vlan": vlan,
            "port": port,
            "mac": mac_addr
        }
        mac_list.append(mac_info)

    # 构建消息
    numeric_id = int(''.join([part.zfill(3) for part in device_ip.split('.')])[-4:])
    message = {
        "devflag": f"ip:{device_ip}",
        "macList": mac_list,
        "interval": 900,
        "id": numeric_id,
        "deviceId": f"ip:{device_ip}",
        "startTimestamp": timestamp - 312,
        "timestamp": timestamp
    }

    return message""",
            sample_output="""{\n  \"devflag\": \"ip:*************\",\n  \"macList\": [\n    {\n      \"vlan\": \"vlan1\",\n      \"port\": \"Gi1/0/1\",\n      \"mac\": \"00:11:22:33:44:55\"\n    }\n  ],\n  \"interval\": 900,\n  \"id\": 1100,\n  \"deviceId\": \"ip:*************\",\n  \"startTimestamp\": 1621234567688,\n  \"timestamp\": 1621234568000\n}"""
        )

        # 添加并保存默认模板
        db.session.add(arp_template)
        db.session.add(mac_template)
        db.session.commit()

        # 重新查询模板列表
        templates = MessageTemplate.query.all()

    # 转换为简化的字典列表
    template_list = [
        {
            "id": template.id,
            "name": template.name,
            "topic": "",  # 模板本身不包含topic，由任务队列项指定
            "description": template.description
        } for template in templates
    ]

    return jsonify(template_list)

@bp.route('/api/run/<int:task_id>', methods=['POST'])
def api_run(task_id):
    """运行任务API - 使用WebSocket进行实时输出"""
    import uuid
    from app.models.task import TestTask
    from app.models.history import TestRunHistory, db
    import json

    # 获取请求参数
    data = request.json or {}
    force = data.get('force', False)  # 是否强制运行（停止已有实例）

    # 查询指定ID的任务
    task = TestTask.query.get(task_id)

    if not task:
        return jsonify({"success": False, "error": "任务不存在"}), 404

    # 检查是否有该任务的实例正在运行
    app = current_app._get_current_object()
    running_instance = None

    if hasattr(app, 'running_tasks'):
        for run_id, task_info in app.running_tasks.items():
            if task_info.get('running', False) and task_info.get('task_id') == task_id:
                running_instance = {
                    'run_id': run_id,
                    'history_id': task_info.get('history_id')
                }
                break

    # 如果有实例在运行，且不是强制运行，则返回错误
    if running_instance and not force:
        return jsonify({
            "success": False,
            "error": "该任务已有实例在运行",
            "running_instance": running_instance,
            "need_force": True
        }), 409  # 409 Conflict

    # 如果有实例在运行，且是强制运行，则停止现有实例
    if running_instance and force:
        try:
            # 停止现有实例
            run_id = running_instance['run_id']

            # 更新内存中的状态
            if run_id in app.running_tasks:
                app.running_tasks[run_id]['running'] = False
                app.running_tasks[run_id]['status'] = 'stopped'

            # 更新数据库状态
            from datetime import datetime
            history = TestRunHistory.query.filter_by(run_id=run_id).first()
            if history:
                history.status = 'stopped'
                history.end_time = datetime.now()

                # 如果有统计数据，从内存中更新到数据库
                if run_id in app.running_tasks:
                    task_status = app.running_tasks[run_id]
                    history.message_count = task_status.get('message_count', 0)
                    history.cycle_count = task_status.get('cycle_count', 0)
                    history.error_count = task_status.get('error_count', 0)

                db.session.commit()
                print(f"已停止任务 {task.name} 的现有实例 {run_id}")
        except Exception as e:
            print(f"停止任务现有实例时出错: {e}")
            # 继续运行新实例，即使停止旧实例失败

    try:
        # 创建运行ID
        run_id = f"run_{uuid.uuid4().hex}"

        # 使用数据库环境变量管理器获取环境变量
        env_data = current_app.env_manager.get_environment(task.environment_id)
        if not env_data:
            # 如果获取失败，可以设置一个空的默认值，或者返回错误
            print(f"警告: 未能获取到 environment_id {task.environment_id} 的配置，将使用空的环境变量。")
            env_data = {'variables': {}}
        else:
            print(f"通过 DbEnvManager 获取到的环境变量: {env_data.get('variables', {})}")

        # 创建历史记录
        history = TestRunHistory(
            run_id=run_id,
            task_id=task.id,
            environment_id=task.environment_id,
            status='running',
            # 存储环境变量快照
            environment_snapshot=json.dumps(env_data),
            # 存储任务配置快照
            task_snapshot=json.dumps(task.to_dict())
        )

        db.session.add(history)
        db.session.commit()

        # 统一使用线程方式运行任务，通过WebSocket发送日志
        import threading
        from app.models.template import MessageTemplate
        from app.kafka_utils import KafkaProducer
        import time
        import json
        import os
        from datetime import datetime

        # 全局运行状态字典
        if not hasattr(current_app, 'running_tasks'):
            current_app.running_tasks = {}

        # 初始化任务状态
        current_app.running_tasks[run_id] = {
            'status': 'running',
            'message_count': 0,
            'cycle_count': 0,
            'error_count': 0,
            'running': True,
            'latest_messages': {},
            'task_id': task.id,
            'history_id': history.id,
            'start_time': time.time(),
            'logs': []  # 添加日志列表
        }

        # 获取当前应用实例的引用
        app = current_app._get_current_object()

        # 定义日志记录函数
        def add_log(run_id, level, message):
            """添加日志到任务状态"""
            if run_id in app.running_tasks:
                timestamp = datetime.now().strftime('%H:%M:%S')
                log_entry = {
                    'timestamp': timestamp,
                    'level': level,
                    'message': message
                }
                app.running_tasks[run_id]['logs'].append(log_entry)

                # 限制日志数量，只保留最近的10条（调试用）
                if len(app.running_tasks[run_id]['logs']) > 10:
                    app.running_tasks[run_id]['logs'] = app.running_tasks[run_id]['logs'][-10:]

                # 使用WebSocket日志模块发送日志
                try:
                    from app.websocket_logger import log_to_websocket
                    log_to_websocket(run_id, level, message)
                except Exception as e:
                    # 如果WebSocket日志发送失败，至少打印到控制台
                    print(f"[{timestamp}] {level.upper()}: {message}")
                    print(f"WebSocket日志发送失败: {e}")

        # 定义测试运行函数
        def run_test(app, run_id, task_snapshot, env_snapshot):
            try:
                add_log(run_id, 'info', '任务开始运行...')

                # 解析任务配置
                task_config = json.loads(task_snapshot)
                task_env_snapshot = json.loads(env_snapshot) if env_snapshot else {}

                add_log(run_id, 'info', f"环境变量快照内容: {task_env_snapshot}")

                # 直接从环境变量快照中获取环境变量
                env_vars = task_env_snapshot.get('variables', {})
                add_log(run_id, 'info', f"从环境变量快照中获取到的环境变量: {env_vars}")

                # 从环境变量中获取Kafka服务器地址
                kafka_servers = env_vars.get('KAFKA_SERVERS')
                if kafka_servers:
                    os.environ['KAFKA_SERVERS'] = kafka_servers
                    add_log(run_id, 'info', f"使用环境变量中的Kafka服务器地址: {kafka_servers}")
                else:
                    add_log(run_id, 'warning', "环境变量中未指定KAFKA_SERVERS，将使用默认值")

                # 创建Kafka生产者
                producer = KafkaProducer()

                # 获取队列配置
                queues = task_config.get('queues', [])
                cycle_duration = task_config.get('cycle_duration', 300)  # 默认5分钟
                total_duration = task_config.get('total_duration', 0)  # 0表示无限运行

                # 运行时间计数
                start_time = time.time()
                cycle_start_time = start_time

                # 循环发送消息，直到被停止或达到总运行时间
                while app.running_tasks.get(run_id, {}).get('running', False):
                    # 检查是否达到总运行时间
                    if total_duration > 0 and (time.time() - start_time) > total_duration:
                        break

                    # 检查是否完成一个周期
                    current_time = time.time()
                    cycle_elapsed = current_time - cycle_start_time

                    # 计算在当前周期中已过的秒数（取模，确保值在0到cycle_duration之间）
                    cycle_elapsed_in_current = cycle_elapsed % cycle_duration

                    # 更新周期已过时间
                    app.running_tasks[run_id]['cycle_elapsed'] = cycle_elapsed_in_current

                    # 每个周期结束时重置队列发送计数器
                    if cycle_elapsed >= cycle_duration:  # 修改判断条件，直接检查总经过时间
                        # 计算完成的周期数
                        completed_cycles = int(cycle_elapsed / cycle_duration)

                        # 如果完成了多个周期，增加相应的周期计数
                        if completed_cycles > 0:
                            app.running_tasks[run_id]['cycle_count'] += completed_cycles
                            print(f"周期结束，增加周期计数 {completed_cycles}，当前总周期数: {app.running_tasks[run_id]['cycle_count']}")

                        # 重置周期计时器，保留未完成部分
                        cycle_start_time = current_time
                        # 重置周期已过时间
                        app.running_tasks[run_id]['cycle_elapsed'] = 0
                        print(f"重置周期计时器，新的周期开始时间: {cycle_start_time}，周期已过时间: 0秒")

                        # 重置队列发送计数器
                        for queue_id in range(len(queues)):
                            queue_key = f"{run_id}_{queue_id}_sent_times"
                            if queue_key in app.running_tasks[run_id]:
                                app.running_tasks[run_id][queue_key] = 0

                        # 将运行数据保存到数据库
                        with app.app_context():
                            try:
                                from app.models.history import TestRunHistory, db
                                history = TestRunHistory.query.filter_by(run_id=run_id).first()
                                if history:
                                    # 将当前运行数据存储为JSON
                                    run_data = {
                                        'message_count': app.running_tasks[run_id]['message_count'],
                                        'cycle_count': app.running_tasks[run_id]['cycle_count'],
                                        'error_count': app.running_tasks[run_id]['error_count'],
                                        'latest_messages': app.running_tasks[run_id]['latest_messages'],
                                        'cycle_elapsed': cycle_elapsed_in_current,
                                        'cycle_duration': cycle_duration,
                                        'last_updated': time.time()
                                    }

                                    # 添加每个队列的消息计数
                                    for q_idx in range(len(queues)):
                                        queue_key_count = f"queue_{q_idx}_count"
                                        if queue_key_count in app.running_tasks[run_id]:
                                            run_data[queue_key_count] = app.running_tasks[run_id][queue_key_count]

                                    history.run_data = json.dumps(run_data)
                                    db.session.commit()
                            except Exception as e:
                                print(f"保存运行数据到数据库时发生错误: {str(e)}")

                    # 为每个队列发送消息
                    for i, queue in enumerate(queues):
                        if not queue.get('enabled', True):
                            continue

                        try:
                            # 获取模板
                            template_id = queue.get('template_id')
                            with app.app_context():
                                template = MessageTemplate.query.get(template_id)

                            if not template:
                                continue

                            # 从模板中获取主题，而不是从队列配置中获取
                            topic = template.topic if template.topic else 'default-topic'
                            # 获取每周期发送次数
                            cycle_send_times = queue.get('rate', 1)  # 每个周期发送次数

                            # 简化发送逻辑：每次循环都发送消息（用于测试日志功能）
                            # 获取队列在当前周期已发送的次数
                            queue_key = f"{run_id}_{i}_sent_times"
                            sent_times = app.running_tasks[run_id].get(queue_key, 0)

                            # 计算当前在周期中的位置百分比
                            cycle_progress = (current_time - cycle_start_time) / cycle_duration
                            # 计算应该发送的次数（基于当前周期进度）
                            should_send_times = max(1, int(cycle_progress * cycle_send_times))

                            # 如果应发送次数大于已发送次数，则发送消息
                            should_send = should_send_times > sent_times

                            # 如果发送了消息，更新已发送次数
                            if should_send:
                                app.running_tasks[run_id][queue_key] = should_send_times

                            # 如果不需要发送，跳过当前队列
                            if not should_send:
                                continue

                            # 获取模板代码和环境变量
                            code = template.python_code
                            env_vars = json.loads(template.env_vars) if template.env_vars else {}

                            # 合并环境变量
                            # 使用从环境变量快照中获取的环境变量
                            env = env_vars.copy()  # 使用从任务环境快照中获取的环境变量

                            # 生成消息
                            try:
                                # 创建本地命名空间
                                local_vars = {'env': env}

                                # 执行模板代码
                                exec(code, globals(), local_vars)

                                # 获取生成的消息
                                if 'generate_message' in local_vars:
                                    message = local_vars['generate_message'](env)
                                else:
                                    message = {"error": "模板中未定义generate_message函数"}

                                # 发送消息到Kafka
                                # 如果消息是数组，循环发送每个元素
                                if isinstance(message, list):
                                    last_msg_item = None  # 记录最后一条消息用于更新latest_messages
                                    for msg_item in message:
                                        # 直接发送原始消息对象，KafkaProducer的value_serializer会处理序列化
                                        producer.send(topic, msg_item)
                                        # 每发送一条消息就增加计数
                                        app.running_tasks[run_id]['message_count'] += 1
                                        # 更新队列特定的消息计数
                                        queue_key_count = f"queue_{i}_count"
                                        app.running_tasks[run_id][queue_key_count] = app.running_tasks[run_id].get(queue_key_count, 0) + 1
                                        add_log(run_id, 'info', f"发送数组消息元素到主题 {topic}: {msg_item}")
                                        last_msg_item = msg_item  # 更新最后一条消息

                                    # 更新最新消息，只保存最后一条发送的消息
                                    app.running_tasks[run_id]['latest_messages'][str(i)] = {
                                        'topic': topic,
                                        'message': last_msg_item  # 存储最后一条消息，而不是整个数组
                                    }
                                else:
                                    # 如果不是数组，直接发送整个消息
                                    # 直接发送原始消息对象，KafkaProducer的value_serializer会处理序列化
                                    producer.send(topic, message)
                                    # 更新消息计数
                                    app.running_tasks[run_id]['message_count'] += 1
                                    # 更新队列特定的消息计数
                                    queue_key_count = f"queue_{i}_count"
                                    app.running_tasks[run_id][queue_key_count] = app.running_tasks[run_id].get(queue_key_count, 0) + 1
                                    add_log(run_id, 'info', f"发送单条消息到主题 {topic}: {message}")

                                    # 更新最新消息
                                    app.running_tasks[run_id]['latest_messages'][str(i)] = {
                                        'topic': topic,
                                        'message': message
                                    }

                            except Exception as e:
                                # 更新错误计数
                                app.running_tasks[run_id]['error_count'] += 1
                                add_log(run_id, 'error', f"消息生成错误: {str(e)}")

                        except Exception as e:
                            # 更新错误计数
                            app.running_tasks[run_id]['error_count'] += 1
                            add_log(run_id, 'error', f"队列处理错误: {str(e)}")

                    # 控制发送速率
                    time.sleep(1)  # 简化处理，每秒循环一次

                # 测试结束，更新状态
                app.running_tasks[run_id]['status'] = 'completed'
                app.running_tasks[run_id]['running'] = False
                add_log(run_id, 'info', '任务运行完成')

                # 从WebSocket日志处理器注销任务
                try:
                    from app.websocket_logger import unregister_task_logger
                    unregister_task_logger(run_id)
                except Exception as e:
                    print(f"注销WebSocket日志处理器失败: {e}")

                # 在应用上下文中更新历史记录
                with app.app_context():
                    from app.models.history import TestRunHistory, db
                    history = TestRunHistory.query.filter_by(run_id=run_id).first()
                    if history:
                        history.status = 'completed'
                        history.end_time = datetime.now()
                        history.message_count = app.running_tasks[run_id]['message_count']
                        history.cycle_count = app.running_tasks[run_id]['cycle_count']
                        history.error_count = app.running_tasks[run_id]['error_count']
                        db.session.commit()

            except Exception as e:
                # 测试异常，更新状态
                app.running_tasks[run_id]['status'] = 'error'
                app.running_tasks[run_id]['running'] = False
                add_log(run_id, 'error', f"测试运行错误: {str(e)}")

                # 从WebSocket日志处理器注销任务
                try:
                    from app.websocket_logger import unregister_task_logger
                    unregister_task_logger(run_id)
                except Exception as unreg_e:
                    print(f"注销WebSocket日志处理器失败: {unreg_e}")

                # 在应用上下文中更新历史记录
                try:
                    with app.app_context():
                        from app.models.history import TestRunHistory, db
                        history = TestRunHistory.query.filter_by(run_id=run_id).first()
                        if history:
                            history.status = 'error'
                            history.end_time = datetime.now()
                            history.error_message = str(e)
                            db.session.commit()
                except Exception as db_error:
                    add_log(run_id, 'error', f"更新历史记录时发生错误: {str(db_error)}")

        # 注册任务到WebSocket日志处理器
        try:
            from app.websocket_logger import register_task_logger
            register_task_logger(task_id, run_id)
        except Exception as e:
            print(f"注册WebSocket日志处理器失败: {e}")

        # 启动后台线程
        test_thread = threading.Thread(
            target=run_test,
            args=(app, run_id, history.task_snapshot, history.environment_snapshot),
            daemon=True
        )
        test_thread.start()

        # 返回运行ID和历史记录ID
        return jsonify({
            "success": True,
            "run_id": run_id,
            "history_id": history.id
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/stop/<path:run_id>', methods=['POST'])
def api_stop(run_id):
    """停止任务API"""
    from app.models.history import TestRunHistory, db
    from datetime import datetime

    # 统一的任务停止逻辑
    # 查询指定运行ID的历史记录
    history = TestRunHistory.query.filter_by(run_id=run_id).first()

    if not history:
        return jsonify({"success": False, "error": "运行记录不存在"}), 404

    try:
        # 更新全局运行状态字典，通知后台线程停止
        if hasattr(current_app, 'running_tasks') and run_id in current_app.running_tasks:
            current_app.running_tasks[run_id]['running'] = False
            current_app.running_tasks[run_id]['status'] = 'stopped'

            # 从WebSocket日志处理器注销任务
            try:
                from app.websocket_logger import unregister_task_logger
                unregister_task_logger(run_id)
            except Exception as e:
                print(f"注销WebSocket日志处理器失败: {e}")

        # 更新数据库状态为已停止
        history.status = 'stopped'
        history.end_time = datetime.now()

        # 如果有统计数据，从内存中更新到数据库
        if hasattr(current_app, 'running_tasks') and run_id in current_app.running_tasks:
            task_status = current_app.running_tasks[run_id]
            history.message_count = task_status.get('message_count', 0)
            history.cycle_count = task_status.get('cycle_count', 0)
            history.error_count = task_status.get('error_count', 0)

        # 提交更改
        db.session.commit()

        return jsonify({"success": True})
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "error": str(e)}), 500

@bp.route('/api/status/<path:run_id>')
def api_status(run_id):
    """获取任务状态API"""
    from app.models.history import TestRunHistory, db
    import json

    # 检查全局运行状态字典
    if hasattr(current_app, 'running_tasks') and run_id in current_app.running_tasks:
        # 从内存中获取任务状态
        task_status = current_app.running_tasks[run_id]

        # 获取周期已过时间
        cycle_elapsed = task_status.get('cycle_elapsed', 0)
        cycle_count = task_status.get('cycle_count', 0)
        print(f"[api_status] 从内存中获取周期已过时间: {cycle_elapsed}秒, 总周期数: {cycle_count}")

        # 查询历史记录
        history = TestRunHistory.query.filter_by(run_id=run_id).first()

        if history:
            # 将当前状态保存到数据库
            run_data = {
                'message_count': task_status.get('message_count', 0),
                'cycle_count': cycle_count,
                'error_count': task_status.get('error_count', 0),
                'latest_messages': task_status.get('latest_messages', {}),
                'cycle_elapsed': cycle_elapsed,
                'cycle_duration': task_status.get('cycle_duration', 300),
                'last_updated': time.time()
            }

            # 添加队列特定的消息计数
            for key, value in task_status.items():
                if key.startswith('queue_') and key.endswith('_count'):
                    run_data[key] = value

            # 更新历史记录
            history.run_data = json.dumps(run_data)
            history.cycle_count = cycle_count
            history.status = task_status.get('status', 'running')

            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"保存运行数据时发生错误: {e}")

        # 准备API响应数据
        response_data = {
            'status': task_status.get('status', 'running'),
            'running': task_status.get('running', True),
            'message_count': task_status.get('message_count', 0),
            'cycle_count': cycle_count,
            'error_count': task_status.get('error_count', 0),
            'latest_messages': task_status.get('latest_messages', {}),
            'cycle_elapsed': cycle_elapsed,
            'cycle_duration': task_status.get('cycle_duration', 300)
        }

        # 添加队列特定的消息计数到响应中
        for key, value in task_status.items():
            if key.startswith('queue_') and key.endswith('_count'):
                response_data[key] = value

        # 添加队列信息
        if history:
            try:
                # 解析任务快照以获取队列信息
                task_snapshot = json.loads(history.task_snapshot) if history.task_snapshot else {}
                if 'queues' in task_snapshot:
                    queues = []
                    for queue_index, queue in enumerate(task_snapshot.get('queues', [])):
                        # 获取模板ID
                        template_id = queue.get('template_id')

                        # 获取模板信息以获取正确的主题名
                        from app.models.template import MessageTemplate
                        template = MessageTemplate.query.get(template_id)
                        topic = template.topic if template and template.topic else queue.get('topic', 'default-topic')

                        # 从最新消息中获取主题名
                        if 'latest_messages' in task_status and str(queue_index) in task_status['latest_messages']:
                            message_data = task_status['latest_messages'][str(queue_index)]
                            if 'topic' in message_data:
                                topic = message_data['topic']

                        # 获取队列特定的消息计数
                        queue_key_count = f"queue_{queue_index}_count"
                        queue_message_count = task_status.get(queue_key_count, 0)

                        queue_info = {
                            "topic": topic,
                            "template_id": template_id,
                            "message_count": queue_message_count,
                            "average_rate": "-"  # 不计算速率
                        }
                        queues.append(queue_info)

                    response_data['queues'] = queues
            except Exception as e:
                print(f"构建队列信息时出错: {e}")

        # 直接返回内存中的任务状态，确保最新数据
        return jsonify(response_data)

    # 如果在内存中找不到任务状态，则从数据库中获取
    history = TestRunHistory.query.filter_by(run_id=run_id).first()
    if history:
        # 解析运行数据
        run_data = {}
        if history.run_data:
            try:
                run_data = json.loads(history.run_data)
            except Exception as e:
                print(f"解析运行数据时发生错误: {e}")

        # 准备响应数据
        response_data = {
            'status': history.status,
            'running': history.status == 'running',
            'message_count': run_data.get('message_count', 0),
            'cycle_count': history.cycle_count or 0,
            'error_count': run_data.get('error_count', 0),
            'latest_messages': run_data.get('latest_messages', {}),
            'cycle_elapsed': run_data.get('cycle_elapsed', 0),
            'cycle_duration': run_data.get('cycle_duration', 300)
        }

        # 添加队列特定的消息计数
        for key, value in run_data.items():
            if key.startswith('queue_') and key.endswith('_count'):
                response_data[key] = value

        # 添加队列信息
        try:
            # 解析任务快照以获取队列信息
            task_snapshot = json.loads(history.task_snapshot) if history.task_snapshot else {}
            if 'queues' in task_snapshot:
                queues = []
                for queue_index, queue in enumerate(task_snapshot.get('queues', [])):
                    # 获取模板ID
                    template_id = queue.get('template_id')

                    # 获取模板信息以获取正确的主题名
                    from app.models.template import MessageTemplate
                    template = MessageTemplate.query.get(template_id)
                    topic = template.topic if template and template.topic else queue.get('topic', 'default-topic')

                    # 从最新消息中获取主题名
                    if 'latest_messages' in run_data and str(queue_index) in run_data['latest_messages']:
                        message_data = run_data['latest_messages'][str(queue_index)]
                        if 'topic' in message_data:
                            topic = message_data['topic']

                    # 获取队列特定的消息计数
                    queue_key_count = f"queue_{queue_index}_count"
                    queue_message_count = run_data.get(queue_key_count, 0)

                    queue_info = {
                        "topic": topic,
                        "template_id": template_id,
                        "message_count": queue_message_count,
                        "average_rate": "-"  # 不计算速率
                    }
                    queues.append(queue_info)

                response_data['queues'] = queues
        except Exception as e:
            print(f"构建历史记录队列信息时出错: {e}")

        # 返回历史记录状态
        return jsonify(response_data)

    # 如果找不到任务，返回错误
    return jsonify({'error': '找不到任务状态'}), 404

@bp.route('/api/check_running/<int:task_id>')
def check_running_task(task_id):
    """检查任务是否有正在运行的实例"""
    app = current_app._get_current_object()

    # 初始化响应数据
    response = {
        'is_running': False,
        'run_info': None
    }

    # 检查是否有正在运行的任务实例
    if hasattr(app, 'running_tasks'):
        for run_id, task_info in app.running_tasks.items():
            if task_info.get('running', False) and task_info.get('task_id') == task_id:
                # 找到运行中的任务实例
                response['is_running'] = True
                response['run_info'] = {
                    'run_id': run_id,
                    'history_id': task_info.get('history_id'),
                    'message_count': task_info.get('message_count', 0),
                    'cycle_count': task_info.get('cycle_count', 0),
                    'start_time': task_info.get('start_time')
                }
                break

    return jsonify(response)

@bp.route('/api/logs/<path:run_id>')
def api_logs(run_id):
    """获取任务运行日志API"""
    app = current_app._get_current_object()

    # 检查全局运行状态字典
    if hasattr(app, 'running_tasks') and run_id in app.running_tasks:
        logs = app.running_tasks[run_id].get('logs', [])
        task_id = app.running_tasks[run_id].get('task_id')
        return jsonify({
            'success': True,
            'logs': logs,
            'task_id': task_id,
            'run_id': run_id
        })

    # 如果任务不在运行中，返回空日志
    return jsonify({
        'success': True,
        'logs': [],
        'task_id': None,
        'run_id': run_id
    })

@bp.route('/api/logs/task/<int:task_id>')
def api_logs_by_task(task_id):
    """根据任务ID获取当前运行的日志"""
    app = current_app._get_current_object()

    # 查找该任务当前运行的实例
    if hasattr(app, 'running_tasks'):
        for run_id, task_info in app.running_tasks.items():
            if task_info.get('running', False) and task_info.get('task_id') == task_id:
                logs = task_info.get('logs', [])
                return jsonify({
                    'success': True,
                    'logs': logs,
                    'task_id': task_id,
                    'run_id': run_id
                })

    # 如果任务没有运行中的实例，返回空日志
    return jsonify({
        'success': True,
        'logs': [],
        'task_id': task_id,
        'run_id': None
    })