from flask import (
    Blueprint, render_template, request, redirect, url_for, jsonify, current_app, abort
)

bp = Blueprint('env', __name__, url_prefix='/env')

@bp.route('/')
def index():
    """环境变量列表页面"""
    return render_template('environment/index.html')

@bp.route('/create')
def create():
    """创建环境变量页面"""
    return render_template('environment/edit.html', env_id=0)

@bp.route('/edit/<int:env_id>')
def edit(env_id):
    """编辑环境变量页面"""
    # 获取环境变量
    env_data = current_app.env_manager.get_environment(env_id)
    if not env_data:
        abort(404)
    return render_template('environment/edit.html', env_id=env_id)

@bp.route('/api/list')
def api_list():
    """获取环境变量列表API"""
    env_list = current_app.env_manager.list_environments()
    return jsonify(env_list)

@bp.route('/api/get/<int:env_id>')
def api_get(env_id):
    """获取环境变量详情API"""
    env_data = current_app.env_manager.get_environment(env_id)
    if not env_data:
        return jsonify({"success": False, "error": "环境变量不存在"}), 404
    return jsonify(env_data)

@bp.route('/api/save', methods=['POST'])
def api_save():
    """保存环境变量API"""
    env_data = request.get_json()
    if not env_data:
        return jsonify({"success": False, "error": "无效的请求数据"}), 400
    
    # 保存环境变量
    success, error, env_id = current_app.env_manager.save_environment(env_data)
    if not success:
        return jsonify({"success": False, "error": error}), 400
    
    return jsonify({"success": True, "id": env_id})

@bp.route('/api/delete/<int:env_id>', methods=['DELETE'])
def api_delete(env_id):
    """删除环境变量API"""
    success, error = current_app.env_manager.delete_environment(env_id)
    if not success:
        return jsonify({"success": False, "error": error}), 400
    
    return jsonify({"success": True})

@bp.route('/api/clone/<int:env_id>', methods=['POST'])
def api_clone(env_id):
    """克隆环境变量API"""
    success, error, new_env_id = current_app.env_manager.clone_environment(env_id)
    if not success:
        return jsonify({"success": False, "error": error}), 400
    
    return jsonify({"success": True, "id": new_env_id}) 