from flask import (
    Blueprint, render_template, current_app, jsonify
)

bp = Blueprint('dashboard', __name__)

@bp.route('/')
def index():
    """仪表盘首页"""
    return render_template('dashboard/index.html')

@bp.route('/api/stats')
def api_stats():
    """获取系统统计数据API"""
    # 获取环境变量数量
    env_count = len(current_app.env_manager.list_environments())
    
    # TODO: 获取模板数量、任务数量和历史记录数量
    template_count = 0  # 需要实现模板管理器
    task_count = 0      # 需要实现任务管理器
    history_count = 0   # 需要实现历史记录管理器
    
    # 返回统计数据
    return jsonify({
        "env_count": env_count,
        "template_count": template_count,
        "task_count": task_count,
        "history_count": history_count
    }) 