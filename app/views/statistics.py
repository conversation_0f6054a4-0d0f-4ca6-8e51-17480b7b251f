from flask import Blueprint, jsonify, request
import json
from datetime import datetime, timedelta

bp = Blueprint('statistics', __name__, url_prefix='/statistics')


@bp.route('/api/latest/<int:task_id>')
def api_get_latest_statistics(task_id):
    """获取指定任务最后一次运行的统计数据API"""
    from app.models.history import TestRunHistory
    from app.models.task import TestTask
    
    try:
        # 获取任务信息
        task = TestTask.query.get(task_id)
        if not task:
            return jsonify({"error": "任务不存在"}), 404
        
        # 获取该任务最后一次运行的历史记录
        latest_history = TestRunHistory.query.filter_by(task_id=task_id).order_by(TestRunHistory.start_time.desc()).first()
        
        if not latest_history:
            return jsonify({
                "task_id": task_id,
                "task_name": task.name,
                "has_data": False,
                "message": "该任务暂无运行历史记录"
            })
        
        # 解析运行数据
        run_data = {}
        if latest_history.run_data:
            try:
                run_data = json.loads(latest_history.run_data)
            except Exception as e:
                print(f"解析运行数据时出错: {e}")
        
        # 计算运行时长
        duration = 0
        if latest_history.start_time:
            end_time = latest_history.end_time or datetime.utcnow()
            duration = int((end_time - latest_history.start_time).total_seconds())
        
        # 获取统计数据
        statistics_data = extract_statistics_from_run_data(run_data, latest_history)
        
        return jsonify({
            "task_id": task_id,
            "task_name": task.name,
            "has_data": True,
            "run_id": latest_history.run_id,
            "start_time": latest_history.start_time.isoformat() if latest_history.start_time else None,
            "end_time": latest_history.end_time.isoformat() if latest_history.end_time else None,
            "status": latest_history.status,
            "duration": duration,
            "cycle_count": latest_history.cycle_count or 0,
            "statistics": statistics_data
        })
        
    except Exception as e:
        print(f"获取统计数据时出错: {e}")
        return jsonify({"error": f"获取统计数据失败: {str(e)}"}), 500


def extract_statistics_from_run_data(run_data, history_record):
    """从运行数据中提取统计信息"""
    from app.views.history import generate_rate_data
    from datetime import datetime

    # 初始化统计数据结构
    stats = {
        "total_messages": 0,
        "error_count": 0,
        "queue_distribution": {},
        "messages_timeline": [],
        "topics": {}
    }

    try:
        # 获取基本统计信息
        stats["total_messages"] = run_data.get("message_count", 0)
        stats["error_count"] = run_data.get("error_count", 0)

        # 获取最新消息信息
        latest_messages = run_data.get("latest_messages", {})

        # 统计队列分布和主题分布
        for queue_key, queue_data in latest_messages.items():
            if isinstance(queue_data, dict):
                topic = queue_data.get("topic", "未知")

                # 获取队列特定的消息计数
                queue_count_key = f"queue_{queue_key}_count"
                message_count = run_data.get(queue_count_key, 1)

                # 队列分布
                queue_name = f"队列{int(queue_key) + 1}" if queue_key.isdigit() else f"队列{queue_key}"
                stats["queue_distribution"][queue_name] = message_count

                # 主题分布
                if topic not in stats["topics"]:
                    stats["topics"][topic] = 0
                stats["topics"][topic] += message_count

        # 生成简化的时间线数据用于图表显示
        if history_record.start_time:
            stats["messages_timeline"] = generate_simple_timeline_data(
                history_record.start_time,
                history_record.end_time,
                stats["total_messages"],
                history_record.cycle_count or 1
            )

        # 如果没有队列分布数据，使用默认数据
        if not stats["queue_distribution"] and stats["total_messages"] > 0:
            stats["queue_distribution"]["默认队列"] = stats["total_messages"]

        # 如果没有主题数据，使用默认数据
        if not stats["topics"] and stats["total_messages"] > 0:
            stats["topics"]["默认主题"] = stats["total_messages"]

    except Exception as e:
        print(f"提取统计数据时出错: {e}")

    return stats


def generate_simple_timeline_data(start_time, end_time, total_messages, cycle_count):
    """生成简化的时间线数据用于图表显示"""
    timeline_data = []

    if not total_messages or total_messages <= 0:
        return timeline_data

    # 如果没有结束时间，使用当前时间
    if not end_time:
        end_time = datetime.utcnow()

    # 计算总运行时长（秒）
    duration_seconds = (end_time - start_time).total_seconds()

    # 如果持续时间异常长（超过1小时），可能是时区问题，使用周期数来估算合理的时间范围
    if duration_seconds > 3600 and cycle_count > 0:
        # 假设每个周期大约2秒（基于常见的消息发送频率）
        estimated_duration = cycle_count * 2
        duration_seconds = min(duration_seconds, estimated_duration)
        end_time = start_time + timedelta(seconds=duration_seconds)

    # 根据运行时长决定数据点数量
    if duration_seconds <= 60:  # 1分钟以内，每10秒一个点
        interval_seconds = 10
    elif duration_seconds <= 300:  # 5分钟以内，每30秒一个点
        interval_seconds = 30
    elif duration_seconds <= 1800:  # 30分钟以内，每2分钟一个点
        interval_seconds = 120
    else:  # 超过30分钟，每5分钟一个点
        interval_seconds = 300

    # 计算数据点数量
    num_points = max(2, min(20, int(duration_seconds / interval_seconds) + 1))

    # 如果有周期数信息，优先使用周期数来生成数据点
    if cycle_count > 0 and cycle_count <= 20:
        # 使用周期数生成数据点
        for i in range(cycle_count + 1):  # +1 包含起始点
            progress = i / cycle_count if cycle_count > 0 else 0
            current_time = start_time + timedelta(seconds=duration_seconds * progress)
            cumulative_messages = int(total_messages * progress)

            timeline_data.append({
                "timestamp": current_time.isoformat(),
                "count": cumulative_messages
            })
    else:
        # 生成均匀分布的时间点
        for i in range(num_points):
            # 计算当前时间点
            progress = i / (num_points - 1) if num_points > 1 else 0
            current_time = start_time + timedelta(seconds=duration_seconds * progress)

            # 计算累积消息数（线性增长）
            cumulative_messages = int(total_messages * progress)

            timeline_data.append({
                "timestamp": current_time.isoformat(),
                "count": cumulative_messages
            })

    return timeline_data


@bp.route('/api/running/<int:task_id>')
def api_get_running_statistics(task_id):
    """获取正在运行任务的实时统计数据API"""
    from app.models.task import TestTask
    from app.views.history import get_running_task_stats

    try:
        # 获取任务信息
        task = TestTask.query.get(task_id)
        if not task:
            return jsonify({"error": "任务不存在"}), 404

        # 获取运行中的任务统计数据
        running_stats = get_running_task_stats(task_id)

        if not running_stats:
            return jsonify({
                "task_id": task_id,
                "task_name": task.name,
                "is_running": False,
                "message": "该任务当前未在运行"
            })

        return jsonify({
            "task_id": task_id,
            "task_name": task.name,
            "is_running": True,
            "statistics": running_stats
        })

    except Exception as e:
        print(f"获取运行中统计数据时出错: {e}")
        return jsonify({"error": f"获取运行中统计数据失败: {str(e)}"}), 500
