from app import db
from datetime import datetime

class TestTask(db.Model):
    """测试任务模型"""
    __tablename__ = 'test_tasks'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.String(256))
    environment_id = db.Column(db.Integer, nullable=False)  # 环境ID，环境变量存储在数据库中
    # 全局测试参数
    cycle_duration = db.Column(db.Integer, default=300)  # 周期时长(秒)
    total_duration = db.Column(db.Integer, default=0)    # 总运行时长(秒)，0表示无限
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联的队列项
    queues = db.relationship('TaskQueue', backref='task', cascade='all, delete-orphan', lazy='dynamic')
    # 环境变量配置集ID，环境变量存储在数据库中
    # 通过DbEnvManager类获取环境变量

    def to_dict(self):
        """转换为字典"""
        from flask import current_app

        # 使用DbEnvManager获取环境名称
        environment_name = '-'
        if hasattr(current_app, 'env_manager') and self.environment_id:
            try:
                env_data = current_app.env_manager.get_environment(self.environment_id)
                if env_data:
                    environment_name = env_data.get('name', '-')
            except Exception as e:
                print(f"获取环境名称时发生错误: {e}")

        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'environment_id': self.environment_id,
            'environment_name': environment_name,  # 添加环境名称
            'cycle_duration': self.cycle_duration,
            'total_duration': self.total_duration,
            'queues': [queue.to_dict() for queue in self.queues.all()],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @staticmethod
    def from_dict(data):
        """从字典创建测试任务"""
        task = TestTask(
            name=data.get('name'),
            description=data.get('description'),
            environment_id=data.get('environment_id'),
            cycle_duration=data.get('cycle_duration', 300),
            total_duration=data.get('total_duration', 0)
        )

        # 注意：队列项需要在任务保存后单独添加，避免循环引用
        return task

    def add_queues_from_dict(self, queues_data):
        """从字典数据添加队列项"""
        for queue_data in queues_data:
            queue = TaskQueue.from_dict(queue_data)
            queue.task_id = self.id
            self.queues.append(queue)


class TaskQueue(db.Model):
    """任务队列项模型"""
    __tablename__ = 'task_queues'

    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('test_tasks.id'), nullable=False)
    template_id = db.Column(db.Integer, db.ForeignKey('message_templates.id'), nullable=False)
    topic = db.Column(db.String(128), nullable=False)
    rate = db.Column(db.Integer, default=1)  # 每秒发送消息数
    enabled = db.Column(db.Boolean, default=True)

    # 关联的消息模板
    template = db.relationship('MessageTemplate')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'template_id': self.template_id,
            'topic': self.topic,
            'rate': self.rate,
            'enabled': self.enabled,
            'template_name': self.template.name if self.template else None
        }

    @staticmethod
    def from_dict(data):
        """从字典创建队列项"""
        return TaskQueue(
            template_id=data.get('template_id'),
            topic=data.get('topic', 'default-topic'),
            rate=data.get('rate', 1),
            enabled=data.get('enabled', True)
        )