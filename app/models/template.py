from app import db
from datetime import datetime

class MessageTemplate(db.Model):
    """消息模板模型"""
    __tablename__ = 'message_templates'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.String(256))
    # Kafka主题
    topic = db.Column(db.String(128), default='default-topic')
    # 存储Python代码字符串
    python_code = db.Column(db.Text, nullable=False)
    # 可选的示例输出，用于展示
    sample_output = db.Column(db.Text)
    # 存储测试环境变量（JSON格式）
    env_vars = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'topic': self.topic,
            'python_code': self.python_code,
            'sample_output': self.sample_output,
            'env_vars': self.env_vars,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @staticmethod
    def from_dict(data):
        """从字典创建模板"""
        return MessageTemplate(
            name=data.get('name'),
            description=data.get('description'),
            topic=data.get('topic', 'default-topic'),
            python_code=data.get('python_code'),
            sample_output=data.get('sample_output'),
            env_vars=data.get('env_vars')
        ) 