from app import db
from datetime import datetime
import json


class Environment(db.Model):
    """环境变量配置模型"""
    __tablename__ = 'environments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联的环境变量
    variables = db.relationship('EnvironmentVariable', backref='environment', cascade='all, delete-orphan', lazy='dynamic')
    
    def to_dict(self):
        """转换为字典"""
        # 获取所有环境变量并转换为字典格式
        variables_dict = {}
        for var in self.variables:
            variables_dict[var.key] = var.value
            
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'variables': variables_dict,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @staticmethod
    def from_dict(data):
        """从字典创建环境变量配置"""
        env = Environment(
            name=data.get('name'),
            description=data.get('description', '')
        )
        
        # 添加环境变量
        variables = data.get('variables', {})
        for key, value in variables.items():
            env.variables.append(EnvironmentVariable(key=key, value=value))
        
        return env


class EnvironmentVariable(db.Model):
    """环境变量模型"""
    __tablename__ = 'environment_variables'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(64), nullable=False)
    value = db.Column(db.String(1024))
    environment_id = db.Column(db.Integer, db.ForeignKey('environments.id'), nullable=False)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'environment_id': self.environment_id
        }
    
    @staticmethod
    def from_dict(data):
        """从字典创建环境变量"""
        return EnvironmentVariable(
            key=data.get('key'),
            value=data.get('value'),
            environment_id=data.get('environment_id')
        )
