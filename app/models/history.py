from app import db
from datetime import datetime
import json

class TestRunHistory(db.Model):
    """测试运行历史模型"""
    __tablename__ = 'test_run_history'
    
    id = db.Column(db.Integer, primary_key=True)
    run_id = db.Column(db.String(36), unique=True, nullable=False)  # UUID
    task_id = db.Column(db.Integer, db.<PERSON>ey('test_tasks.id'), nullable=False)
    environment_id = db.Column(db.Integer, nullable=False)  # 环境ID，实际环境变量存储在文件系统中
    start_time = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)
    status = db.Column(db.String(16), default='running')  # running, completed, failed, stopped
    cycle_count = db.Column(db.Integer, default=0)
    # 存储实际使用的环境变量快照
    environment_snapshot = db.Column(db.Text)  # JSON字符串
    # 存储测试任务配置快照
    task_snapshot = db.Column(db.Text)  # JSON字符串
    # 存储运行数据，包括消息计数、错误计数、最新消息等
    run_data = db.Column(db.Text)  # JSON字符串
    # 存储错误消息
    error_message = db.Column(db.Text)
    
    # 关联的测试任务
    task = db.relationship('TestTask')
    
    def to_dict(self):
        """转换为字典"""
        from flask import current_app
        
        # 使用EnvManager获取环境名称
        environment_name = '-'
        if hasattr(current_app, 'env_manager') and self.environment_id:
            try:
                env_data = current_app.env_manager.get_environment(self.environment_id)
                if env_data:
                    environment_name = env_data.get('name', '-')
            except Exception as e:
                print(f"获取环境名称时发生错误: {e}")
        
        # 解析运行数据
        run_data_dict = {}
        if self.run_data:
            try:
                run_data_dict = json.loads(self.run_data)
            except Exception as e:
                print(f"解析运行数据时发生错误: {e}")
        
        result = {
            'id': self.id,
            'run_id': self.run_id,
            'task_id': self.task_id,
            'task_name': self.task.name if self.task else None,
            'environment_id': self.environment_id,
            'environment_name': environment_name,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'message_count': run_data_dict.get('message_count', 0),
            'error_count': run_data_dict.get('error_count', 0),
            'latest_messages': run_data_dict.get('latest_messages', {}),
            'status': self.status,
            'cycle_count': self.cycle_count,
            'environment_snapshot': json.loads(self.environment_snapshot) if self.environment_snapshot else {},
            'task_snapshot': json.loads(self.task_snapshot) if self.task_snapshot else {}
        }


