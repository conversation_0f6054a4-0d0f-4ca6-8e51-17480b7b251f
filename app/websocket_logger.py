"""
WebSocket日志处理模块
负责将任务运行日志通过WebSocket实时发送到前端
"""

import threading
import queue
import time
from datetime import datetime
from typing import Dict, Any, Optional

# 尝试导入SocketIO
try:
    from app import socketio
    SOCKETIO_AVAILABLE = True
except ImportError:
    SOCKETIO_AVAILABLE = False
    socketio = None


class WebSocketLogger:
    """WebSocket日志处理器"""
    
    def __init__(self):
        self.log_queue = queue.Queue()
        self.active_tasks = {}  # task_id -> room_id 映射
        self.worker_thread = None
        self.running = False
        
    def start(self):
        """启动日志处理线程"""
        if not SOCKETIO_AVAILABLE:
            print("WebSocket不可用，日志将只输出到控制台")
            return
            
        if self.running:
            return
            
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
        print("WebSocket日志处理器已启动")
    
    def stop(self):
        """停止日志处理线程"""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=1)
        print("WebSocket日志处理器已停止")
    
    def register_task(self, task_id: int, run_id: str):
        """注册任务，建立task_id和room_id的映射"""
        room_id = f'task_{task_id}'
        self.active_tasks[run_id] = {
            'task_id': task_id,
            'room_id': room_id,
            'start_time': datetime.now()
        }
        print(f"注册任务到WebSocket日志器: task_id={task_id}, run_id={run_id}, room_id={room_id}")
    
    def unregister_task(self, run_id: str):
        """注销任务"""
        if run_id in self.active_tasks:
            task_info = self.active_tasks.pop(run_id)
            print(f"从WebSocket日志器注销任务: run_id={run_id}, task_id={task_info['task_id']}")
    
    def log(self, run_id: str, level: str, message: str):
        """添加日志到队列"""
        if run_id not in self.active_tasks:
            # 如果任务未注册，只打印到控制台
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{timestamp}] [{level.upper()}] {message}")
            return
        
        log_entry = {
            'run_id': run_id,
            'task_id': self.active_tasks[run_id]['task_id'],
            'room_id': self.active_tasks[run_id]['room_id'],
            'level': level,
            'message': message,
            'timestamp': datetime.now()
        }
        
        # 添加到队列
        try:
            self.log_queue.put_nowait(log_entry)
        except queue.Full:
            print(f"日志队列已满，丢弃日志: {message}")
        
        # 同时打印到控制台
        timestamp_str = log_entry['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp_str}] [{level.upper()}] {message}")
    
    def _worker(self):
        """工作线程，处理日志队列"""
        while self.running:
            try:
                # 从队列获取日志，超时1秒
                log_entry = self.log_queue.get(timeout=1)
                self._send_log(log_entry)
                self.log_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"WebSocket日志处理错误: {e}")
    
    def _send_log(self, log_entry: Dict[str, Any]):
        """发送日志到前端"""
        if not SOCKETIO_AVAILABLE or not socketio:
            return
        
        try:
            # 格式化时间戳
            timestamp_str = log_entry['timestamp'].strftime('%H:%M:%S')
            
            # 构造发送数据
            log_data = {
                'timestamp': timestamp_str,
                'level': log_entry['level'],
                'message': log_entry['message']
            }
            
            # 发送到指定房间
            socketio.emit('task_log', log_data, room=log_entry['room_id'])
            
        except Exception as e:
            print(f"WebSocket日志发送失败: {e}")


# 全局日志处理器实例
websocket_logger = WebSocketLogger()


def init_websocket_logger():
    """初始化WebSocket日志处理器"""
    websocket_logger.start()


def shutdown_websocket_logger():
    """关闭WebSocket日志处理器"""
    websocket_logger.stop()


def register_task_logger(task_id: int, run_id: str):
    """注册任务到日志处理器"""
    websocket_logger.register_task(task_id, run_id)


def unregister_task_logger(run_id: str):
    """从日志处理器注销任务"""
    websocket_logger.unregister_task(run_id)


def log_to_websocket(run_id: str, level: str, message: str):
    """发送日志到WebSocket"""
    websocket_logger.log(run_id, level, message)
