#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WebSocket事件处理器
用于实时任务运行输出和状态更新
"""

import json
import threading
import time
from datetime import datetime
from app import socketio, SOCKETIO_AVAILABLE

if SOCKETIO_AVAILABLE:
    from flask_socketio import emit, join_room, leave_room

# 导入消息生成相关模块
try:
    from app.utils.message_generator import MessageGenerator
    from app.utils.env_manager import EnvManager
    from app.models.template import Template
    MESSAGE_GENERATOR_AVAILABLE = True
except ImportError:
    MESSAGE_GENERATOR_AVAILABLE = False

# 全局变量存储运行中的任务
running_tasks = {}
task_threads = {}

class TaskRunner:
    """任务运行器，负责执行任务并发送实时更新"""
    
    def __init__(self, task_id, task_data, room_id):
        self.task_id = task_id
        self.task_data = task_data
        self.room_id = room_id
        self.is_running = False
        self.start_time = None
        self.message_count = 0
        self.cycle_count = 0
        self.error_count = 0
        
    def start(self):
        """开始运行任务"""
        self.is_running = True
        self.start_time = datetime.now()
        
        # 发送任务开始事件
        self.emit_log('info', f'任务 "{self.task_data.get("name", "未命名任务")}" 开始运行...')
        self.emit_status_update()
        
        # 在新线程中运行任务
        thread = threading.Thread(target=self._run_task)
        thread.daemon = True
        thread.start()
        
        return thread
    
    def stop(self):
        """停止任务"""
        self.is_running = False
        self.emit_log('info', '任务已停止')
        self.emit_status_update()
    
    def _run_task(self):
        """实际的任务运行逻辑"""
        try:
            cycle_duration = self.task_data.get('cycle_duration', 60)
            total_duration = self.task_data.get('total_duration', 0)
            queues = self.task_data.get('queues', [])
            
            if not queues:
                self.emit_log('error', '没有配置队列，任务无法运行')
                self.error_count += 1
                self.emit_status_update()
                return
            
            self.emit_log('info', f'配置了 {len(queues)} 个队列')
            self.emit_log('info', f'周期时长: {cycle_duration}秒, 总运行时长: {total_duration}秒')
            
            start_time = time.time()
            
            while self.is_running:
                # 检查是否超过总运行时长
                if total_duration > 0 and (time.time() - start_time) >= total_duration:
                    self.emit_log('info', f'达到总运行时长 {total_duration}秒，任务结束')
                    break
                
                # 执行一个周期
                self.cycle_count += 1
                self.emit_log('info', f'开始第 {self.cycle_count} 个周期')
                
                # 处理每个队列
                for i, queue in enumerate(queues):
                    if not self.is_running:
                        break
                        
                    try:
                        self._process_queue(queue, i + 1)
                    except Exception as e:
                        self.error_count += 1
                        self.emit_log('error', f'队列 {i + 1} 处理失败: {str(e)}')
                
                self.emit_status_update()
                self.emit_log('info', f'第 {self.cycle_count} 个周期完成')
                
                # 等待下一个周期
                if self.is_running:
                    time.sleep(cycle_duration)
            
        except Exception as e:
            self.error_count += 1
            self.emit_log('error', f'任务运行异常: {str(e)}')
        finally:
            self.is_running = False
            self.emit_log('info', '任务运行结束')
            self.emit_status_update()
    
    def _process_queue(self, queue, queue_num):
        """处理单个队列"""
        template_id = queue.get('template_id')
        topic = queue.get('topic', 'default-topic')
        messages_per_cycle = queue.get('messages_per_cycle', 1)

        self.emit_log('info', f'队列 {queue_num}: 准备发送 {messages_per_cycle} 条消息到 Topic: {topic}')

        # 获取模板和环境变量
        template = None
        env_vars = {}

        if MESSAGE_GENERATOR_AVAILABLE and template_id:
            try:
                template = Template.get_by_id(template_id)
                env_manager = EnvManager()
                env_vars = env_manager.get_env_vars()
            except Exception as e:
                self.emit_log('warning', f'无法获取模板 {template_id} 或环境变量: {str(e)}')

        # 创建Kafka生产者
        kafka_producer = None
        try:
            from app.kafka_utils import KafkaProducer
            kafka_producer = KafkaProducer()
        except Exception as e:
            self.emit_log('warning', f'无法创建Kafka生产者: {str(e)}')

        for msg_idx in range(messages_per_cycle):
            if not self.is_running:
                break

            try:
                # 使用真正的消息生成器生成消息
                if MESSAGE_GENERATOR_AVAILABLE and template:
                    try:
                        # 使用模板的Python代码生成消息
                        from app.utils.message_generator import MessageGenerator
                        result = MessageGenerator.generate_message(template.python_code, env_vars)

                        if result.get('success', False):
                            kafka_message = result.get('result')

                            # 发送到Kafka
                            if kafka_producer:
                                try:
                                    kafka_producer.send(topic, kafka_message)
                                    self.message_count += 1
                                    message_json = json.dumps(kafka_message, ensure_ascii=False, indent=2)
                                    self.emit_log('success', f'队列 {queue_num} 成功发送消息到 Topic {topic}:\n{message_json}')
                                except Exception as kafka_error:
                                    self.error_count += 1
                                    self.emit_log('error', f'队列 {queue_num} Kafka发送失败: {str(kafka_error)}')
                            else:
                                # 没有Kafka连接时，只显示生成的消息
                                self.message_count += 1
                                message_json = json.dumps(kafka_message, ensure_ascii=False, indent=2)
                                self.emit_log('success', f'队列 {queue_num} 生成消息 (未发送到Kafka):\n{message_json}')
                        else:
                            # 消息生成失败
                            error_msg = result.get('error', '未知错误')
                            self.error_count += 1
                            self.emit_log('error', f'队列 {queue_num} 消息生成失败: {error_msg}')

                    except Exception as e:
                        # 如果消息生成失败，使用备用消息
                        self.error_count += 1
                        self.emit_log('error', f'队列 {queue_num} 消息生成异常: {str(e)}')
                        self._send_fallback_message(queue_num, msg_idx, topic, kafka_producer)
                else:
                    # 没有模板时使用备用消息
                    self._send_fallback_message(queue_num, msg_idx, topic, kafka_producer)

            except Exception as e:
                self.error_count += 1
                self.emit_log('error', f'队列 {queue_num} 消息 {msg_idx + 1} 发送异常: {str(e)}')

    def _send_fallback_message(self, queue_num, msg_idx, topic, kafka_producer=None):
        """发送备用消息（当无法使用模板时）"""
        message_data = {
            'id': f'msg-{self.cycle_count}-{queue_num}-{msg_idx + 1}',
            'timestamp': datetime.now().isoformat(),
            'queue': queue_num,
            'cycle': self.cycle_count,
            'data': f'消息数据 - 周期{self.cycle_count}, 队列{queue_num}, 消息{msg_idx + 1}'
        }

        # 尝试发送到Kafka
        if kafka_producer:
            try:
                kafka_producer.send(topic, message_data)
                self.message_count += 1
                message_json = json.dumps(message_data, ensure_ascii=False, indent=2)
                self.emit_log('success', f'队列 {queue_num} 成功发送备用消息到 Topic {topic}:\n{message_json}')
            except Exception as e:
                self.error_count += 1
                self.emit_log('error', f'队列 {queue_num} 备用消息Kafka发送失败: {str(e)}')
        else:
            # 没有Kafka连接时，只显示消息
            self.message_count += 1
            message_json = json.dumps(message_data, ensure_ascii=False, indent=2)
            self.emit_log('success', f'队列 {queue_num} 生成备用消息 (未发送到Kafka):\n{message_json}')
    
    def emit_log(self, level, message):
        """发送日志消息"""
        if not SOCKETIO_AVAILABLE:
            print(f'[{level.upper()}] {message}')
            return
            
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_data = {
            'timestamp': timestamp,
            'level': level,
            'message': message
        }
        socketio.emit('task_log', log_data, room=self.room_id)
    
    def emit_status_update(self):
        """发送状态更新"""
        if not SOCKETIO_AVAILABLE:
            return
            
        elapsed_time = 0
        if self.start_time:
            elapsed_time = int((datetime.now() - self.start_time).total_seconds())
        
        hours = elapsed_time // 3600
        minutes = (elapsed_time % 3600) // 60
        seconds = elapsed_time % 60
        elapsed_str = f'{hours:02d}:{minutes:02d}:{seconds:02d}'
        
        status_data = {
            'task_id': self.task_id,
            'status': 'running' if self.is_running else 'stopped',
            'message_count': self.message_count,
            'cycle_count': self.cycle_count,
            'error_count': self.error_count,
            'elapsed_time': elapsed_str
        }
        socketio.emit('task_status', status_data, room=self.room_id)

# WebSocket事件处理器
if SOCKETIO_AVAILABLE:
    @socketio.on('connect')
    def handle_connect():
        print('客户端已连接')
        emit('connected', {'message': '连接成功'})
    
    @socketio.on('disconnect')
    def handle_disconnect():
        print('客户端已断开连接')
    
    @socketio.on('join_task_room')
    def handle_join_task_room(data):
        """加入任务房间以接收实时更新"""
        task_id = data.get('task_id')
        if task_id:
            room_id = f'task_{task_id}'
            join_room(room_id)
            emit('joined_room', {'room': room_id, 'task_id': task_id})
            print(f'客户端加入任务房间: {room_id}')
    
    @socketio.on('leave_task_room')
    def handle_leave_task_room(data):
        """离开任务房间"""
        task_id = data.get('task_id')
        if task_id:
            room_id = f'task_{task_id}'
            leave_room(room_id)
            emit('left_room', {'room': room_id, 'task_id': task_id})
            print(f'客户端离开任务房间: {room_id}')
    
    @socketio.on('start_task')
    def handle_start_task(data):
        """开始运行任务"""
        task_id = data.get('task_id')
        task_data = data.get('task_data', {})
        
        if not task_id:
            emit('error', {'message': '缺少任务ID'})
            return
        
        room_id = f'task_{task_id}'
        
        # 如果任务已在运行，先停止
        if task_id in running_tasks:
            running_tasks[task_id].stop()
            if task_id in task_threads:
                task_threads[task_id].join(timeout=1)
        
        # 创建新的任务运行器
        runner = TaskRunner(task_id, task_data, room_id)
        running_tasks[task_id] = runner
        
        # 开始运行任务
        thread = runner.start()
        task_threads[task_id] = thread
        
        emit('task_started', {'task_id': task_id, 'message': '任务已开始'})
        print(f'任务 {task_id} 开始运行')
    
    @socketio.on('stop_task')
    def handle_stop_task(data):
        """停止运行任务"""
        task_id = data.get('task_id')
        
        if not task_id:
            emit('error', {'message': '缺少任务ID'})
            return
        
        if task_id in running_tasks:
            running_tasks[task_id].stop()
            del running_tasks[task_id]
            
            if task_id in task_threads:
                task_threads[task_id].join(timeout=1)
                del task_threads[task_id]
            
            emit('task_stopped', {'task_id': task_id, 'message': '任务已停止'})
            print(f'任务 {task_id} 已停止')
        else:
            emit('error', {'message': '任务未在运行'})

def get_running_tasks():
    """获取当前运行中的任务列表"""
    result = []
    for task_id, runner in running_tasks.items():
        if runner.is_running:
            elapsed_time = 0
            if runner.start_time:
                elapsed_time = int((datetime.now() - runner.start_time).total_seconds())
            
            result.append({
                'task_id': task_id,
                'name': runner.task_data.get('name', '未命名任务'),
                'message_count': runner.message_count,
                'cycle_count': runner.cycle_count,
                'error_count': runner.error_count,
                'elapsed_time': elapsed_time
            })
    
    return result

def stop_all_tasks():
    """停止所有运行中的任务"""
    for task_id in list(running_tasks.keys()):
        if task_id in running_tasks:
            running_tasks[task_id].stop()
            del running_tasks[task_id]
        
        if task_id in task_threads:
            task_threads[task_id].join(timeout=1)
            del task_threads[task_id]
