#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Kafka工具模块，提供简化的Kafka生产者接口
"""

from app.utils.kafka_client import KafkaClient

class KafkaProducer:
    """
    Kafka生产者包装类，简化消息发送接口
    """
    
    def __init__(self, bootstrap_servers: str = None):
        """
        初始化Kafka生产者
        
        Args:
            bootstrap_servers: Kafka服务器地址
                               如果为None，将从环境变量KAFKA_SERVERS中获取
        """
        import logging
        self.logger = logging.getLogger(__name__)
        self.client = KafkaClient(bootstrap_servers=bootstrap_servers)
        self.connected = self.client.connect()
        if not self.connected:
            self.logger.warning("无法连接到Kafka，消息将仅记录到日志而不会实际发送")
    
    def send(self, topic: str, message: str):
        """
        发送消息到指定主题
        
        Args:
            topic: 目标主题
            message: 消息内容（JSON字符串）
        
        Returns:
            bool: 发送是否成功
        """
        # 如果没有连接，只记录消息而不实际发送
        if not self.connected:
            self.logger.info(f"模拟发送消息到主题 {topic}: {message}")
            return True
            
        try:
            # 如果没有send_message方法，尝试直接使用producer
            if hasattr(self.client, 'send_message'):
                self.client.send_message(topic, message)
            elif hasattr(self.client, 'producer') and self.client.producer:
                self.client.producer.send(topic, message)
            else:
                self.logger.error("无法发送消息：KafkaClient没有可用的发送方法")
                return False
            return True
        except Exception as e:
            self.logger.error(f"发送消息失败: {str(e)}")
            # 如果发送失败，仍然记录消息
            self.logger.info(f"消息内容 (发送失败): 主题={topic}, 消息={message}")
            return False
    
    def close(self):
        """
        关闭Kafka连接
        """
        if self.connected:
            self.client.close()
