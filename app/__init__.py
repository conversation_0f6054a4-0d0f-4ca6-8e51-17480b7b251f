from flask import Flask, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import os
from app.utils.db_env_manager import DbEnvManager

# 创建SQLite数据库实例
db = SQLAlchemy()

# 尝试导入SocketIO，如果没有安装则跳过
try:
    from flask_socketio import SocketIO
    socketio = SocketIO()
    SOCKETIO_AVAILABLE = True
except ImportError:
    socketio = None
    SOCKETIO_AVAILABLE = False
    print("Warning: flask-socketio not installed, WebSocket features will be disabled")

def create_app():
    """创建并配置Flask应用"""
    app = Flask(__name__)

    # 启用CORS支持前后端分离
    CORS(app)

    # 配置应用
    app.config.from_mapping(
        SECRET_KEY='dev',
        DATA_DIR='data',
    )

    # 初始化数据库环境变量管理器
    env_manager = DbEnvManager()

    # 将环境变量管理器设置为全局变量
    app.env_manager = env_manager

    # 配置数据库
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(os.path.abspath(os.path.dirname(__file__)), 'data.sqlite')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # 初始化数据库
    db.init_app(app)

    # 创建数据库表并初始化默认数据
    with app.app_context():
        db.create_all()
        print("数据库表创建成功")

        # 初始化默认环境变量配置
        env_manager.initialize_defaults()

    # 初始化SocketIO
    if SOCKETIO_AVAILABLE:
        socketio.init_app(app, cors_allowed_origins="*")

    # 静态文件服务 - 为前端提供服务
    @app.route('/')
    def index():
        try:
            return send_from_directory('../frontend', 'index.html')
        except Exception as e:
            print(f"前端文件加载失败: {e}")
            # 如果前端文件不存在，返回原来的仪表盘
            from app.views.dashboard import bp as dashboard_bp
            return dashboard_bp.index()

    @app.route('/css/<path:filename>')
    def frontend_css(filename):
        return send_from_directory('../frontend/css', filename)

    @app.route('/js/<path:filename>')
    def frontend_js(filename):
        return send_from_directory('../frontend/js', filename)

    # 新版本前端路由
    @app.route('/v2')
    def index_v2():
        try:
            return send_from_directory('../frontend-v2', 'index.html')
        except Exception as e:
            print(f"新版前端文件加载失败: {e}")
            return f"新版前端暂不可用: {e}", 404

    @app.route('/v2/css/<path:filename>')
    def frontend_v2_css(filename):
        return send_from_directory('../frontend-v2/css', filename)

    @app.route('/v2/js/<path:filename>')
    def frontend_v2_js(filename):
        return send_from_directory('../frontend-v2/js', filename)

    # 版本对比页面
    @app.route('/compare')
    def compare_versions():
        return '''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>版本对比 - KafkaTool</title>
            <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100">
            <div class="container mx-auto px-4 py-8">
                <h1 class="text-3xl font-bold text-center mb-8">KafkaTool 版本对比</h1>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold mb-4 text-blue-600">原版本 (v1)</h2>
                        <p class="text-gray-600 mb-4">传统的HTML/CSS/JS实现</p>
                        <a href="/" class="inline-block bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 transition-colors">
                            查看原版本
                        </a>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold mb-4 text-green-600">新版本 (v2)</h2>
                        <p class="text-gray-600 mb-4">使用 Tailwind CSS + Alpine.js 重构</p>
                        <a href="/v2" class="inline-block bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600 transition-colors">
                            查看新版本
                        </a>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''

    # 注册API蓝图
    from app.views import env, template, task, history, dashboard, statistics
    app.register_blueprint(env.bp)
    app.register_blueprint(template.bp)
    app.register_blueprint(task.bp)
    app.register_blueprint(history.bp)
    app.register_blueprint(dashboard.bp)
    app.register_blueprint(statistics.bp)

    # 导入WebSocket事件处理器
    if SOCKETIO_AVAILABLE:
        from app import websocket_events
        # 初始化WebSocket日志处理器
        from app.websocket_logger import init_websocket_logger
        init_websocket_logger()

    # 处理其他路径 - 用于前端路由
    @app.route('/<path:path>')
    def static_files(path):
        # 如果是API路径，让蓝图处理
        if path.startswith('api/') or path.startswith('env/') or path.startswith('template/') or path.startswith('task/') or path.startswith('history/') or path.startswith('statistics/'):
            from flask import abort
            abort(404)

        try:
            return send_from_directory('../frontend', path)
        except:
            # 如果文件不存在，返回index.html用于前端路由
            try:
                return send_from_directory('../frontend', 'index.html')
            except:
                # 如果前端完全不存在，返回404
                from flask import abort
                abort(404)

    return app