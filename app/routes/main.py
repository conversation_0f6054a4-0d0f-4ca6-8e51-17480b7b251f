from flask import Blueprint, render_template, jsonify
from app.models import Environment, MessageTemplate, TestTask, TestRunHistory

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """首页"""
    return render_template('index.html')

@main_bp.route('/api/dashboard')
def dashboard():
    """仪表盘数据"""
    # 获取统计数据
    env_count = Environment.query.count()
    template_count = MessageTemplate.query.count()
    task_count = TestTask.query.count()
    history_count = TestRunHistory.query.count()
    
    # 获取最近的测试运行
    recent_runs = TestRunHistory.query.order_by(TestRunHistory.start_time.desc()).limit(5).all()
    recent_runs_data = [run.to_dict() for run in recent_runs]
    
    return jsonify({
        'env_count': env_count,
        'template_count': template_count,
        'task_count': task_count,
        'history_count': history_count,
        'recent_runs': recent_runs_data
    }) 