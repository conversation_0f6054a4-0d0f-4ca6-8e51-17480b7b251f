from flask import Blueprint, request, jsonify, render_template
from app import db
from app.models import MessageTemplate
from app.utils import MessageGenerator, CodeExecutor
import json

template_bp = Blueprint('template', __name__)

@template_bp.route('/')
def index():
    """消息模板列表页面"""
    return render_template('template/index.html')

@template_bp.route('/edit/<int:template_id>')
def edit(template_id):
    """消息模板编辑页面"""
    return render_template('template/edit.html', template_id=template_id)

@template_bp.route('/create')
def create():
    """消息模板创建页面"""
    return render_template('template/edit.html', template_id=0)

@template_bp.route('/api/list')
def list_templates():
    """获取消息模板列表"""
    templates = MessageTemplate.query.all()
    return jsonify([t.to_dict() for t in templates])

@template_bp.route('/api/get/<int:template_id>')
def get_template(template_id):
    """获取消息模板"""
    template = MessageTemplate.query.get_or_404(template_id)
    return jsonify(template.to_dict())

@template_bp.route('/api/save', methods=['POST'])
def save_template():
    """保存消息模板"""
    data = request.json
    
    if 'id' in data and data['id']:
        # 更新现有模板
        template = MessageTemplate.query.get_or_404(data['id'])
        template.name = data['name']
        template.description = data.get('description', '')
        template.python_code = data.get('python_code', '')
        template.sample_output = data.get('sample_output', '')
    else:
        # 创建新模板
        template = MessageTemplate.from_dict(data)
        db.session.add(template)
    
    db.session.commit()
    return jsonify({'success': True, 'id': template.id})

@template_bp.route('/api/delete/<int:template_id>', methods=['DELETE'])
def delete_template(template_id):
    """删除消息模板"""
    template = MessageTemplate.query.get_or_404(template_id)
    db.session.delete(template)
    db.session.commit()
    return jsonify({'success': True})

@template_bp.route('/api/preview', methods=['POST'])
def preview_template():
    """预览消息模板渲染结果"""
    data = request.json
    python_code = data.get('python_code', '')
    env_vars = data.get('env_vars', {})
    
    # 使用代码执行器执行Python代码
    result = CodeExecutor.execute_code(python_code, env_vars)
    
    return jsonify(result)

@template_bp.route('/api/example/<template_type>')
def get_example_template(template_type):
    """获取示例模板"""
    example_code = MessageGenerator.get_example_code(template_type)
    return jsonify({
        'python_code': example_code
    })

@template_bp.route('/api/import', methods=['POST'])
def import_template():
    """导入消息模板"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': '未提供文件'}), 400
    
    file = request.files['file']
    if not file.filename:
        return jsonify({'success': False, 'error': '未提供文件名'}), 400
    
    try:
        data = json.loads(file.read().decode('utf-8'))
        template = MessageTemplate.from_dict(data)
        db.session.add(template)
        db.session.commit()
        return jsonify({'success': True, 'id': template.id})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@template_bp.route('/api/export/<int:template_id>')
def export_template(template_id):
    """导出消息模板"""
    template = MessageTemplate.query.get_or_404(template_id)
    data = template.to_dict()
    return jsonify(data) 