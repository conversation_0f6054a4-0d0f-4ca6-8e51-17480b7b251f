from flask import Blueprint, request, jsonify, render_template
from app import db
from app.models import TestTask, TaskQueue, MessageTemplate, Environment
import json
import uuid
import os

task_bp = Blueprint('task', __name__)

@task_bp.route('/')
def index():
    """测试任务列表页面"""
    return render_template('task/index.html')

@task_bp.route('/edit/<int:task_id>')
def edit(task_id):
    """测试任务编辑页面"""
    return render_template('task/edit.html', task_id=task_id)

@task_bp.route('/create')
def create():
    """测试任务创建页面"""
    return render_template('task/edit.html', task_id=0)

@task_bp.route('/run/<int:task_id>')
def run(task_id):
    """测试任务运行页面"""
    return render_template('task/run.html', task_id=task_id)

@task_bp.route('/api/list')
def list_tasks():
    """获取测试任务列表"""
    tasks = TestTask.query.all()
    return jsonify([t.to_dict() for t in tasks])

@task_bp.route('/api/get/<int:task_id>')
def get_task(task_id):
    """获取测试任务"""
    task = TestTask.query.get_or_404(task_id)
    return jsonify(task.to_dict())

@task_bp.route('/api/save', methods=['POST'])
def save_task():
    """保存测试任务"""
    data = request.json
    
    if 'id' in data and data['id']:
        # 更新现有任务
        task = TestTask.query.get_or_404(data['id'])
        task.name = data['name']
        task.description = data.get('description', '')
        task.environment_id = data['environment_id']
        task.cycle_duration = data.get('cycle_duration', 300)
        task.total_duration = data.get('total_duration', 0)
        
        # 删除现有队列项
        for queue in task.queues:
            db.session.delete(queue)
        
        # 添加新队列项
        queues_data = data.get('queues', [])
        for queue_data in queues_data:
            queue = TaskQueue(
                template_id=queue_data['template_id'],
                topic=queue_data['topic'],
                rate=queue_data.get('rate', 1),
                enabled=queue_data.get('enabled', True)
            )
            task.queues.append(queue)
    else:
        # 创建新任务
        task = TestTask.from_dict(data)
        db.session.add(task)
    
    db.session.commit()
    return jsonify({'success': True, 'id': task.id})

@task_bp.route('/api/delete/<int:task_id>', methods=['DELETE'])
def delete_task(task_id):
    """删除测试任务"""
    task = TestTask.query.get_or_404(task_id)
    db.session.delete(task)
    db.session.commit()
    return jsonify({'success': True})

@task_bp.route('/api/templates')
def list_available_templates():
    """获取可用的消息模板列表"""
    templates = MessageTemplate.query.all()
    return jsonify([{
        'id': t.id,
        'name': t.name,
        'description': t.description
    } for t in templates])

@task_bp.route('/api/environments')
def list_available_environments():
    """获取可用的环境变量配置列表"""
    environments = Environment.query.all()
    return jsonify([{
        'id': e.id,
        'name': e.name,
        'description': e.description
    } for e in environments])

@task_bp.route('/api/import', methods=['POST'])
def import_task():
    """导入测试任务"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': '未提供文件'}), 400
    
    file = request.files['file']
    if not file.filename:
        return jsonify({'success': False, 'error': '未提供文件名'}), 400
    
    try:
        data = json.loads(file.read().decode('utf-8'))
        task = TestTask.from_dict(data)
        db.session.add(task)
        db.session.commit()
        return jsonify({'success': True, 'id': task.id})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@task_bp.route('/api/export/<int:task_id>')
def export_task(task_id):
    """导出测试任务"""
    task = TestTask.query.get_or_404(task_id)
    data = task.to_dict()
    return jsonify(data)

@task_bp.route('/api/run/<int:task_id>', methods=['POST'])
def start_task(task_id):
    """启动测试任务"""
    from app.utils.kafka_client import KafkaClient
    
    # 获取任务配置
    task = TestTask.query.get_or_404(task_id)
    task_config = task.to_dict()
    
    # 获取环境变量
    environment = Environment.query.get_or_404(task.environment_id)
    env_vars = {var.key: var.value for var in environment.variables}
    
    # 获取消息模板
    template_ids = [queue.template_id for queue in task.queues]
    templates = MessageTemplate.query.filter(MessageTemplate.id.in_(template_ids)).all()
    templates_data = [t.to_dict() for t in templates]
    
    # 获取Kafka服务器配置
    kafka_servers = env_vars.get('KAFKA_SERVERS', 'localhost:9092')
    
    # 设置环境变量
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # 生成运行ID
    run_id = str(uuid.uuid4())
    
    # 创建测试运行历史记录
    from app.models import TestRunHistory
    import json
    from datetime import datetime
    
    history = TestRunHistory(
        run_id=run_id,
        task_id=task.id,
        environment_id=task.environment_id,
        start_time=datetime.utcnow(),
        status='running',
        environment_snapshot=json.dumps(env_vars),
        task_snapshot=json.dumps(task_config)
    )
    db.session.add(history)
    db.session.commit()
    
    # 启动测试
    kafka_client = KafkaClient()
    success, error = kafka_client.start_test(
        run_id=run_id,
        task_config=task_config,
        environment_vars=env_vars,
        message_templates=templates_data,
        kafka_servers=kafka_servers
    )
    
    if not success:
        # 更新历史记录状态
        history.status = 'failed'
        history.end_time = datetime.utcnow()
        db.session.commit()
        return jsonify({'success': False, 'error': error}), 400
    
    return jsonify({
        'success': True,
        'run_id': run_id,
        'history_id': history.id
    })

@task_bp.route('/api/stop/<run_id>', methods=['POST'])
def stop_task(run_id):
    """停止测试任务"""
    from app.utils.kafka_client import KafkaClient
    from app.models import TestRunHistory
    from datetime import datetime
    
    # 查找测试运行历史记录
    history = TestRunHistory.query.filter_by(run_id=run_id).first()
    if not history:
        return jsonify({'success': False, 'error': '未找到测试运行记录'}), 404
    
    # 停止测试
    kafka_client = KafkaClient()
    success, error = kafka_client.stop_test(run_id)
    
    # 更新历史记录状态
    history.status = 'stopped'
    history.end_time = datetime.utcnow()
    db.session.commit()
    
    return jsonify({'success': success, 'error': error})

@task_bp.route('/api/status/<run_id>')
def get_task_status(run_id):
    """获取测试任务状态"""
    from app.utils.kafka_client import KafkaClient
    
    # 获取测试状态
    kafka_client = KafkaClient()
    status = kafka_client.get_test_status(run_id)
    
    # 打印周期进度信息
    print(f"[get_task_status] 返回状态中的周期已过时间: {status.get('cycle_elapsed', 0)}秒")
    
    return jsonify(status) 