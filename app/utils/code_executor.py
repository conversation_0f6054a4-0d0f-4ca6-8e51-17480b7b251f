import json
import uuid
import time
import random
import string
import datetime
import ipaddress
from typing import Dict, Any, Optional
import traceback
import re

class CodeExecutor:
    """Python代码执行器，用于安全地执行用户提供的Python代码"""
    
    @staticmethod
    def _get_safe_globals():
        """获取安全的全局变量字典"""
        # 提供一组安全的内置函数和模块
        safe_builtins = {
            'int': int,
            'float': float,
            'str': str,
            'list': list,
            'dict': dict,
            'tuple': tuple,
            'set': set,
            'bool': bool,
            'len': len,
            'range': range,
            'enumerate': enumerate,
            'zip': zip,
            'min': min,
            'max': max,
            'sum': sum,
            'abs': abs,
            'round': round,
            'sorted': sorted,
            'reversed': reversed,
            'all': all,
            'any': any,
            'map': map,
            'filter': filter,
            'print': print,  # 允许print用于调试
        }
        
        # 添加一些常用的生成函数
        safe_globals = {
            # 基本类型
            **safe_builtins,
            
            # 时间相关
            'time': time.time,
            'datetime': datetime.datetime,
            'date': datetime.date,
            'timedelta': datetime.timedelta,
            
            # UUID生成
            'uuid4': uuid.uuid4,
            
            # 随机数相关
            'random_int': random.randint,
            'random_float': random.uniform,
            'random_choice': random.choice,
            'random_sample': random.sample,
            'random_shuffle': lambda x: random.sample(x, len(x)),
            'random_bool': lambda: random.choice([True, False]),
            
            # 字符串相关
            'random_string': lambda length, chars=string.ascii_letters + string.digits: 
                ''.join(random.choice(chars) for _ in range(length)),
            'random_numeric': lambda length: 
                ''.join(random.choice(string.digits) for _ in range(length)),
            'random_alpha': lambda length: 
                ''.join(random.choice(string.ascii_letters) for _ in range(length)),
            'random_hex': lambda length: 
                ''.join(random.choice(string.hexdigits.lower()) for _ in range(length)),
            
            # IP地址相关
            'random_ipv4': lambda: str(ipaddress.IPv4Address(random.randint(1, 2**32-1))),
            'random_ipv4_private': lambda: str(random.choice([
                ipaddress.IPv4Address(random.randint(int(ipaddress.IPv4Address('10.0.0.0')), 
                                                    int(ipaddress.IPv4Address('**************')))),
                ipaddress.IPv4Address(random.randint(int(ipaddress.IPv4Address('**********')), 
                                                    int(ipaddress.IPv4Address('**************')))),
                ipaddress.IPv4Address(random.randint(int(ipaddress.IPv4Address('***********')), 
                                                    int(ipaddress.IPv4Address('***************'))))
            ])),
            
            # MAC地址相关
            'random_mac': lambda: ':'.join(format(random.randint(0, 255), '02x') for _ in range(6)),
            'random_mac_unicast': lambda: ':'.join([
                format(random.randint(0, 255) & 0xfe, '02x'),  # 确保第一个字节的第二位是0(全局唯一地址)
                *[format(random.randint(0, 255), '02x') for _ in range(5)]
            ]),
            
            # JSON相关
            'json_dumps': json.dumps,
            'json_loads': json.loads,
        }
        
        return safe_globals
    
    @staticmethod
    def execute_code(code: str, env_vars: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行Python代码并返回结果
        
        :param code: Python代码字符串
        :param env_vars: 环境变量字典，将作为上下文传递给代码
        :return: 包含执行结果和状态的字典
        """
        if env_vars is None:
            env_vars = {}
        
        # 创建安全的全局变量字典
        safe_globals = CodeExecutor._get_safe_globals()
        
        # 添加环境变量
        local_vars = {**env_vars}
        
        # 检查代码是否有潜在的危险操作
        if CodeExecutor._has_dangerous_code(code):
            return {
                'success': False,
                'error': '代码包含潜在的危险操作',
                'result': None
            }
        
        try:
            # 将代码包装在函数中，以便可以返回结果
            wrapped_code = f"""
def _message_generator_func(env_vars):
{CodeExecutor._indent_code(code)}
    
# 执行生成函数并返回结果
_result = _message_generator_func(env_vars)
"""
            
            # 执行代码
            exec_globals = {**safe_globals, 'env_vars': local_vars}
            exec(wrapped_code, exec_globals)
            
            # 获取结果
            result = exec_globals.get('_result')
            
            # 确保结果是可序列化的
            try:
                json.dumps(result)
                return {
                    'success': True,
                    'error': None,
                    'result': result
                }
            except (TypeError, OverflowError):
                return {
                    'success': False,
                    'error': '结果无法序列化为JSON',
                    'result': str(result)
                }
                
        except Exception as e:
            error_info = traceback.format_exc()
            return {
                'success': False,
                'error': f"执行代码时出错: {str(e)}",
                'traceback': error_info,
                'result': None
            }
    
    @staticmethod
    def _indent_code(code: str, spaces: int = 4) -> str:
        """
        缩进代码
        
        :param code: 原始代码
        :param spaces: 缩进空格数
        :return: 缩进后的代码
        """
        indent = ' ' * spaces
        return '\n'.join(indent + line for line in code.splitlines())
    
    @staticmethod
    def _has_dangerous_code(code: str) -> bool:
        """
        检查代码是否包含潜在的危险操作
        
        :param code: Python代码字符串
        :return: 是否包含危险操作
        """
        # 定义危险模式
        dangerous_patterns = [
            # 导入语句
            r'^\s*import\s+',
            r'^\s*from\s+\S+\s+import',
            # 系统调用
            r'(os|subprocess|sys|shutil)\.',
            # 文件操作
            r'(open|file)\s*\(',
            r'(read|write)File',
            # 网络操作
            r'(socket|urllib|http|requests)\.',
            # 代码执行
            r'(eval|exec|compile)\s*\(',
            # 属性访问和修改
            r'(__\w+__)',
            r'(globals|locals|vars)\s*\(',
            # 类定义和继承
            r'^\s*class\s+',
            # 其他可能危险的操作
            r'(setattr|getattr|delattr)\s*\(',
        ]
        
        # 检查每一行
        for line in code.splitlines():
            for pattern in dangerous_patterns:
                if re.search(pattern, line):
                    return True
        
        return False 