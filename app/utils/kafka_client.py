#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import ssl
import time
import logging
from typing import Dict, Any, Optional, List, Tuple
from kafka import KafkaProducer
import threading
import uuid
import queue
from kafka.errors import KafkaError

class KafkaClient:
    """Kafka客户端，用于管理Kafka连接和消息发送"""
    
    def __init__(self, bootstrap_servers: str = None):
        """
        初始化Kafka客户端
        
        :param bootstrap_servers: Kafka服务器地址，格式为 "host1:port1,host2:port2"
                                如果为None，将从环境变量KAFKA_SERVERS中获取
        """
        # 从环境变量中获取Kafka服务器地址
        if bootstrap_servers is None:
            bootstrap_servers = os.environ.get('KAFKA_SERVERS', 'localhost:9092')
            
        self.bootstrap_servers = bootstrap_servers
        self.producer = None
        self.ssl_dir = "ssl"
        self.ssl_files = {
            "cafile": os.path.join(self.ssl_dir, "CARoot.pem"),
            "certfile": os.path.join(self.ssl_dir, "certificate.pem"),
            "keyfile": os.path.join(self.ssl_dir, "key.pem")
        }
        self.active_tests = {}  # 存储活动的测试任务 {run_id: TestRunner}
        self.logger = logging.getLogger(__name__)
    
    def check_ssl_files(self) -> bool:
        """
        检查SSL证书文件是否存在
        
        :return: 所有文件是否都存在
        """
        all_files_exist = True
        for key, path in self.ssl_files.items():
            if not os.path.exists(path):
                all_files_exist = False
                break
        return all_files_exist
    
    def connect(self) -> bool:
        """
        连接到Kafka服务器
        
        :return: 连接是否成功
        """
        try:
            # 从环境变量中获取是否使用SSL
            use_ssl_str = os.environ.get('KAFKA_USE_SSL', 'false').lower()
            use_ssl = use_ssl_str == 'true'
            
            # 基本配置
            config = {
                'bootstrap_servers': self.bootstrap_servers,
                'value_serializer': lambda m: json.dumps(m).encode('utf-8'),
                'acks': 'all',  # 等待所有副本确认
                'retries': 3,    # 重试次数
                'request_timeout_ms': 10000  # 请求超时时间
            }
            
            # 如果配置为使用SSL，检查证书文件并配置SSL
            if use_ssl:
                # 检查SSL证书文件是否存在
                ssl_files_exist = self.check_ssl_files()
                if not ssl_files_exist:
                    self.logger.error("配置为使用SSL但证书文件不存在")
                    return False
                    
                self.logger.info("使用SSL连接Kafka")
                ssl_config = {
                    'security_protocol': 'SSL',
                    'ssl_cafile': self.ssl_files['cafile'],
                    'ssl_certfile': self.ssl_files['certfile'],
                    'ssl_keyfile': self.ssl_files['keyfile'],
                    'ssl_check_hostname': False,  # 禁用主机名验证，解决IP地址不匹配问题
                }
                config.update(ssl_config)
            else:
                self.logger.info("使用非SSL连接Kafka")
            
            # 创建生产者
            self.producer = KafkaProducer(**config)
            self.logger.info(f"成功连接到Kafka: {self.bootstrap_servers}")
            return True
        except KafkaError as e:
            self.logger.error(f"连接Kafka失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"连接Kafka时发生异常: {e}")
            return False
    
    def disconnect(self):
        """断开与Kafka的连接"""
        if self.producer:
            self.producer.close()
            self.producer = None
    
    def is_connected(self) -> bool:
        """
        检查是否已连接到Kafka
        
        :return: 是否已连接
        """
        return self.producer is not None
    
    def send_message(self, topic: str, message: Dict[str, Any], key: Optional[str] = None) -> bool:
        """
        发送单条消息到Kafka
        
        :param topic: 主题
        :param message: 消息内容
        :param key: 消息键，可选
        :return: 发送是否成功
        """
        if not self.is_connected():
            if not self.connect():
                return False
        
        try:
            # 发送消息
            key_bytes = key.encode('utf-8') if key else None
            future = self.producer.send(topic, message, key=key_bytes)
            
            # 等待消息发送完成
            record_metadata = future.get(timeout=10)
            
            self.logger.debug(f"消息已发送到 {record_metadata.topic}, 分区: {record_metadata.partition}, 偏移量: {record_metadata.offset}")
            return True
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def send_messages(self, topic: str, messages: List[Dict[str, Any]], keys: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        批量发送消息到Kafka
        
        :param topic: 主题
        :param messages: 消息列表
        :param keys: 消息键列表，可选
        :return: 发送结果统计
        """
        if not self.is_connected():
            if not self.connect():
                return {"success": False, "sent": 0, "total": len(messages), "error": "Kafka连接失败"}
        
        start_time = time.time()
        success_count = 0
        error_count = 0
        
        try:
            # 批量发送消息
            for i, message in enumerate(messages):
                key_bytes = None
                if keys and i < len(keys):
                    key_bytes = keys[i].encode('utf-8')
                
                try:
                    future = self.producer.send(topic, message, key=key_bytes)
                    # 不等待单个消息完成，提高性能
                    success_count += 1
                except Exception as e:
                    self.logger.error(f"发送消息失败: {e}, 消息内容: {message}")
                    error_count += 1
            
            # 等待所有消息发送完成
            self.producer.flush()
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                "success": True,
                "sent": success_count,
                "errors": error_count,
                "total": len(messages),
                "time": duration,
                "rate": success_count / duration if duration > 0 else 0
            }
            
            self.logger.info(f"批量发送完成: 成功 {success_count}, 失败 {error_count}, 用时 {duration:.2f}s, 速率 {result['rate']:.2f} msg/s")
            return result
        except Exception as e:
            self.logger.error(f"批量发送失败: {e}")
            return {
                "success": False,
                "sent": success_count,
                "errors": error_count,
                "total": len(messages),
                "error": str(e)
            }
    
    def check_topic_exists(self, topic: str) -> bool:
        """
        检查主题是否存在
        
        :param topic: 主题名称
        :return: 主题是否存在
        """
        # 此方法仅用于测试，实际上Kafka会自动创建不存在的主题
        # 要实现真正的检查，需要使用Kafka Admin API
        try:
            if not self.is_connected():
                if not self.connect():
                    return False
            
            # 尝试发送一个空消息，如果成功则表示主题存在或可以创建
            future = self.producer.send(topic, {"_test": True})
            future.get(timeout=5)
            return True
        except Exception as e:
            self.logger.error(f"检查主题失败: {e}")
            return False
    
    def start_test(self, run_id: str, task_config: Dict[str, Any], environment_vars: Dict[str, Any], 
                   message_templates: List[Dict[str, Any]], kafka_servers: str) -> Tuple[bool, Optional[str]]:
        """
        启动测试任务
        
        :param run_id: 运行ID
        :param task_config: 任务配置
        :param environment_vars: 环境变量
        :param message_templates: 消息模板列表
        :param kafka_servers: Kafka服务器地址
        :return: (是否成功, 错误信息)
        """
        # 如果已经有相同ID的测试在运行，先停止
        if run_id in self.active_tests:
            self.stop_test(run_id)
        
        # 连接到Kafka
        success, error = self.connect()
        if not success:
            return False, error
        
        # 创建并启动测试运行器
        test_runner = TestRunner(
            run_id=run_id,
            task_config=task_config,
            environment_vars=environment_vars,
            message_templates=message_templates,
            kafka_producer=self.producer,
            callback=self._test_callback
        )
        
        # 存储活动的测试
        self.active_tests[run_id] = test_runner
        
        # 启动测试
        test_runner.start()
        
        return True, None
    
    def stop_test(self, run_id: str) -> Tuple[bool, Optional[str]]:
        """
        停止测试任务
        
        :param run_id: 运行ID
        :return: (是否成功, 错误信息)
        """
        if run_id not in self.active_tests:
            return False, "测试任务不存在或已停止"
        
        # 停止测试
        test_runner = self.active_tests[run_id]
        test_runner.stop()
        
        # 从活动测试中移除
        del self.active_tests[run_id]
        
        return True, None
    
    def get_test_status(self, run_id: str) -> Dict[str, Any]:
        """
        获取测试状态
        
        :param run_id: 运行ID
        :return: 测试状态
        """
        if run_id not in self.active_tests:
            return {
                'running': False,
                'status': 'stopped',
                'cycle_count': 0,
                'message_count': 0,
                'error_count': 0
            }
        
        test_runner = self.active_tests[run_id]
        return test_runner.get_status()
    
    def _test_callback(self, run_id: str, status: Dict[str, Any]):
        """
        测试回调函数，当测试状态变化时调用
        
        :param run_id: 运行ID
        :param status: 测试状态
        """
        # 如果测试已完成，从活动测试中移除
        if status.get('status') in ['completed', 'failed', 'stopped']:
            if run_id in self.active_tests:
                del self.active_tests[run_id]


class TestRunner:
    """测试运行器，负责执行测试任务"""
    
    def __init__(self, run_id: str, task_config: Dict[str, Any], environment_vars: Dict[str, Any],
                 message_templates: List[Dict[str, Any]], kafka_producer: KafkaProducer, 
                 callback=None):
        """
        初始化测试运行器
        
        :param run_id: 运行ID
        :param task_config: 任务配置
        :param environment_vars: 环境变量
        :param message_templates: 消息模板列表
        :param kafka_producer: Kafka生产者
        :param callback: 状态回调函数
        """
        self.run_id = run_id
        self.task_config = task_config
        self.environment_vars = environment_vars
        self.message_templates = message_templates
        self.kafka_producer = kafka_producer
        self.callback = callback
        
        self.running = False
        self.thread = None
        
        # 设置周期开始时间为当前时间
        current_time = time.time()
        self.cycle_start_time = current_time
        print(f"[TestRunner.__init__] 初始化周期开始时间: {self.cycle_start_time:.2f}")
        
        self.status = {
            'running': False,
            'status': 'initialized',
            'cycle_count': 0,
            'message_count': 0,
            'error_count': 0,
            'start_time': current_time,
            'last_update_time': current_time,
            'latest_messages': {},  # {queue_id: {topic, message}}
            'cycle_duration': self.task_config.get('cycle_duration', 300),  # 添加周期时长
            'cycle_elapsed': 0  # 添加周期已过时间
        }
        
        # 创建消息队列，用于存储要发送的消息
        self.message_queue = queue.Queue()
        
        # 创建结果队列，用于存储发送结果
        self.result_queue = queue.Queue()
    
    def start(self):
        """启动测试"""
        if self.running:
            return
        
        self.running = True
        self.status['running'] = True
        self.status['status'] = 'running'
        self.status['start_time'] = time.time()
        self.cycle_start_time = time.time()  # 重置周期开始时间
        
        # 创建并启动线程
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()
    
    def stop(self):
        """停止测试"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        
        self.status['running'] = False
        self.status['status'] = 'stopped'
        
        # 调用回调函数
        if self.callback:
            self.callback(self.run_id, self.get_status())
    
    def get_status(self) -> Dict[str, Any]:
        """获取测试状态"""
        # 计算周期已过时间
        current_time = time.time()
        cycle_duration = self.status.get('cycle_duration', 300)
        
        # 计算从周期开始到现在的总秒数
        total_elapsed = current_time - self.cycle_start_time
        
        # 计算在当前周期中已过的秒数（取模，确保值在0到cycle_duration之间）
        cycle_elapsed = total_elapsed % cycle_duration
        
        # 打印调试信息
        print(f"[TestRunner] 计算周期进度: 当前时间={current_time:.2f}, 周期开始时间={self.cycle_start_time:.2f}")
        print(f"[TestRunner] 总过去时间={total_elapsed:.2f}秒, 周期时长={cycle_duration}秒, 周期已过时间={cycle_elapsed:.2f}秒")
        
        # 使用直接计算的周期已过时间，而不是依赖状态中可能未更新的值
        self.status['cycle_elapsed'] = cycle_elapsed
        
        # 返回状态的副本，避免修改原始状态
        return {**self.status}
    
    def _run(self):
        """运行测试"""
        try:
            # 获取任务配置
            cycle_duration = self.task_config.get('cycle_duration', 300)  # 默认300秒
            total_duration = self.task_config.get('total_duration', 0)    # 默认无限
            queues = self.task_config.get('queues', [])
            
            # 更新状态中的周期时长
            self.status['cycle_duration'] = cycle_duration
            
            # 启动发送线程
            sender_thread = threading.Thread(target=self._sender_thread)
            sender_thread.daemon = True
            sender_thread.start()
            
            # 启动结果处理线程
            result_thread = threading.Thread(target=self._result_thread)
            result_thread.daemon = True
            result_thread.start()
            
            # 计算结束时间
            end_time = time.time() + total_duration if total_duration > 0 else None
            
            # 循环执行，直到达到总时长或被停止
            while self.running:
                # 检查是否达到总时长
                if end_time and time.time() >= end_time:
                    break
                
                # 开始新的周期
                self.cycle_start_time = time.time()  # 更新周期开始时间
                print(f"[TestRunner] 开始新周期，设置周期开始时间: {self.cycle_start_time}")
                self.status['cycle_count'] += 1
                
                # 生成并发送本周期的消息
                for queue_item in queues:
                    if not queue_item.get('enabled', True):
                        continue
                    
                    queue_id = queue_item.get('id')
                    template_id = queue_item.get('template_id')
                    topic = queue_item.get('topic', '')
                    rate = queue_item.get('rate', 1)
                    
                    # 查找对应的模板
                    template = next((t for t in self.message_templates if t.get('id') == template_id), None)
                    if not template:
                        continue
                    
                    # 计算本周期需要发送的消息数量
                    message_count = rate * cycle_duration
                    
                    # 将消息放入队列
                    for _ in range(message_count):
                        self.message_queue.put({
                            'queue_id': queue_id,
                            'topic': topic,
                            'template': template,
                            'timestamp': time.time()
                        })
                
                # 定期更新周期已过时间
                update_interval = 1  # 每秒更新一次
                elapsed = 0
                
                while elapsed < cycle_duration and self.running:
                    # 计算周期已过时间
                    current_time = time.time()
                    elapsed = current_time - self.cycle_start_time
                    
                    # 更新状态中的周期已过时间
                    self.status['cycle_elapsed'] = elapsed
                    
                    # 等待一段时间再更新
                    time.sleep(update_interval)
            
            # 测试完成
            self.status['status'] = 'completed'
            
        except Exception as e:
            self.status['status'] = 'failed'
            self.status['error'] = str(e)
        
        finally:
            self.running = False
            self.status['running'] = False
            
            # 调用回调函数
            if self.callback:
                self.callback(self.run_id, self.get_status())
    
    def _sender_thread(self):
        """发送线程，从消息队列中获取消息并发送"""
        from app.utils.message_generator import MessageGenerator
        
        while self.running:
            try:
                # 从队列中获取消息，最多等待1秒
                try:
                    message_item = self.message_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # 获取消息信息
                queue_id = message_item.get('queue_id')
                topic = message_item.get('topic', '')
                template = message_item.get('template', {})
                timestamp = message_item.get('timestamp', time.time())
                
                # 生成消息
                python_code = template.get('python_code', '')
                message = MessageGenerator.generate_message(python_code, self.environment_vars)
                
                # 如果生成失败，记录错误
                if not message.get('success'):
                    self.result_queue.put({
                        'success': False,
                        'queue_id': queue_id,
                        'topic': topic,
                        'error': message.get('error'),
                        'timestamp': timestamp
                    })
                    continue
                
                # 发送消息
                try:
                    future = self.kafka_producer.send(topic, message.get('result'))
                    record_metadata = future.get(timeout=10)
                    
                    # 记录最新的消息
                    self.status['latest_messages'][queue_id] = {
                        'topic': topic,
                        'message': message.get('result')
                    }
                    
                    # 发送成功
                    self.result_queue.put({
                        'success': True,
                        'queue_id': queue_id,
                        'topic': topic,
                        'timestamp': timestamp
                    })
                    
                except Exception as e:
                    # 发送失败
                    self.result_queue.put({
                        'success': False,
                        'queue_id': queue_id,
                        'topic': topic,
                        'error': str(e),
                        'timestamp': timestamp
                    })
                
            except Exception as e:
                # 忽略其他错误，继续运行
                pass
    
    def _result_thread(self):
        """结果处理线程，处理发送结果"""
        while self.running:
            try:
                # 从结果队列中获取结果，最多等待1秒
                try:
                    result = self.result_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # 更新状态
                if result.get('success'):
                    self.status['message_count'] += 1
                else:
                    self.status['error_count'] += 1
                
                self.status['last_update_time'] = time.time()
                
            except Exception as e:
                # 忽略其他错误，继续运行
                pass 