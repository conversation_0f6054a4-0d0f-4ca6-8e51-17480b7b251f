#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
from typing import Dict, Any, List, Optional, Tuple


class DbEnvManager:
    """数据库环境变量管理器，用于管理环境变量配置"""
    
    def __init__(self):
        """初始化数据库环境变量管理器"""
        pass
    
    def ensure_dirs(self):
        """确保必要的目录存在（数据库版本不需要）"""
        pass
    
    def list_environments(self) -> List[Dict[str, Any]]:
        """
        获取所有环境变量配置列表

        :return: 环境变量配置列表
        """
        try:
            from app.models.environment import Environment
            environments = Environment.query.all()
            return [env.to_dict() for env in environments]
        except Exception as e:
            print(f"获取环境变量配置列表失败: {e}")
            return []
    
    def get_environment(self, env_id: int) -> Optional[Dict[str, Any]]:
        """
        获取指定ID的环境变量配置

        :param env_id: 环境变量配置ID
        :return: 环境变量配置数据，如果不存在则返回None
        """
        try:
            from app.models.environment import Environment
            environment = Environment.query.get(env_id)
            if environment:
                return environment.to_dict()
            return None
        except Exception as e:
            print(f"获取环境变量配置失败: {e}")
            return None
    
    def save_environment(self, env_data: Dict[str, Any]) -> Tuple[bool, Optional[str], Optional[int]]:
        """
        保存环境变量配置

        :param env_data: 环境变量配置数据
        :return: (是否成功, 错误信息, 环境变量配置ID)
        """
        try:
            from app.models.environment import Environment, EnvironmentVariable
            from app import db

            # 验证必要的字段
            if "name" not in env_data or not env_data["name"].strip():
                return False, "环境变量配置名称不能为空", None

            # 检查是否是更新现有配置
            is_update = "id" in env_data and env_data["id"] > 0

            if is_update:
                # 更新现有环境变量配置
                environment = Environment.query.get(env_data["id"])
                if not environment:
                    return False, "环境变量配置不存在", None

                # 检查名称是否与其他配置冲突
                existing = Environment.query.filter(
                    Environment.name == env_data["name"],
                    Environment.id != env_data["id"]
                ).first()
                if existing:
                    return False, "环境变量配置名称已存在", None

                # 更新基本信息
                environment.name = env_data["name"]
                environment.description = env_data.get("description", "")

                # 删除现有变量
                for var in environment.variables:
                    db.session.delete(var)

                # 添加新变量
                variables = env_data.get('variables', {})
                for key, value in variables.items():
                    environment.variables.append(EnvironmentVariable(key=key, value=value))

                env_id = environment.id
            else:
                # 检查名称是否已存在
                existing = Environment.query.filter_by(name=env_data["name"]).first()
                if existing:
                    return False, "环境变量配置名称已存在", None

                # 创建新环境变量配置
                environment = Environment.from_dict(env_data)
                db.session.add(environment)
                db.session.flush()  # 获取ID
                env_id = environment.id

            # 提交更改
            db.session.commit()
            return True, None, env_id

        except Exception as e:
            db.session.rollback()
            return False, f"保存环境变量配置失败: {e}", None
    
    def delete_environment(self, env_id: int) -> Tuple[bool, Optional[str]]:
        """
        删除环境变量配置

        :param env_id: 环境变量配置ID
        :return: (是否成功, 错误信息)
        """
        try:
            from app.models.environment import Environment
            from app.models.task import TestTask
            from app import db

            environment = Environment.query.get(env_id)
            if not environment:
                return False, "环境变量配置不存在"

            # 检查是否有任务在使用这个环境
            tasks_using_env = TestTask.query.filter_by(environment_id=env_id).all()
            if tasks_using_env:
                task_names = [task.name for task in tasks_using_env]
                return False, f"无法删除，以下任务正在使用此环境: {', '.join(task_names)}"

            db.session.delete(environment)
            db.session.commit()
            return True, None

        except Exception as e:
            db.session.rollback()
            return False, f"删除环境变量配置失败: {e}"
    
    def clone_environment(self, env_id: int) -> Tuple[bool, Optional[str], Optional[int]]:
        """
        克隆环境变量配置

        :param env_id: 要克隆的环境变量配置ID
        :return: (是否成功, 错误信息, 新环境变量配置ID)
        """
        try:
            from app.models.environment import Environment

            # 获取源环境变量配置
            source_env = Environment.query.get(env_id)
            if not source_env:
                return False, "源环境变量配置不存在", None

            # 创建克隆配置
            clone_data = source_env.to_dict()
            # 移除ID，以便分配新ID
            if "id" in clone_data:
                del clone_data["id"]

            # 修改名称，添加"(副本)"后缀
            base_name = f"{source_env.name} (副本)"
            clone_name = base_name
            counter = 1

            # 确保名称唯一
            while Environment.query.filter_by(name=clone_name).first():
                counter += 1
                clone_name = f"{base_name} {counter}"

            clone_data["name"] = clone_name

            # 保存克隆配置
            return self.save_environment(clone_data)

        except Exception as e:
            return False, f"克隆环境变量配置失败: {e}", None
    
    def initialize_defaults(self):
        """初始化默认环境变量配置"""
        try:
            from app.models.environment import Environment

            # 检查是否已有配置
            if Environment.query.count() > 0:
                return

            # 创建默认的开发环境配置
            dev_env_data = {
                "name": "开发环境",
                "description": "本地开发环境配置",
                "variables": {
                    "KAFKA_SERVERS": "localhost:9092",
                    "USE_SSL": "false",
                    "MAC_COUNT": "50",
                    "ARP_COUNT": "50",
                    "MESSAGE_RATE": "10"
                }
            }

            # 创建默认的测试环境配置
            test_env_data = {
                "name": "测试环境",
                "description": "测试环境配置",
                "variables": {
                    "KAFKA_SERVERS": "test-kafka:9092",
                    "USE_SSL": "true",
                    "MAC_COUNT": "100",
                    "ARP_COUNT": "100",
                    "MESSAGE_RATE": "50"
                }
            }

            # 保存默认配置
            self.save_environment(dev_env_data)
            self.save_environment(test_env_data)

            print("已初始化默认环境变量配置")

        except Exception as e:
            print(f"初始化默认环境变量配置失败: {e}")
