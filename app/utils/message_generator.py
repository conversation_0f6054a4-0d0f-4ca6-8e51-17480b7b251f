from typing import Dict, Any, Optional
from app.utils.code_executor import CodeExecutor

class MessageGenerator:
    """消息生成器，用于生成Kafka消息"""
    
    @staticmethod
    def generate_message(python_code: str, env_vars: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        使用Python代码生成消息
        
        :param python_code: Python代码字符串
        :param env_vars: 环境变量字典
        :return: 包含生成结果的字典
        """
        # 使用代码执行器执行Python代码
        return CodeExecutor.execute_code(python_code, env_vars)
    
    @staticmethod
    def get_example_code(template_type: str = 'arp') -> str:
        """
        获取示例代码
        
        :param template_type: 模板类型，可选值：arp, mac
        :return: 示例代码字符串
        """
        if template_type == 'arp':
            return MessageGenerator.get_arp_example_code()
        elif template_type == 'mac':
            return MessageGenerator.get_mac_example_code()
        else:
            return MessageGenerator.get_custom_example_code()
    
    @staticmethod
    def get_arp_example_code() -> str:
        """获取ARP消息示例代码"""
        return '''# ARP消息生成示例
# 可以使用的内置函数:
# - random_int(min, max): 生成指定范围内的随机整数
# - random_ipv4(): 生成随机IPv4地址
# - random_ipv4_private(): 生成随机私有IPv4地址
# - random_mac(): 生成随机MAC地址
# - random_mac_unicast(): 生成随机单播MAC地址
# - time(): 获取当前时间戳(秒)
# - uuid4(): 生成UUID

# 使用环境变量
device_id_num = env_vars.get('DEVICE_ID', random_int(1000, 9999))
min_arp_entries = int(env_vars.get('MIN_ARP_ENTRIES', 1))
max_arp_entries = int(env_vars.get('MAX_ARP_ENTRIES', 5))

# 生成ARP条目列表
arp_list = []
for i in range(random_int(min_arp_entries, max_arp_entries)):
    # 随机选择接口
    interfaces = ['Or1', f'Vl{random_int(1, 99)}', f'Mg0/0/{i}']
    intf = random_choice(interfaces)
    
    # 生成IP和MAC
    ip = random_ipv4()
    mac = random_mac_unicast().upper()
    
    arp_entry = {
        "intf": intf,
        "ip": ip,
        "mac": mac
    }
    arp_list.append(arp_entry)

# 构建最终消息
message = {
    "devflag": f"ip:{device_id_num}",
    "message_source_env": env_vars.get('KAFKA_ENV_NAME', 'unknown'),
    "interval": random_choice([300, 600, 900]),
    "id": str(device_id_num),
    "deviceSn": f"SN{random_numeric(12)}",
    "deviceId": f"ip:{random_ipv4_private()}",
    "arpList": arp_list,
    "timestamp": int(time() * 1000)
}

# 返回生成的消息
return message'''
    
    @staticmethod
    def get_mac_example_code() -> str:
        """获取MAC消息示例代码"""
        return '''# MAC地址消息生成示例
# 可以使用的内置函数:
# - random_int(min, max): 生成指定范围内的随机整数
# - random_ipv4(): 生成随机IPv4地址
# - random_ipv4_private(): 生成随机私有IPv4地址
# - random_mac(): 生成随机MAC地址
# - random_mac_unicast(): 生成随机单播MAC地址
# - time(): 获取当前时间戳(秒)
# - uuid4(): 生成UUID

# 使用环境变量
device_ip = env_vars.get('DEVICE_IP', random_ipv4_private())
mac_count = int(env_vars.get('MAC_COUNT', 5))
device_key = f"ip:{device_ip}"

# 生成MAC地址列表
mac_list = []
for i in range(mac_count):
    # 随机选择VLAN
    vlan_choices = ["vlan1", "vlan1003"]
    vlan = random_choice(vlan_choices)
    
    # 端口固定为Te0/49
    port = "Te0/49"
    
    # 生成MAC地址
    mac = random_mac_unicast().upper()
    
    mac_info = {
        "vlan": vlan,
        "port": port,
        "mac": mac
    }
    mac_list.append(mac_info)

# 构建消息ID
numeric_id = int(''.join([part.zfill(3) for part in device_ip.split('.')])[-4:])

# 获取当前时间戳
current_time = int(time() * 1000)

# 构建消息
message = {
    "devflag": device_key,
    "macList": mac_list,
    "interval": 900,
    "id": numeric_id,
    "deviceId": device_key,
    "startTimestamp": current_time - 312,
    "timestamp": current_time
}

# 返回生成的消息
return message'''
    
    @staticmethod
    def get_custom_example_code() -> str:
        """获取自定义消息示例代码"""
        return '''# 自定义消息生成示例
# 可以使用的内置函数:
# - random_int(min, max): 生成指定范围内的随机整数
# - random_float(min, max): 生成指定范围内的随机浮点数
# - random_choice(options): 从列表中随机选择一个元素
# - random_string(length): 生成指定长度的随机字符串
# - random_numeric(length): 生成指定长度的随机数字字符串
# - random_alpha(length): 生成指定长度的随机字母字符串
# - random_ipv4(): 生成随机IPv4地址
# - random_ipv4_private(): 生成随机私有IPv4地址
# - random_mac(): 生成随机MAC地址
# - random_mac_unicast(): 生成随机单播MAC地址
# - time(): 获取当前时间戳(秒)
# - uuid4(): 生成UUID

# 使用环境变量
app_name = env_vars.get('APP_NAME', 'my-app')
instance_id = env_vars.get('INSTANCE_ID', str(uuid4()))

# 生成随机数据
cpu_usage = random_float(0, 100)
memory_usage = random_float(0, 100)
disk_usage = random_float(0, 100)

# 构建消息
message = {
    "app": app_name,
    "instance": instance_id,
    "metrics": {
        "cpu": round(cpu_usage, 2),
        "memory": round(memory_usage, 2),
        "disk": round(disk_usage, 2)
    },
    "status": random_choice(["healthy", "warning", "critical"]),
    "timestamp": int(time() * 1000)
}

# 返回生成的消息
return message''' 