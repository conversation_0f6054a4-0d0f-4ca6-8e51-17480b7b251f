

















# Project Architecture
- User wants to convert the project to a frontend-backend separated architecture.

# User Interface
- User prefers the UX style from ux.html file and wants the project's interface to match that design.
- For modal management interfaces (like template and environment management), user prefers a right-side fixed layout with a scrollable left side.
- User prefers CodeMirror for Python code editing in the interface.
- User prefers to remove descriptions from the interface elements.
- User prefers template name and default topic fields to be displayed on the same row, and test code and Python labels to be on the same row for more compact layout.
- User confirms and approves the left-right layout for Python code label and test button in the template management interface.
- User prefers CSS row layouts with display: flex, gap: 15px for spacing between columns, and flex: 1 for equal column distribution in col-md-6 elements.
- User prefers preview content to be displayed in a popup/modal window rather than inline display.
- User prefers preview modal interfaces without scary/intimidating icons and expects both X button and close button to work properly for closing modals.
- User prefers preview functionality to include a small button next to the preview button for editing temporary environment variables, utilizing the env_vars parameter from list interfaces.
- Preview environment variables are located in the env_vars field from the template/api/list endpoint and should be used for preview functionality calls.
- User prefers preview environment variables to automatically use the selected template from the left sidebar instead of having a separate dropdown for template selection.
- User prefers save buttons to be placed on the right side of tab sections for task configuration interfaces.
- User prefers a flattened UI structure where parameters, queues, and environment configuration are consolidated into a single tab page, with main navigation at the same level as run output, run history, and statistics charts.
- User prefers modal dialogs for new task creation, consolidated tab structure for task configuration, and enhanced content areas for run output/history/statistics in the main project interface.
- User prefers new task creation forms to have the same elements and structure as the main task configuration form for consistency.
- User prefers modal dialogs to only close when clicking close buttons (X or cancel), not when clicking outside the modal area.
- User prefers to display the full JSON content of messages in the interface rather than just showing message IDs.
- User prefers that when a task is running, the start button should change to a stop button to allow stopping the running task.
- User prefers task running status to be determined by querying actual backend running status rather than relying on WebSocket state for status determination.
- User prefers task tab clicks to have transition animations and wants the right side content to be re-rendered when switching tabs.
- User prefers simple and fast switching animations over complex ones for task tab transitions.
- User prefers long text content in task list items to be truncated with ellipsis (...) and shown in full via tooltips on hover.
- User prefers not to show help cursor (question mark) on hover for task names with tooltips.
- The frontend code in the app folder has been deprecated and should not be used.
- User prefers log configuration (like maxConsoleLogLines) to be user-configurable in the frontend interface rather than hardcoded in config files.

# Real-time Output
- User prefers WebSocket implementation for real-time output functionality in the running output feature.
- User prefers real-time log output to be implemented using WebSocket for automatic streaming rather than manual button-triggered polling.
- User prefers WebSocket real-time logs to display the actual Kafka message content that gets sent to Kafka from the backend, not simulated or test message data, and not the internal message data structure.
- Message sending should use the run_test method in task.py, and WebSocket logs should be output from there to the frontend, not from websocket_events.py.
- User prefers a dedicated WebSocket logging module that handles frontend communication while keeping thread-based task execution, rather than having separate WebSocket vs thread execution paths.
- User prefers unified thread-based task execution with WebSocket logging rather than separate WebSocket vs thread execution paths in the codebase.
- User prefers running output logs to be limited to 10 items for debugging purposes and wants frontend script configuration for this setting.
- User prefers frontend to display maximum 10 log entries and wants automatic cleanup of older logs to prevent frontend memory issues.

# Queue Configuration Interface
- Remove the 'enable/disable' toggle and make Topic field read-only, automatically populated from the selected message template.

# Data Format
- Environment variables should be stored as object format {"key": "value"} not as array format [{"key": "name", "value": "val"}] in the variables field.

# Environment Variables Storage
- User prefers environment variables to be stored in database rather than files for better maintainability and wants the external interface to remain consistent.

# Dependency Management
- User uses uv for Python dependency management instead of pip.
- The application should be started using 'uv run run.py' command.

# Codebase
- User prefers to remove simulated network timeout and similar mock/simulation code from the codebase.
- User prefers to remove unused database tables from the codebase for cleaner database schema.

# Run History
- User wants a run history module that displays last run events, message count, sent topics, and last message content with both frontend and backend implementation.
- User prefers run history to be filtered to show only records related to the currently selected task from the left sidebar.
- User prefers history interface to automatically show only the current selected task's history without toggle buttons for switching between current task and all tasks views.
- User wants a clear history functionality that only clears history records for the currently selected task, not all tasks.