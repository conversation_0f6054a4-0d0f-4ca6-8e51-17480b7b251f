# API客户端配置文件

# 当前激活的环境
active = "30w"

[config.30w]
server = "**************"
user = "admin"
password = "Rgsdn@66"
deviceIp = "**************"
siteId = 4151
zoneId = 4152
mac = "1000.0000.0002"
count = 1
# Kafka配置
kafka_server = "**************:9093,**************:9093,**************:9093"
kafka_topic = "dev-mac"
mac_count = 200 # 每个设备生成的MAC地址数量
arp_count = 200  # 每个设备生成的ARP条目数量
device_limit = 0  # 发送的设备数量限制，0表示不限制，发送所有设备


[config.32]
server = "*************"
deviceIp = "**************"
mac = "1000.0000.0002"
count = 1
user = "admin"
password = "Rgsdn@66789"
bindSiteId = 4151
zoneId = 4152
# Kafka配置
kafka_server = "*************:9092"
kafka_topic = "dev-mac"
mac_count = 200
device_limit = 0  # 发送的设备数量限制，0表示不限制，发送所有设备

# ARP消息配置
arp_count = 20

[config."204.51"]
server = "*************"
deviceIp = "**********"
mac = "1000.0000.0001"
count = 60
user = "admin"
password = "Rgsdn@66"
# Kafka配置
kafka_server = "*************:9092"
kafka_topic = "dev-mac"
mac_count = 200
device_limit = 0  # 发送的设备数量限制，0表示不限制，发送所有设备

# ARP消息配置
arp_count = 20

[config.199]
server = "*************"
deviceIp = "**********"
mac = "1000.0000.0002"
count = 1
user = "admin"
password = "Rgsdn@66"
# Kafka配置
kafka_server = "*************:9092"
kafka_topic = "dev-mac"
mac_count = 200
device_limit = 0  # 发送的设备数量限制，0表示不限制，发送所有设备

# ARP消息配置
arp_count = 20

[config.123]
server = "*************"
deviceIp = "*************"
mac = "1000.0000.0001"
count = 10
user = "admin"
password = "Rgsdn@321"

[config.dfx]
server = "**************"
deviceIp = "************"
mac = "1000.0000.0001"
count = 100000
user = "admin"
password = "Rgsdn@66"

[config.222]
server = "**************"
deviceIp = "*************"
mac = "1000.0000.0001"
count = 2000
user = "<EMAIL>"
password = "Rgsdn@66"

[config.229]
server = "**************"
deviceIp = "*************"
mac = "1000.0000.0001"
count = 2000
user = "admin"
password = "Rgsdn@66"
siteId = 255401
