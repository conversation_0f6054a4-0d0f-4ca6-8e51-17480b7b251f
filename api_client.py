import json
import http.client
import ssl
import toml
from typing import List, Dict, Any, Optional, Union, Tuple


class ApiClient:
    def __init__(self, host: str, user: str = None, password: str = None, token: str = None, site_id: str = None):
        """
        初始化API客户端
        
        :param host: 服务器主机地址
        :param user: 用户名
        :param password: 密码
        :param token: 访问令牌(可选，如已有token可直接提供)
        :param site_id: 站点ID(可选)
        """
        self.host = host
        self.user = user
        self.password = password
        self.token = token
        self.site_id = site_id
        
        # 如果提供了用户名和密码但没有token，自动获取token
        if user and password and not token:
            self.token = self.get_token()
    
    def _create_connection(self):
        """创建不验证SSL的HTTPS连接"""
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        return http.client.HTTPSConnection(self.host, context=context)
    
    def get_token(self) -> str:
        """
        获取访问令牌
        
        :return: 访问令牌字符串
        """
        conn = self._create_connection()
        payload = f'username={self.user}&password={self.password}&grant_type=password'
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        conn.request("POST", "/api/auth/token", payload, headers)
        res = conn.getresponse()
        data = res.read()
        token = json.loads(data.decode("utf-8"))["access_token"]
        return token
    
    def list_devices(self, site_id: Optional[str] = None, page_size: int = 5000) -> List[str]:
        """
        获取设备列表 (旧API)
        
        :param site_id: 站点ID，如果不提供则使用初始化时的site_id
        :param page_size: 每页数量
        :return: 设备ID列表
        """
        site_id = site_id or self.site_id
        if not site_id:
            raise ValueError("站点ID必须提供")
        
        conn = self._create_connection()
        payload = json.dumps({
            "ifAlias": "",
            "name": "",
            "mac": "",
            "typeId": "",
            "onlineState": "",
            "configDownStatus": "",
            "snmpTmplId": "",
            "telnetTmplId": "",
            "deviceModel": "",
            "deviceLevel": "",
            "location": "",
            "orderParam": "",
            "orderAscDesc": "",
            "deviceNetRole": "",
            "pageIndex": 1,
            "pageSize": page_size,
            "areaId": site_id,
            "sceneType": "INC",
            "deviceType": "switch",
            "isRecursion": True,
            "containIncDevice": True
        })
        headers = {
            'Content-Type': 'application/json',
            'authorization': f'Bearer {self.token}'
        }
        conn.request("POST", "/api/ne-mgr/v1/byPageSimple", payload, headers)
        res = conn.getresponse()
        data = res.read()
        device_ids = []
        for device in json.loads(data.decode("utf-8"))["result"]["list"]:
            device_ids.append(device["id"])
        return device_ids
    
    def get_device_list(self, site_id: Optional[str] = None, fields: Optional[List[str]] = None) -> Union[List[Dict[str, Any]], List[Tuple]]:
        """
        获取设备详细信息列表 (新API)
        
        :param site_id: 站点ID，如果不提供则使用初始化时的site_id
        :param fields: 要提取的字段列表，如果提供则返回元组列表而非字典列表，默认返回全部字段
                      例如: fields=["id", "deviceIp"] 将只返回设备ID和IP
        :return: 包含设备详细信息的字典列表或指定字段的元组列表
        """
        site_id = site_id or self.site_id
        
        conn = self._create_connection()
        headers = {
            'Content-Type': 'application/json',
            'authorization': f'Bearer {self.token}'
        }
        
        # 如果提供了站点ID，可以在URL中添加查询参数
        url = "/api/ne-mgr/v2/device/basic"
        if site_id:
            url += f"?siteId={site_id}"
            
        conn.request("GET", url, headers=headers)
        res = conn.getresponse()
        data = res.read()
        devices = json.loads(data.decode("utf-8"))
        
        # 如果指定了要提取的字段，只返回这些字段
        if fields:
            result = []
            for device in devices:
                # 提取指定的字段，如果字段不存在则为None
                values = tuple(device.get(field) for field in fields)
                result.append(values)
            return result
        
        return devices
    
    def get_device_ids(self, site_id: Optional[str] = None, filter_func=None) -> List[str]:
        """
        获取设备ID列表，使用新API
        
        :param site_id: 站点ID，如果不提供则使用初始化时的site_id
        :param filter_func: 用于过滤设备的函数，接收设备信息字典，返回布尔值
        :return: 设备ID列表
        """
        devices = self.get_device_list(site_id)
        
        if filter_func:
            devices = [device for device in devices if filter_func(device)]
            
        return [str(device["id"]) for device in devices]
    
    def add_terminal_identify(self, device_ids: List[str], site_id: Optional[str] = None) -> Dict[str, Any]:
        """
        添加终端识别
        
        :param device_ids: 设备ID列表
        :param site_id: 站点ID，如果不提供则使用初始化时的site_id
        :return: API响应结果
        """
        site_id = site_id or self.site_id
        if not site_id:
            raise ValueError("站点ID必须提供")
        
        conn = self._create_connection()
        payload = json.dumps(device_ids)
        headers = {
            'Content-Type': 'application/json',
            'SITE-ID': site_id,
            'authorization': f'Bearer {self.token}'
        }
        conn.request("POST", "/api/endpoint-mgr/v1/terminal-identify/template/device/apply", payload, headers)
        res = conn.getresponse()
        data = res.read()
        return json.loads(data.decode("utf-8"))
    
    def get_device_id_ip_map(self, site_id: Optional[str] = None) -> Dict[str, str]:
        """
        获取设备ID和IP的映射关系
        
        :param site_id: 站点ID，如果不提供则使用初始化时的site_id
        :return: 以设备ID为键、设备IP为值的字典
        """
        device_data = self.get_device_list(site_id, fields=["id", "deviceIp"])
        return {str(device_id): ip for device_id, ip in device_data if device_id and ip}
    
    @staticmethod
    def split_array(arr: List[Any], size: int = 100) -> List[List[Any]]:
        """
        将一个数组切割成指定大小的子数组
        
        :param arr: 待切割的数组
        :param size: 子数组的大小，默认为 100
        :return: 切割后的子数组列表
        """
        return [arr[i:i+size] for i in range(0, len(arr), size)]


def load_config(config_file: str = 'config.toml') -> Dict[str, Any]:
    """
    加载TOML格式的配置文件
    
    :param config_file: 配置文件路径
    :return: 配置信息
    """
    with open(config_file, 'r') as f:
        config = toml.load(f)
    return config["config"][config["active"]] 