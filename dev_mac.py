#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import random
from api_client import load_config
from kafka_sender import KafkaSender, parse_command_args
from message_generators import MacMessageGenerator

def main():
    """MAC地址消息发送主程序"""
    # 解析命令行参数
    args = parse_command_args()
    
    # 如果命令行指定了设备数量限制，则覆盖配置文件中的设置
    config = load_config()
    if args.limit > 0:
        config["device_limit"] = args.limit
        print(f"通过命令行参数设置设备数量限制为: {args.limit}台")
    
    # 创建Kafka发送器
    sender = KafkaSender(config)
    
    # 创建MAC消息生成器
    mac_generator = MacMessageGenerator(config)
    
    # 获取设备列表
    device_id_ip_map = sender.get_device_list()
    if not device_id_ip_map:
        print("未找到设备，无法发送消息")
        return False
    
    # 生成消息
    print("\n正在生成MAC地址消息...")
    device_messages = mac_generator.generate_messages(device_id_ip_map)
    print(f"已生成 {len(device_messages)} 个设备的MAC地址消息")
    print(device_messages)
    
    # 获取Kafka主题
    kafka_topic = mac_generator.get_topic()
    print(f"消息将发送到主题: {kafka_topic}")
    
    # 发送消息
    continuous_mode = not args.once
    if continuous_mode:
        print(f"运行模式: 持续发送 (每 {args.interval} 秒)")
    else:
        print("运行模式: 单次发送")
    
    return sender.send_messages(
        kafka_topic=kafka_topic,
        device_messages=device_messages,
        continuous_mode=continuous_mode,
        interval_seconds=args.interval
    )


if __name__ == "__main__":
    # 设置随机种子，确保每次运行生成的MAC地址相同
    random.seed(42)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序异常: {e}") 