<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KafkaTool v2 - Postman Style</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Postman-like color scheme
                        sidebar: {
                            bg: '#2c3e50',
                            hover: '#34495e',
                            active: '#3498db',
                            text: '#ecf0f1',
                            border: '#34495e'
                        },
                        primary: {
                            500: '#3498db',
                            600: '#2980b9'
                        },
                        success: {
                            500: '#27ae60',
                            600: '#229954'
                        },
                        gray: {
                            50: '#f8f9fa',
                            100: '#e9ecef',
                            200: '#e0e0e0',
                            300: '#ccc',
                            400: '#95a5a6',
                            500: '#6c757d',
                            600: '#5a6268',
                            700: '#333',
                            800: '#2c3e50',
                            900: '#1a252f'
                        }
                    },
                    fontFamily: {
                        sans: ['-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', '"Helvetica Neue"', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/material.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/hint/show-hint.min.css">

    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>

    <!-- Socket.IO Client -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50 font-sans" x-data="kafkaToolApp()">
    <!-- 版本切换提示 -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 text-center text-sm">
        <span>🎉 您正在使用新版本 KafkaTool v2</span>
        <a href="/" class="ml-4 underline hover:no-underline">返回原版本</a>
        <a href="/compare" class="ml-2 underline hover:no-underline">版本对比</a>
    </div>

    <div class="flex h-screen bg-gray-50">
        <!-- 侧边栏 - 完全按照 ux.html 设计 -->
        <div class="w-70 bg-sidebar-bg text-sidebar-text flex flex-col border-r border-sidebar-border">
            <!-- 侧边栏头部 -->
            <div class="p-4 border-b border-sidebar-border">
                <h2 class="text-xl font-normal text-sidebar-text">KafkaTool</h2>
            </div>

            <!-- 新建任务按钮 -->
            <div class="p-4">
                <button
                    @click="showNewTaskModal = true"
                    class="w-full bg-primary-500 hover:bg-primary-600 text-white py-2.5 px-4 rounded text-base transition-colors duration-200"
                >
                    ＋ 新建任务
                </button>
            </div>

            <!-- 我的任务部分 -->
            <div class="flex-1 overflow-y-auto">
                <div class="px-4">
                    <div class="text-xs font-bold mt-5 mb-2.5 text-gray-400 uppercase">我的任务</div>
                    <ul class="space-y-0">
                        <template x-for="task in tasks" :key="task.id">
                            <li
                                @click="selectTask(task)"
                                :class="selectedTask?.id === task.id ? 'bg-sidebar-hover' : ''"
                                class="py-2.5 px-4 cursor-pointer border-b border-sidebar-border flex justify-between items-center transition-colors duration-200 hover:bg-sidebar-hover group"
                            >
                                <span class="text-sm flex-1 min-w-0 truncate" x-text="task.name"></span>
                                <span class="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <button
                                        @click.stop="runTask(task)"
                                        class="text-gray-400 hover:text-sidebar-text p-1 text-sm"
                                        title="运行"
                                    >
                                        ▶
                                    </button>
                                    <button
                                        @click.stop="copyTask(task)"
                                        class="text-gray-400 hover:text-sidebar-text p-1 text-sm"
                                        title="复制"
                                    >
                                        ❏
                                    </button>
                                    <button
                                        @click.stop="deleteTask(task)"
                                        class="text-gray-400 hover:text-sidebar-text p-1 text-sm"
                                        title="删除"
                                    >
                                        🗑
                                    </button>
                                </span>
                            </li>
                        </template>
                        <!-- 示例任务数据 -->
                        <li class="py-2.5 px-4 cursor-pointer border-b border-sidebar-border flex justify-between items-center transition-colors duration-200 hover:bg-sidebar-hover group bg-sidebar-hover">
                            <span class="text-sm">订单处理模拟</span>
                            <span class="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="运行">▶</button>
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="复制">❏</button>
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="删除">🗑</button>
                            </span>
                        </li>
                        <li class="py-2.5 px-4 cursor-pointer border-b border-sidebar-border flex justify-between items-center transition-colors duration-200 hover:bg-sidebar-hover group">
                            <span class="text-sm">日志收集测试</span>
                            <span class="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="运行">▶</button>
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="复制">❏</button>
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="删除">🗑</button>
                            </span>
                        </li>
                        <li class="py-2.5 px-4 cursor-pointer border-b border-sidebar-border flex justify-between items-center transition-colors duration-200 hover:bg-sidebar-hover group">
                            <span class="text-sm">用户行为分析</span>
                            <span class="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="运行">▶</button>
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="复制">❏</button>
                                <button class="text-gray-400 hover:text-sidebar-text p-1 text-sm" title="删除">🗑</button>
                            </span>
                        </li>
                    </ul>
                </div>

                <!-- 管理功能 -->
                <div class="px-4 mt-5">
                    <div class="text-xs font-bold mb-2.5 text-gray-400 uppercase">管理</div>
                    <ul class="space-y-0">
                        <li
                            @click="showEnvModal = true"
                            class="py-2.5 px-4 cursor-pointer border-b border-sidebar-border flex items-center transition-colors duration-200 hover:bg-sidebar-hover text-sm"
                        >
                            <span class="mr-2 w-4 text-center">🌐</span>
                            <span>环境变量</span>
                        </li>
                        <li
                            @click="showTemplateModal = true"
                            class="py-2.5 px-4 cursor-pointer border-b border-sidebar-border flex items-center transition-colors duration-200 hover:bg-sidebar-hover text-sm"
                        >
                            <span class="mr-2 w-4 text-center">📄</span>
                            <span>消息模板</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col bg-white">
            <!-- 主头部 -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex-1 max-w-lg">
                        <input 
                            x-model="taskName"
                            type="text" 
                            placeholder="任务名称"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        >
                    </div>
                    <button 
                        @click="runTask()"
                        :disabled="!selectedTask"
                        :class="selectedTask ? 'bg-green-500 hover:bg-green-600' : 'bg-gray-300 cursor-not-allowed'"
                        class="ml-4 px-6 py-2 text-white font-medium rounded-lg transition-colors duration-200 flex items-center space-x-2"
                    >
                        <i data-feather="play" class="w-5 h-5"></i>
                        <span>运行</span>
                    </button>
                </div>
            </div>
            
            <!-- 标签页导航 -->
            <div class="bg-gray-50 border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <template x-for="tab in tabs" :key="tab.id">
                        <button
                            @click="activeTab = tab.id"
                            :class="activeTab === tab.id ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
                            x-text="tab.name"
                        ></button>
                    </template>
                </nav>
            </div>
            
            <!-- 标签页内容 -->
            <div class="flex-1 overflow-hidden">
                <!-- 任务配置标签页 -->
                <div x-show="activeTab === 'config'" class="h-full overflow-y-auto">
                    <div class="max-w-4xl mx-auto p-6">
                        <div class="space-y-8">
                            <!-- 基本设置卡片 -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <i data-feather="settings" class="w-5 h-5 mr-2"></i>
                                    基本设置
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">任务描述</label>
                                        <input 
                                            x-model="taskDescription"
                                            type="text" 
                                            placeholder="输入任务描述"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                        >
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">周期时长 (秒)</label>
                                        <input 
                                            x-model="cycleDuration"
                                            type="number" 
                                            min="1"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                        >
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">总运行时长 (秒, 0为无限)</label>
                                        <input 
                                            x-model="totalDuration"
                                            type="number" 
                                            min="0"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                        >
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 队列配置卡片 -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                        <i data-feather="layers" class="w-5 h-5 mr-2"></i>
                                        队列配置
                                    </h3>
                                    <button 
                                        @click="addQueue()"
                                        class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-2"
                                    >
                                        <i data-feather="plus" class="w-4 h-4"></i>
                                        <span>添加队列</span>
                                    </button>
                                </div>
                                <div x-show="queues.length === 0" class="text-center py-8 text-gray-400 border-2 border-dashed border-gray-200 rounded-lg">
                                    <i data-feather="layers" class="w-12 h-12 mx-auto mb-3 opacity-50"></i>
                                    <p class="text-sm">暂无队列配置</p>
                                    <p class="text-xs">点击上方按钮添加第一个队列</p>
                                </div>

                                <!-- 队列列表 -->
                                <div x-show="queues.length > 0" class="space-y-4">
                                    <template x-for="(queue, index) in queues" :key="queue.id">
                                        <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                            <div class="flex items-center justify-between mb-4">
                                                <h4 class="font-medium text-gray-900" x-text="`队列 ${index + 1}`"></h4>
                                                <button
                                                    @click="removeQueue(index)"
                                                    class="text-red-500 hover:text-red-700 p-1"
                                                >
                                                    <i data-feather="trash-2" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">Kafka Topic</label>
                                                    <input
                                                        x-model="queue.topic"
                                                        type="text"
                                                        placeholder="例如: orders.new"
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                                    >
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">消息模板</label>
                                                    <select
                                                        x-model="queue.template_id"
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                                    >
                                                        <option value="">选择模板...</option>
                                                        <template x-for="template in templates" :key="template.id">
                                                            <option :value="template.id" x-text="template.name"></option>
                                                        </template>
                                                    </select>
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">消息数量</label>
                                                    <input
                                                        x-model="queue.message_count"
                                                        type="number"
                                                        min="1"
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                                    >
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">发送间隔 (毫秒)</label>
                                                    <input
                                                        x-model="queue.message_interval"
                                                        type="number"
                                                        min="100"
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            <!-- 环境配置卡片 -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <i data-feather="globe" class="w-5 h-5 mr-2"></i>
                                    环境配置
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">选择环境</label>
                                        <select
                                            x-model="selectedEnvironment"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                        >
                                            <option value="">选择环境...</option>
                                            <template x-for="env in environments" :key="env.id">
                                                <option :value="env.id" x-text="env.name"></option>
                                            </template>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">环境变量预览</label>
                                        <div class="h-32 bg-gray-50 border border-gray-300 rounded-lg p-3 overflow-y-auto">
                                            <div x-show="!selectedEnvironment" class="text-gray-400 text-sm">
                                                选择环境后将显示环境变量
                                            </div>
                                            <div x-show="selectedEnvironment" class="text-xs font-mono text-gray-600">
                                                <!-- 这里可以显示选中环境的变量预览 -->
                                                环境变量预览功能开发中...
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 保存按钮 -->
                            <div class="flex justify-end">
                                <button
                                    @click="saveTask()"
                                    :disabled="!selectedTask"
                                    :class="selectedTask ? 'bg-green-500 hover:bg-green-600' : 'bg-gray-300 cursor-not-allowed'"
                                    class="px-6 py-3 text-white font-medium rounded-lg transition-colors duration-200 flex items-center space-x-2 shadow-sm"
                                >
                                    <i data-feather="save" class="w-5 h-5"></i>
                                    <span>保存任务</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 运行输出标签页 -->
                <div x-show="activeTab === 'output'" class="h-full flex flex-col">
                    <!-- 控制栏 -->
                    <div class="bg-gray-50 border-b border-gray-200 px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <label class="text-sm font-medium text-gray-700">最大日志条数:</label>
                                    <input
                                        type="number"
                                        value="10"
                                        min="5"
                                        max="1000"
                                        class="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                                    >
                                    <button class="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600">
                                        应用
                                    </button>
                                </div>
                                <button class="px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600">
                                    清除日志
                                </button>
                            </div>
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" checked class="rounded">
                                    <span class="text-sm text-gray-700">自动滚动</span>
                                </label>
                                <input
                                    type="text"
                                    placeholder="筛选日志..."
                                    class="px-3 py-1 border border-gray-300 rounded text-sm w-40"
                                >
                            </div>
                        </div>
                    </div>

                    <!-- 状态栏 -->
                    <div class="bg-gray-100 px-6 py-3 border-b border-gray-200">
                        <div class="flex items-center space-x-6 text-sm">
                            <span class="flex items-center space-x-1">
                                <span class="text-gray-600">状态:</span>
                                <span :class="isRunning ? 'text-green-600' : 'text-gray-600'" class="font-medium" x-text="runStatus"></span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <span class="text-gray-600">消息数:</span>
                                <span class="font-medium" x-text="messageCount"></span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <span class="text-gray-600">周期数:</span>
                                <span class="font-medium" x-text="cycleCount"></span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <span class="text-gray-600">错误数:</span>
                                <span :class="errorCount > 0 ? 'text-red-600' : 'text-gray-600'" class="font-medium" x-text="errorCount"></span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <span class="text-gray-600">已运行时长:</span>
                                <span class="font-medium" x-text="elapsedTime"></span>
                            </span>
                        </div>
                    </div>

                    <!-- 日志输出区域 -->
                    <div class="flex-1 p-6 overflow-hidden">
                        <div class="h-full bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm overflow-y-auto">
                            <div class="space-y-1">
                                <div class="text-blue-400">欢迎使用 KafkaTool v2！</div>
                                <div class="text-blue-400">请配置任务参数并点击运行按钮开始测试。</div>
                                <div x-show="isRunning" class="text-yellow-400">任务正在运行中...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div x-show="activeTab === 'history'" class="h-full p-6">
                    <div class="text-center text-gray-500">运行历史功能开发中...</div>
                </div>
                
                <div x-show="activeTab === 'charts'" class="h-full p-6">
                    <div class="text-center text-gray-500">统计图表功能开发中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框将在后续添加 -->
    
    <!-- JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/app-v2.js"></script>
    
    <script>
        // 初始化Feather图标
        feather.replace();
    </script>
</body>
</html>
