/**
 * 前端配置文件 v2
 * 包含各种可配置的参数
 */

window.AppConfig = {
    // 日志配置
    logging: {
        // 前端控制台显示的最大日志条数（调试用：10条，生产用：100条）
        maxConsoleLogLines: 10,

        // 是否启用自动滚动
        autoScroll: true,

        // 日志级别过滤
        logLevels: ['info', 'warning', 'error', 'debug'],

        // 内存管理配置
        memoryManagement: {
            // 是否启用自动清理（当日志条数达到最大值时自动删除旧日志）
            autoCleanup: true,

            // 清理批次大小（一次删除多少条旧日志）
            cleanupBatchSize: 5,

            // 强制垃圾回收间隔（毫秒，0表示禁用）
            forceGCInterval: 0
        }
    },
    
    // WebSocket配置
    websocket: {
        // 最大重连尝试次数
        maxReconnectAttempts: 5,
        
        // 重连延迟（毫秒）
        reconnectDelay: 1000
    },
    
    // 轮询配置
    polling: {
        // 运行任务状态轮询间隔（毫秒）
        runningTasksInterval: 5000,
        
        // 任务列表刷新间隔（毫秒）
        taskListRefreshInterval: 30000
    },
    
    // UI配置
    ui: {
        // 任务切换动画时间（毫秒）
        taskSwitchAnimationDuration: 150,
        
        // 模态框关闭方式（'button-only' 或 'click-outside'）
        modalCloseMode: 'button-only',
        
        // 新版本特有配置
        theme: {
            // 主色调
            primary: '#3b82f6',
            // 成功色
            success: '#10b981',
            // 警告色
            warning: '#f59e0b',
            // 错误色
            error: '#ef4444',
            // 侧边栏背景色
            sidebarBg: '#1e293b'
        },
        
        // 动画配置
        animations: {
            // 页面切换动画时长
            pageTransition: 200,
            // 模态框动画时长
            modalTransition: 150,
            // 按钮悬停动画时长
            buttonHover: 200
        }
    },
    
    // API配置
    api: {
        // API基础路径
        baseUrl: '',
        // 请求超时时间（毫秒）
        timeout: 30000,
        // 重试次数
        retryCount: 3
    }
};

// 导出配置供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.AppConfig;
}
