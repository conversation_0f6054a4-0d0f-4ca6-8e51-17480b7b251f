/**
 * KafkaTool v2 主应用逻辑
 * 使用 Alpine.js 进行状态管理
 */

function kafkaToolApp() {
    return {
        // 应用状态
        loading: false,
        error: null,
        
        // 任务相关状态
        tasks: [],
        selectedTask: null,
        taskName: '',
        taskDescription: '',
        cycleDuration: 60,
        totalDuration: 300,
        
        // 队列配置
        queues: [],
        
        // 环境变量
        environments: [],
        selectedEnvironment: null,
        
        // 模板
        templates: [],
        
        // UI状态
        activeTab: 'config',
        showNewTaskModal: false,
        showEnvModal: false,
        showTemplateModal: false,
        
        // 标签页配置
        tabs: [
            { id: 'config', name: '任务配置' },
            { id: 'output', name: '运行输出' },
            { id: 'history', name: '运行历史' },
            { id: 'charts', name: '统计图表' }
        ],
        
        // 运行状态
        isRunning: false,
        runStatus: '就绪',
        messageCount: 0,
        cycleCount: 0,
        errorCount: 0,
        elapsedTime: '00:00:00',
        
        // 初始化
        async init() {
            console.log('KafkaTool v2 初始化中...');
            
            try {
                await this.loadTasks();
                await this.loadEnvironments();
                await this.loadTemplates();
                
                // 初始化Feather图标
                this.$nextTick(() => {
                    feather.replace();
                });
                
                console.log('KafkaTool v2 初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
                this.error = '应用初始化失败: ' + error.message;
            }
        },
        
        // 加载任务列表
        async loadTasks() {
            try {
                const response = await fetch('/task/api/list');
                if (!response.ok) throw new Error('加载任务列表失败');
                
                this.tasks = await response.json();
                
                // 如果有任务且没有选中的任务，选中第一个
                if (this.tasks.length > 0 && !this.selectedTask) {
                    this.selectTask(this.tasks[0]);
                }
            } catch (error) {
                console.error('加载任务列表失败:', error);
                this.error = '加载任务列表失败: ' + error.message;
            }
        },
        
        // 加载环境变量列表
        async loadEnvironments() {
            try {
                const response = await fetch('/env/api/list');
                if (!response.ok) throw new Error('加载环境变量失败');
                
                this.environments = await response.json();
            } catch (error) {
                console.error('加载环境变量失败:', error);
            }
        },
        
        // 加载模板列表
        async loadTemplates() {
            try {
                const response = await fetch('/template/api/list');
                if (!response.ok) throw new Error('加载模板列表失败');
                
                this.templates = await response.json();
            } catch (error) {
                console.error('加载模板列表失败:', error);
            }
        },
        
        // 选择任务
        selectTask(task) {
            this.selectedTask = task;
            this.taskName = task.name || '';
            this.taskDescription = task.description || '';
            this.cycleDuration = task.cycle_duration || 60;
            this.totalDuration = task.total_duration || 300;
            
            // 加载任务的队列配置
            this.loadTaskQueues(task.id);
            
            console.log('选中任务:', task);
        },
        
        // 加载任务的队列配置
        async loadTaskQueues(taskId) {
            try {
                const response = await fetch(`/task/api/get/${taskId}`);
                if (!response.ok) throw new Error('加载任务详情失败');
                
                const taskData = await response.json();
                this.queues = taskData.queues || [];
            } catch (error) {
                console.error('加载任务队列失败:', error);
            }
        },
        
        // 添加队列
        addQueue() {
            const newQueue = {
                id: Date.now(), // 临时ID
                topic: '',
                template_id: null,
                message_count: 10,
                message_interval: 1000
            };
            this.queues.push(newQueue);
        },
        
        // 删除队列
        removeQueue(index) {
            this.queues.splice(index, 1);
        },
        
        // 运行任务
        async runTask() {
            if (!this.selectedTask) {
                alert('请先选择一个任务');
                return;
            }
            
            try {
                this.isRunning = true;
                this.runStatus = '运行中';
                
                const response = await fetch(`/task/api/run/${this.selectedTask.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) throw new Error('启动任务失败');
                
                const result = await response.json();
                console.log('任务启动成功:', result);
                
                // 这里可以添加WebSocket连接来实时获取运行状态
                
            } catch (error) {
                console.error('运行任务失败:', error);
                this.error = '运行任务失败: ' + error.message;
                this.isRunning = false;
                this.runStatus = '错误';
            }
        },
        
        // 编辑任务
        editTask(task) {
            this.selectTask(task);
            // 这里可以打开编辑模态框
            console.log('编辑任务:', task);
        },
        
        // 删除任务
        async deleteTask(task) {
            if (!confirm(`确定要删除任务 "${task.name}" 吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`/task/api/delete/${task.id}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) throw new Error('删除任务失败');
                
                // 从列表中移除
                this.tasks = this.tasks.filter(t => t.id !== task.id);
                
                // 如果删除的是当前选中的任务，清空选择
                if (this.selectedTask?.id === task.id) {
                    this.selectedTask = null;
                    this.taskName = '';
                    this.taskDescription = '';
                }
                
                console.log('任务删除成功');
            } catch (error) {
                console.error('删除任务失败:', error);
                this.error = '删除任务失败: ' + error.message;
            }
        },
        
        // 保存任务
        async saveTask() {
            if (!this.selectedTask) return;
            
            try {
                const taskData = {
                    name: this.taskName,
                    description: this.taskDescription,
                    cycle_duration: this.cycleDuration,
                    total_duration: this.totalDuration,
                    queues: this.queues
                };
                
                const response = await fetch(`/task/api/save/${this.selectedTask.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(taskData)
                });
                
                if (!response.ok) throw new Error('保存任务失败');
                
                console.log('任务保存成功');
                await this.loadTasks(); // 重新加载任务列表
                
            } catch (error) {
                console.error('保存任务失败:', error);
                this.error = '保存任务失败: ' + error.message;
            }
        },
        
        // 格式化时间
        formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    };
}
