import sqlite3

# Connect to the SQLite database
conn = sqlite3.connect('app/data.sqlite')
cursor = conn.cursor()

# Check if the topic column already exists
cursor.execute("PRAGMA table_info(message_templates)")
columns = cursor.fetchall()
column_names = [column[1] for column in columns]

if 'topic' not in column_names:
    print("Adding 'topic' column to message_templates table...")
    # Add the topic column with default value
    cursor.execute("ALTER TABLE message_templates ADD COLUMN topic TEXT DEFAULT 'default-topic'")
    conn.commit()
    print("Column added successfully!")
else:
    print("The 'topic' column already exists in the message_templates table.")

# Close the connection
conn.close()
