# Kafka消息生成与性能测试工具

这是一个基于Web的Kafka消息生成与性能测试工具，可以通过Python代码灵活定义消息模板，并执行性能测试。

## 功能特点

- 环境变量配置管理：创建、编辑和管理环境变量配置集
- 消息模板管理：使用Python代码定义消息模板，支持预览和调试
- 测试任务管理：组合消息模板和环境变量配置，定义测试任务
- 测试执行：执行测试任务，实时监控测试状态
- 测试历史：记录测试运行历史，查看详细结果

## 安装与运行

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行应用

```bash
python run.py
```

应用将在 http://localhost:5000 上运行。

## 使用说明

### 1. 环境变量配置

在"环境变量"页面创建环境变量配置集，定义测试所需的环境变量，如Kafka服务器地址、主题名称等。

### 2. 消息模板

在"消息模板"页面创建消息模板，使用Python代码定义消息生成逻辑。可以使用系统提供的辅助函数生成随机数据。

可用的辅助函数包括：

- `random_int(min, max)`: 生成指定范围内的随机整数
- `random_float(min, max)`: 生成指定范围内的随机浮点数
- `random_string(length)`: 生成指定长度的随机字符串
- `random_ipv4()`: 生成随机IPv4地址
- `random_mac()`: 生成随机MAC地址
- `time()`: 获取当前时间戳(秒)
- 更多函数请参考代码编辑器中的示例

### 3. 测试任务

在"测试任务"页面创建测试任务，选择环境变量配置集和消息模板，设置测试参数。

### 4. 执行测试

在测试任务页面点击"运行"按钮执行测试，可以实时监控测试状态。

### 5. 查看历史

在"运行历史"页面查看历史测试记录，包括测试结果和消息样本。

## SSL证书

如果需要使用SSL连接Kafka，请将证书文件放置在`ssl`目录下：

- CARoot.pem：CA根证书
- certificate.pem：客户端证书
- key.pem：客户端私钥
